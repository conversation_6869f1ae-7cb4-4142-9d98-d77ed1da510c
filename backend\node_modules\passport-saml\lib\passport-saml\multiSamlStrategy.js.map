{"version": 3, "file": "multiSamlStrategy.js", "sourceRoot": "", "sources": ["../../src/passport-saml/multiSamlStrategy.ts"], "names": [], "mappings": ";;;AAAA,4CAAoC;AACpC,yCAA8C;AAW9C,MAAa,iBAAkB,SAAQ,2BAAgB;IAMrD,YAAY,OAAwB,EAAE,MAAa;QACjD,IAAI,CAAC,OAAO,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;YAC5D,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QAED,+DAA+D;QAC/D,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,UAAU,GAAG;YACjB,GAAG,OAAO;SACqB,CAAC;QAElC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;IAC7B,CAAC;IAED,YAAY,CAAC,GAAoB,EAAE,OAA4B;QAC7D,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YACrD,IAAI,GAAG,EAAE;gBACP,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACxB;YAED,MAAM,WAAW,GAAG,IAAI,gBAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACtC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CACJ,GAAoB,EACpB,QAAsE;QAEtE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YACrD,IAAI,GAAG,EAAE;gBACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;YAED,MAAM,WAAW,GAAG,IAAI,gBAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;YAC5E,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACtC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+BAA+B,CAC7B,GAAY,EACZ,cAA6B,EAC7B,WAA0B,EAC1B,QAAwD;QAExD,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;SACpF;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,EAAE;YAC5D,IAAI,GAAG,EAAE;gBACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;aACtB;YAED,MAAM,WAAW,GAAG,IAAI,gBAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;YAC5E,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC,CAAC;YACjE,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACtC,OAAO,QAAQ,CACb,IAAI,EACJ,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,QAAQ,EAAE,cAAc,EAAE,WAAW,CAAC,CAClF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,4CAA4C;IAC5C,KAAK,CAAC,GAAU;QACd,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;;AA/EH,8CAgFC;AA/EiB,4CAA0B,GAAG,KAAK,CAAC", "sourcesContent": ["import { SA<PERSON> } from \"../node-saml\";\nimport { AbstractStrategy } from \"./strategy\";\nimport type { Request } from \"express\";\nimport {\n  AuthenticateOptions,\n  MultiSamlConfig,\n  RequestWithUser,\n  SamlConfig,\n  VerifyWithoutRequest,\n  VerifyWithRequest,\n} from \"./types\";\n\nexport class MultiSamlStrategy extends AbstractStrategy {\n  static readonly newSamlProviderOnConstruct = false;\n  _options: SamlConfig & MultiSamlConfig;\n\n  constructor(options: MultiSamlConfig, verify: VerifyWithRequest);\n  constructor(options: MultiSamlConfig, verify: VerifyWithoutRequest);\n  constructor(options: MultiSamlConfig, verify: never) {\n    if (!options || typeof options.getSamlOptions !== \"function\") {\n      throw new Error(\"Please provide a getSamlOptions function\");\n    }\n\n    // Force the type on this since we've disabled `newOnConstruct`\n    // so the `SAML` constructor will not be called at this time\n    // and there are defaults for all `strategy`-required options.\n    const samlConfig = {\n      ...options,\n    } as SamlConfig & MultiSamlConfig;\n\n    super(samlConfig, verify);\n    this._options = samlConfig;\n  }\n\n  authenticate(req: RequestWithUser, options: AuthenticateOptions): void {\n    this._options.getSamlOptions(req, (err, samlOptions) => {\n      if (err) {\n        return this.error(err);\n      }\n\n      const samlService = new SAML({ ...this._options, ...samlOptions });\n      const strategy = Object.assign({}, this, { _saml: samlService });\n      Object.setPrototypeOf(strategy, this);\n      super.authenticate.call(strategy, req, options);\n    });\n  }\n\n  logout(\n    req: RequestWithUser,\n    callback: (err: Error | null, url?: string | null | undefined) => void\n  ) {\n    this._options.getSamlOptions(req, (err, samlOptions) => {\n      if (err) {\n        return callback(err);\n      }\n\n      const samlService = new SAML(Object.assign({}, this._options, samlOptions));\n      const strategy = Object.assign({}, this, { _saml: samlService });\n      Object.setPrototypeOf(strategy, this);\n      super.logout.call(strategy, req, callback);\n    });\n  }\n\n  generateServiceProviderMetadata(\n    req: Request,\n    decryptionCert: string | null,\n    signingCert: string | null,\n    callback: (err: Error | null, metadata?: string) => void\n  ) {\n    if (typeof callback !== \"function\") {\n      throw new Error(\"Metadata can't be provided synchronously for MultiSamlStrategy.\");\n    }\n\n    return this._options.getSamlOptions(req, (err, samlOptions) => {\n      if (err) {\n        return callback(err);\n      }\n\n      const samlService = new SAML(Object.assign({}, this._options, samlOptions));\n      const strategy = Object.assign({}, this, { _saml: samlService });\n      Object.setPrototypeOf(strategy, this);\n      return callback(\n        null,\n        this._generateServiceProviderMetadata.call(strategy, decryptionCert, signingCert)\n      );\n    });\n  }\n\n  // This is reduntant, but helps with testing\n  error(err: Error): void {\n    super.error(err);\n  }\n}\n"]}