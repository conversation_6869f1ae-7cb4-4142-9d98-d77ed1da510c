{"name": "passport-microsoft", "version": "2.1.0", "description": "Microsoft [Graph] authentication strategy for Passport.", "keywords": ["passport", "microsoft", "graph", "graph.microsoft.io", "o<PERSON>h", "oauth 2.0", "auth", "api", "authentication", "sean fisher", "seafish.io"], "repository": {"type": "git", "url": "git://github.com/seanfisher/passport-microsoft.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://seafish.io"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "main": "./lib/", "dependencies": {"passport-oauth2": "1.8.0"}, "engines": {"node": ">= 0.4.0"}, "devDependencies": {"chai": "^4.4.1", "chai-passport-strategy": "^3.0.0", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "mocha": "^10.3.0"}, "scripts": {"test": "mocha --require ./test/bootstrap/node.js"}}