/**
 * Core Security Domain Exports
 *
 * Central export point for all security domain components including
 * entities, value objects, enums, events, interfaces, and specifications.
 */

// Enums
export * from './enums/event-processing-status.enum';
export * from './enums/event-source-type.enum';
export * from './enums/threat-severity.enum';
export * from './enums/confidence-level.enum';

// Value Objects - Event Metadata
export * from './value-objects/event-metadata/event-metadata.value-object';
export * from './value-objects/event-metadata/event-source.value-object';
export * from './value-objects/event-metadata/event-timestamp.value-object';

// Value Objects - Network
export * from './value-objects/network/ip-address.value-object';
export * from './value-objects/network/port.value-object';

// Value Objects - Threat Indicators
export * from './value-objects/threat-indicators/ioc.value-object';
export * from './value-objects/threat-indicators/cvss-score.value-object';
export * from './value-objects/threat-indicators/threat-signature.value-object';
export * from './value-objects/threat-indicators/attack-pattern.value-object';

// Value Objects - Compliance
export * from './value-objects/compliance/compliance-requirement.value-object';

// Entities - Events
export * from './entities/event/security-event.entity';
export * from './entities/event/normalized-event.entity';
export * from './entities/event/enriched-event.entity';
export * from './entities/event/correlated-event.entity';

// Entities - Threats
export * from './entities/threat/threat.entity';

// Entities - Vulnerabilities
export * from './entities/vulnerability/vulnerability.entity';

// Entities - Incidents
export * from './entities/incident/incident.entity';

// Domain Events - Events
export * from './events/security-event-created.event';
export * from './events/security-event-status-changed.event';
export * from './events/event-normalized.event';
export * from './events/event-enriched.event';
export * from './events/events-correlated.event';

// Domain Events - Threats
export * from './events/threat-detected.event';
export * from './events/threat-severity-changed.event';

// Domain Events - Vulnerabilities
export * from './events/vulnerability-discovered.event';

// Domain Events - Incidents
export * from './events/incident-created.event';
export * from './events/incident-status-changed.event';
export * from './events/incident-escalated.event';

// Repository Interfaces
export * from './interfaces/repositories/security-event.repository.interface';
export * from './interfaces/repositories/vulnerability.repository.interface';
export * from './interfaces/repositories/incident.repository.interface';
export * from './interfaces/repositories/correlation.repository.interface';

// Service Interfaces
export * from './interfaces/services/threat-assessment.service.interface';
export * from './interfaces/services/security-orchestrator.service.interface';

// Specifications
export * from './specifications/high-risk-event.specification';
export * from './specifications/critical-vulnerability.specification';
export * from './specifications/incident-escalation.specification';
export * from './specifications/vulnerability-prioritization.specification';

// Re-export shared kernel components commonly used in security domain
export {
  BaseAggregateRoot,
  BaseDomainEvent,
  BaseValueObject,
  BaseRepository,
  BaseService,
} from '../../shared-kernel/domain';

export {
  UniqueEntityId,
  TenantId,
  UserId,
  Timestamp,
  CorrelationId,
  Version,
} from '../../shared-kernel/value-objects';

export {
  DomainException,
  ValidationException,
  NotFoundException,
} from '../../shared-kernel/exceptions';

export {
  PaginatedResult,
  QueryOptions,
  FilterParams,
  DateRangeFilter,
} from '../../shared-kernel/types';
