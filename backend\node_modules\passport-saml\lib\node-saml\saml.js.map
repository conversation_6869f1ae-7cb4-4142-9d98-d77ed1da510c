{"version": 3, "file": "saml.js", "sourceRoot": "", "sources": ["../../src/node-saml/saml.ts"], "names": [], "mappings": ";;;AAAA,iCAA0B;AAC1B,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,WAAW,CAAC,CAAC;AACjC,6BAA6B;AAC7B,iCAAiC;AACjC,6BAA0B;AAC1B,2CAA2C;AAC3C,6BAA6B;AAC7B,uEAAmF;AACnF,2CAA2C;AAC3C,2DAA2D;AAE3D,mCAaiB;AACjB,kDAMgC;AAChC,uCAA2C;AAC3C,+BAQe;AAEf,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAOxD,KAAK,UAAU,oCAAoC,CACjD,IAAU,EACV,GAAc,EACd,GAAa;IAEb,MAAM,OAAO,GAAG,GAAG,CAAC,aAAa,CAAC;IAClC,IAAI,OAAO,EAAE;QACX,MAAM,OAAO,GAAG,EAAa,CAAC;QAC9B,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;SAC3B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACxC;QACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE;YACV,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAM,CAAC;YAC/B,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;aACtC;SACF;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACxC;QACD,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC1C,IAAI,YAAY,EAAE;YAChB,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QACD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;KACrC;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;KACjD;AACH,CAAC;AAED,KAAK,UAAU,mCAAmC,CAChD,IAAU,EACV,GAAc,EACd,GAAa;IAEb,MAAM,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC;IACpC,MAAM,OAAO,GAAG,GAAG,CAAC,aAAa,CAAC;IAElC,IAAI,QAAQ,EAAE;QACZ,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;KAC3C;SAAM,IAAI,OAAO,EAAE;QAClB,OAAO,MAAM,oCAAoC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;KACnE;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;KAClD;AACH,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,MAAY;IAC3C,MAAM,MAAM,GAAG,WAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACzD,OAAO;QACL,KAAK,EAAE,MAAM,CAAC,WAAW;QACzB,MAAM,EAAE,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;KACnD,CAAC;AACJ,CAAC;AAED,MAAM,IAAI;IAOR,YAAY,WAAuB;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IAClD,CAAC;IAED,UAAU,CAAC,WAAuB;;QAChC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;SAC7D;QAED,MAAM,OAAO,GAAG;YACd,GAAG,WAAW;YACd,OAAO,EAAE,MAAA,WAAW,CAAC,OAAO,mCAAI,KAAK;YACrC,4BAA4B,EAAE,MAAA,WAAW,CAAC,4BAA4B,mCAAI,KAAK;YAC/E,gBAAgB,EAAE,MAAA,WAAW,CAAC,gBAAgB,mCAAI,EAAE;YACpD,yBAAyB,EAAE,MAAA,WAAW,CAAC,yBAAyB,mCAAI,EAAE;YACtE,sBAAsB,EAAE,MAAA,WAAW,CAAC,sBAAsB,mCAAI,EAAE;YAChE,UAAU,EAAE,MAAA,WAAW,CAAC,UAAU,mCAAI,KAAK;YAC3C,sBAAsB,EAAE,MAAA,WAAW,CAAC,sBAAsB,mCAAI,KAAK;YACnE,oBAAoB,EAAE,MAAA,WAAW,CAAC,oBAAoB,mCAAI,KAAK;YAC/D,mBAAmB,EAAE,MAAA,WAAW,CAAC,mBAAmB,mCAAI,CAAC;YACzD,iBAAiB,EAAE,MAAA,WAAW,CAAC,iBAAiB,mCAAI,CAAC;YACrD,IAAI,EAAE,MAAA,WAAW,CAAC,IAAI,mCAAI,eAAe;YACzC,IAAI,EAAE,MAAA,WAAW,CAAC,IAAI,mCAAI,WAAW;YACrC,MAAM,EAAE,MAAA,WAAW,CAAC,MAAM,mCAAI,eAAe;YAC7C,gBAAgB,EACd,WAAW,CAAC,gBAAgB,KAAK,SAAS;gBACxC,CAAC,CAAC,wDAAwD;gBAC1D,CAAC,CAAC,WAAW,CAAC,gBAAgB;YAClC,oBAAoB,EAAE,MAAA,WAAW,CAAC,oBAAoB,mCAAI,KAAK;YAC/D,YAAY,EAAE,MAAA,WAAW,CAAC,YAAY,mCAAI;gBACxC,mEAAmE;aACpE;YACD,oBAAoB,EAAE,MAAA,WAAW,CAAC,oBAAoB,mCAAI,KAAK;YAC/D,IAAI,EAAE,IAAA,wBAAc,EAAC,WAAW,CAAC,IAAI,EAAE,kBAAkB,CAAC;YAC1D,2BAA2B,EAAE,MAAA,WAAW,CAAC,2BAA2B,mCAAI,QAAQ;YAChF,aAAa,EACX,MAAA,WAAW,CAAC,aAAa,mCACzB,IAAI,uCAAqB,CAAC;gBACxB,qBAAqB,EAAE,WAAW,CAAC,2BAA2B;aAC/D,CAAC;YACJ,SAAS,EAAE,MAAA,MAAA,WAAW,CAAC,SAAS,mCAAI,WAAW,CAAC,UAAU,mCAAI,EAAE;YAChE,kBAAkB,EAAE,MAAA,WAAW,CAAC,kBAAkB,mCAAI,MAAM;YAC5D,mBAAmB,EAAE,MAAA,WAAW,CAAC,mBAAmB,mCAAI,eAAe;YAEvE,aAAa,EAAE,MAAA,WAAW,CAAC,aAAa,mCAAI,OAAO;SACpD,CAAC;QAEF;;;;;;WAMG;QACH,IAAI,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;YAC9E,MAAM,IAAI,SAAS,CAAC,wEAAwE,CAAC,CAAC;SAC/F;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,cAAc,CAAC,IAAyB;QAC9C,wBAAwB;QACxB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;SACjC;aAAM;YACL,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,kBAAkB,CAAC,CAAC;YACxC,IAAI,IAAI,EAAE;gBACR,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;aACjB;iBAAM;gBACL,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;aAC9B;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACzB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;aACtC;YACD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACjC,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;SACvB;IACH,CAAC;IAED,iBAAiB;QACf,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAEO,eAAe;QACrB,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAEO,WAAW,CAAC,WAA4C;QAC9D,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAA,wBAAc,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QAE5F,MAAM,iBAAiB,GAAoC,EAAE,CAAC;QAC9D,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACrF,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACrE,IAAI,WAAW,CAAC,WAAW,EAAE;YAC3B,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;SACzD;QACD,IAAI,WAAW,CAAC,YAAY,EAAE;YAC5B,iBAAiB,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;SAC3D;QACD,IAAI,WAAW,CAAC,UAAU,EAAE;YAC1B,iBAAiB,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;SACvD;QACD,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,iBAAiB,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;SAC/C;QACD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACxD,WAAW,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,CAAC;IACzF,CAAC;IAEO,KAAK,CAAC,6BAA6B,CACzC,SAAkB,EAClB,iBAA0B,EAC1B,IAAwB;QAExB,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAA,wBAAc,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QAE5F,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACrC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;SACjD;QACD,MAAM,OAAO,GAAwB;YACnC,oBAAoB,EAAE;gBACpB,cAAc,EAAE,sCAAsC;gBACtD,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,KAAK;gBACjB,eAAe,EAAE,OAAO;gBACxB,kBAAkB,EAAE,gDAAgD;gBACpE,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;gBACvC,aAAa,EAAE;oBACb,aAAa,EAAE,uCAAuC;oBACtD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;iBAC7B;aACF;SACF,CAAC;QAEF,IAAI,SAAS;YAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;QAElE,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;YAC3B,OAAO,CAAC,oBAAoB,CAAC,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;SACrD;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACtC,OAAO,CAAC,oBAAoB,CAAC,CAAC,8BAA8B,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;SAC3F;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,EAAE;YACzC,OAAO,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CAAC,GAAG;gBACpD,cAAc,EAAE,sCAAsC;gBACtD,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;gBACxC,cAAc,EAAE,MAAM;aACvB,CAAC;SACH;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE;YAC9C,MAAM,qBAAqB,GAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,YAAyB,CAAC,OAAO,CAAC,UAAU,KAAK;gBAC7D,qBAAqB,CAAC,IAAI,CAAC;oBACzB,aAAa,EAAE,uCAAuC;oBACtD,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,oBAAoB,CAAC,CAAC,6BAA6B,CAAC,GAAG;gBAC7D,cAAc,EAAE,sCAAsC;gBACtD,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;gBACzC,2BAA2B,EAAE,qBAAqB;aACnD,CAAC;SACH;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,8BAA8B,IAAI,IAAI,EAAE;YACvD,OAAO,CAAC,oBAAoB,CAAC,CAAC,iCAAiC,CAAC;gBAC9D,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC;SAC/C;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE;YACrC,OAAO,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SAC5E;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE;YAChC,MAAM,OAAO,GAAa;gBACxB,cAAc,EAAE,sCAAsC;aACvD,CAAC;YAEF,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;gBACvD,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;aAC1D;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE;gBAChC,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CACzD,CAAC,WAA8B,EAAE,EAAE;oBACjC,MAAM,oBAAoB,GAAa;wBACrC,cAAc,EAAE,sCAAsC;qBACvD,CAAC;oBAEF,IAAI,WAAW,CAAC,OAAO,EAAE;wBACvB,oBAAoB,CAAC,gBAAgB,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAC9D,CAAC,KAAyB,EAAE,EAAE;4BAC5B,MAAM,cAAc,GAAa;gCAC/B,cAAc,EAAE,sCAAsC;6BACvD,CAAC;4BAEF,cAAc,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC;4BAEjD,IAAI,KAAK,CAAC,IAAI,EAAE;gCACd,cAAc,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;6BACtC;4BAED,IAAI,KAAK,CAAC,GAAG,EAAE;gCACb,cAAc,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;6BACpC;4BAED,OAAO,cAAc,CAAC;wBACxB,CAAC,CACF,CAAC;qBACH;oBAED,IAAI,WAAW,CAAC,WAAW,EAAE;wBAC3B,oBAAoB,CAAC,mBAAmB,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC;qBACrE;oBAED,OAAO,oBAAoB,CAAC;gBAC9B,CAAC,CACF,CAAC;aACH;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE;gBACpC,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;aACjE;YAED,OAAO,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,GAAG,OAAO,CAAC;SAC1D;QAED,IAAI,aAAa,GAAG,IAAA,2BAAqB,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1D,yCAAyC;QACzC,IAAI,iBAAiB,IAAI,IAAA,iCAAyB,EAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAChE,aAAa,GAAG,IAAA,wCAAoB,EAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACnE;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,IAAa;QACxC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvC,MAAM,OAAO,GAAG;YACd,qBAAqB,EAAE;gBACrB,cAAc,EAAE,sCAAsC;gBACtD,aAAa,EAAE,uCAAuC;gBACtD,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,KAAK;gBACjB,eAAe,EAAE,OAAO;gBACxB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACtC,aAAa,EAAE;oBACb,aAAa,EAAE,uCAAuC;oBACtD,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;iBAC7B;gBACD,aAAa,EAAE;oBACb,SAAS,EAAE,IAAK,CAAC,YAAY;oBAC7B,OAAO,EAAE,IAAK,CAAC,MAAM;iBACtB;aACF;SACkB,CAAC;QAEtB,IAAI,IAAK,CAAC,aAAa,IAAI,IAAI,EAAE;YAC/B,OAAO,CAAC,qBAAqB,CAAC,CAAC,aAAa,CAAC,CAAC,gBAAgB,CAAC,GAAG,IAAK,CAAC,aAAa,CAAC;SACvF;QAED,IAAI,IAAK,CAAC,eAAe,IAAI,IAAI,EAAE;YACjC,OAAO,CAAC,qBAAqB,CAAC,CAAC,aAAa,CAAC,CAAC,kBAAkB,CAAC,GAAG,IAAK,CAAC,eAAe,CAAC;SAC3F;QAED,IAAI,IAAK,CAAC,YAAY,EAAE;YACtB,OAAO,CAAC,qBAAqB,CAAC,CAAC,qBAAqB,CAAC,GAAG;gBACtD,eAAe,EAAE,sCAAsC;gBACvD,OAAO,EAAE,IAAK,CAAC,YAAY;aAC5B,CAAC;SACH;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAChD,OAAO,IAAA,2BAAqB,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,uBAAuB,CAAC,aAAsB;QAC5C,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvC,MAAM,OAAO,GAAG;YACd,sBAAsB,EAAE;gBACtB,cAAc,EAAE,sCAAsC;gBACtD,aAAa,EAAE,uCAAuC;gBACtD,KAAK,EAAE,EAAE;gBACT,UAAU,EAAE,KAAK;gBACjB,eAAe,EAAE,OAAO;gBACxB,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;gBACtC,eAAe,EAAE,aAAa,CAAC,EAAE;gBACjC,aAAa,EAAE;oBACb,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;iBAC7B;gBACD,cAAc,EAAE;oBACd,kBAAkB,EAAE;wBAClB,QAAQ,EAAE,4CAA4C;qBACvD;iBACF;aACF;SACF,CAAC;QAEF,OAAO,IAAA,2BAAqB,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,OAAkC,EAClC,QAAuB,EACvB,SAAiB,EACjB,oBAAgD;QAEhD,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAA,wBAAc,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QAE5F,IAAI,MAAc,CAAC;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACvC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,QAAQ,CAAE,EAAE,MAAM,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,GAAG,MAAM,eAAe,CAAC,CAAC,OAAO,IAAI,QAAQ,CAAE,CAAC,CAAC;SACxD;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,MAAM,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE9C,IAAI,SAAS,KAAK,QAAQ,EAAE;YAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;gBAC1B,MAAM,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;aAC1C;SACF;aAAM,IAAI,SAAS,KAAK,WAAW,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;SACpD;QAED,MAAM,WAAW,GAA+B,OAAO;YACrD,CAAC,CAAC;gBACE,WAAW,EAAE,MAAM;aACpB;YACH,CAAC,CAAC;gBACE,YAAY,EAAE,MAAM;aACrB,CAAC;QACN,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9C,WAAW,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAC5B,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;aAClF;YAED,8BAA8B;YAC9B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAC/B;QACD,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACrC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAW,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,oBAAoB,CAClB,UAAkB,EAClB,SAAiB,EACjB,cAA2C;QAE3C,MAAM,gBAAgB,GAA+B,EAAE,CAAC;QAExD,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,gBAAgB,CAAC,UAAU,GAAG,UAAU,CAAC;SAC1C;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAC9D,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,gBAAgB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAI,uCAAuC,GAA2B,EAAE,CAAC;QACzE,IAAI,SAAS,IAAI,WAAW,EAAE;YAC5B,uCAAuC,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC;SAClF;QACD,IAAI,SAAS,IAAI,QAAQ,EAAE;YACzB,uCAAuC,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC;SAC/E;QAED,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;YACtE,gBAAgB,CAAC,CAAC,CAAC,GAAG,uCAAuC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,cAAc,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,EAAE,CAAC;QACtC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;YAC7C,gBAAgB,CAAC,CAAC,CAAC,GAAG,cAAe,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,UAAkB,EAClB,IAAwB,EACxB,OAAyB;QAEzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,WAAW,CAAC;QAC9B,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAClC,OAAO,EACP,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,CACjE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,UAAkB,EAAE,IAAa;QAC3D,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAA,wBAAc,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC;QAE5F,sGAAsG;QACtG,qGAAqG;QACrG,2BAA2B;QAC3B,MAAM,SAAS,GAAG,UAChB,CAQsB,EACtB,UAAoB;YAEpB,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;YACnD,OAAO,CACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,mCAAmC;iBACzC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,oCAAoC;iBAC3D,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,6CAA6C;iBACrE,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;iBACvB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;iBACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;gBACtB,4CAA4C;gBAC5C,iEAAiE;iBAChE,OAAO,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,uCAAuC;iBACxE,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CACtC,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,MAAc,CAAC;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACvC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAQ,EAAE,MAAM,CAAC,CAAC;SACxC;aAAM;YACL,MAAM,GAAG,MAAM,eAAe,CAAC,OAAQ,CAAC,CAAC;SAC1C;QAED,MAAM,SAAS,GAAG,WAAW,CAAC;QAC9B,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC9E,MAAM,WAAW,GAAoC;YACnD,WAAW,EAAE,MAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;SACxC,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9C,WAAW,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;aACxC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,OAAO,6BAA6B,GAAG,CAAC,GAAG,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAC9F,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhB,OAAO;YACL,iBAAiB;YACjB,QAAQ;YACR,QAAQ;YACR,wBAAwB;YACxB,uDAAuD;YACvD,SAAS;YACT,4CAA4C;YAC5C,YAAY;YACZ,gIAAgI;YAChI,aAAa;YACb,8BAA8B,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI;YAC1E,UAAU;YACV,wCAAwC;YACxC,SAAS;YACT,0DAA0D;YAC1D,SAAS;YACT,SAAS;SACV,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,IAAa,EACb,UAAkB,EAClB,OAA+C;QAE/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,QAAQ,CAAC;QAC3B,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAClC,OAAO,EACP,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,CACjE,CAAC;IACJ,CAAC;IAED,oBAAoB,CAClB,iBAA0B,EAC1B,UAAkB,EAClB,OAA+C,EAC/C,QAA0D;QAE1D,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAC5F,QAAQ,CACT,CAAC;IACJ,CAAC;IACO,KAAK,CAAC,yBAAyB,CACrC,iBAA0B,EAC1B,UAAkB,EAClB,OAA+C,CAAC,kBAAkB;;QAElE,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QACjE,MAAM,SAAS,GAAG,QAAQ,CAAC;QAC3B,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAClC,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC,CACjE,CAAC;IACJ,CAAC;IAED,UAAU,CAAC,IAAY;QACrB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAAE,IAAI,GAAG,+BAA+B,GAAG,IAAI,CAAC;QAC9F,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAAE,IAAI,GAAG,IAAI,GAAG,+BAA+B,CAAC;QAE5F,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,IAAI,YAAsB,CAAC;QAE3B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE;YAC3C,YAAY,GAAG,MAAM,IAAI;iBACtB,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAoB,CAAC,EAAE;iBAC9C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACd,KAAK,GAAG,IAAA,wBAAc,EAAC,KAAK,EAAE,6BAA6B,CAAC,CAAC;gBAC7D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACzB,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;iBACjB;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;SACN;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC3C,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;SAClC;aAAM;YACL,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACpC;QAED,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAA,wBAAc,EAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,iGAAiG;IACjG,oCAAoC;IACpC,EAAE;IACF,6FAA6F;IAC7F,iDAAiD;IACjD,iBAAiB,CAAC,OAAe,EAAE,WAAoB,EAAE,KAAe;QACtE,MAAM,aAAa,GACjB,OAAO;YACP,gCAAgC;YAChC,4DAA4D;YAC5D,qDAAqD;YACrD,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC;YAC9B,IAAI;YACJ,GAAG,CAAC;QACN,MAAM,UAAU,GAAG,WAAK,CAAC,cAAc,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QACpE,4FAA4F;QAC5F,uBAAuB;QACvB,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QACD,MAAM,mBAAmB,GACvB,OAAO;YACP,gCAAgC;YAChC,4DAA4D;YAC5D,mDAAmD;YACnD,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC;YAC9B,IAAI;YACJ,GAAG,CAAC;QACN,MAAM,UAAU,GAAG,WAAK,CAAC,cAAc,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;QAC1E,iDAAiD;QACjD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,oFAAoF;YACpF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;YAChC,OAAO,IAAA,iCAA2B,EAChC,SAAS,EACT,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAC5B,OAAO,EACP,WAAW,CACZ,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,SAAiC;QAEjC,IAAI,GAAW,EAAE,GAAa,EAAE,YAA2B,CAAC;QAC5D,IAAI;YACF,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YACrE,GAAG,GAAG,IAAA,wBAAkB,EAAC,GAAG,CAAC,CAAC;YAE9B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,iBAAiB,CAAC;gBAC/D,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAElE,MAAM,iBAAiB,GAAG,WAAK,CAAC,gBAAgB,CAC9C,GAAG,EACH,2CAA2C,CAC5C,CAAC;YAEF,IAAI,iBAAiB,EAAE;gBACrB,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEhF,MAAM,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;aAC/C;YACD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACxC,kGAAkG;YAClG,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IACE,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;gBACvD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,UAAiC,CAAC,CAAC,MAAM,CACtD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,UAAU,IAAI,IAAI,CACjD,CAAC,MAAM,KAAK,CAAC,EACd;gBACA,cAAc,GAAG,IAAI,CAAC;aACvB;YAED,MAAM,UAAU,GAAG,WAAK,CAAC,cAAc,CACrC,GAAG,EACH,yDAAyD,CAC1D,CAAC;YACF,MAAM,mBAAmB,GAAG,WAAK,CAAC,cAAc,CAC9C,GAAG,EACH,kEAAkE,CACnE,CAAC;YAEF,IAAI,UAAU,CAAC,MAAM,GAAG,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtD,8FAA8F;gBAC9F,4FAA4F;gBAC5F,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;aAC3D;YAED,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;gBAC1B,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC;oBACtD,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAClD;oBACA,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;iBACtC;gBACD,OAAO,MAAM,IAAI,CAAC,kCAAkC,CAClD,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACxB,GAAG,EACH,YAAa,CACd,CAAC;aACH;YAED,IAAI,mBAAmB,CAAC,MAAM,IAAI,CAAC,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAA,wBAAc,EACzC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,+CAA+C,CAChD,CAAC;gBAEF,MAAM,qBAAqB,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAEhE,MAAM,YAAY,GAAG,MAAM,IAAA,gBAAU,EAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;gBACzF,MAAM,YAAY,GAAG,IAAA,wBAAkB,EAAC,YAAY,CAAC,CAAC;gBACtD,MAAM,mBAAmB,GAAG,WAAK,CAAC,cAAc,CAC9C,YAAY,EACZ,8BAA8B,CAC/B,CAAC;gBACF,IAAI,mBAAmB,CAAC,MAAM,IAAI,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;gBAE3F,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,CAAC,cAAc,CAAC;oBACtD,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EACpE;oBACA,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;iBAC/D;gBAED,OAAO,MAAM,IAAI,CAAC,kCAAkC,CAClD,mBAAmB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EACjC,GAAG,EACH,YAAa,CACd,CAAC;aACH;YAED,iFAAiF;YACjF,yBAAyB;YAEzB,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAqB,EAAC,GAAG,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACnC,IAAI,QAAQ,EAAE;gBACZ,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;gBACrC,IAAI,CAAC,SAAS,EAAE;oBACd,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;oBAC/B,IAAI,MAAM,EAAE;wBACV,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;wBACxC,IACE,UAAU;4BACV,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,8CAA8C,EACxE;4BACA,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;4BAClD,IACE,gBAAgB;gCAChB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,8CAA8C,EAC9E;gCACA,IAAI,CAAC,cAAc,EAAE;oCACnB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;iCACjD;gCACD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;6BAC5C;yBACF;wBAED,oFAAoF;wBACpF,gFAAgF;wBAChF,gEAAgE;wBAChE,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;4BACvC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;4BACzD,IAAI,OAAO,IAAI,SAAS,EAAE;gCACxB,IAAI,GAAG,GAAG,aAAa,CAAC;gCACxB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE;oCAC3B,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iCACpC;qCAAM,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;oCACnC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iCAC9D;gCACD,MAAM,SAAS,GAAG,IAAA,uBAAiB,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gCACzD,MAAM,IAAI,0BAAkB,CAC1B,yBAAyB,GAAG,OAAO,GAAG,UAAU,GAAG,GAAG,EACtD,SAAS,CACV,CAAC;6BACH;yBACF;qBACF;iBACF;gBACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,CAAC,cAAc,EAAE;oBACnB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;iBACzD;gBACD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;gBAC/C,IAAI,cAAc,EAAE;oBAClB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;iBAC3C;qBAAM;oBACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;iBAClD;aACF;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,KAAK,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBACrC,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAa,CAAC,CAAC;aACrD;YACD,MAAM,GAAG,CAAC;SACX;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,YAA2B;QAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACrC,IAAI,YAAY,EAAE;gBAChB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBAC/D,IAAI,CAAC,MAAM;oBAAE,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC1D,OAAO;aACR;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;aAC1D;SACF;aAAM;YACL,OAAO;SACR;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,SAAmB,EACnB,aAA4B;QAE5B,MAAM,eAAe,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC;QAE/E,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAW,EAAE,QAAQ,CAAC,CAAC;QACzE,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,CAAC;QAE7C,MAAM,GAAG,GAAG,IAAA,wBAAkB,EAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACpD,MAAM,GAAG,GAAc,MAAM,IAAA,2BAAqB,EAAC,QAAQ,CAAC,CAAC;QAC7D,eAAe,KAAK,cAAc;YAChC,CAAC,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClE,OAAO,MAAM,mCAAmC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,4BAA4B,CACxC,SAAmB,EACnB,aAA4B;QAE5B,MAAM,MAAM,GAAG,aAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE;YAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;gBACjC,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF,IAAI,SAAS,CAAC,SAAS,EAAE;YACvB,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC;YAEpE,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE;gBAC1B,SAAS,IAAI,GAAG,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;aAC3C;YAED,SAAS,IAAI,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEtC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACxC,MAAM,sBAAsB,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACjD,OAAO,IAAI,CAAC,4BAA4B,CACtC,SAAS,EACT,SAAS,CAAC,SAAmB,EAC7B,SAAS,CAAC,MAAgB,EAC1B,IAAI,CACL,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,sBAAsB,EAAE;gBAC3B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,4BAA4B,CAClC,SAA4B,EAC5B,SAAiB,EACjB,GAAW,EACX,IAAY;QAEZ,oFAAoF;QACpF,SAAS,QAAQ,CAAC,OAAe;YAC/B,gDAAgD;YAChD,wFAAwF;YACxF,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAC/D,OAAO,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC;QAC9C,CAAC;QACD,MAAM,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,YAAY,CAAC;QACjB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACV,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC;SAC5C;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QACnD,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAE3B,OAAO,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC;IAEO,mBAAmB,CAAC,GAAc;QACxC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACrC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,UAAU,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,4BAA4B,CAC9C,KAAK,EACL,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,YAAY,CACxB,CAAC;QACF,IAAI,MAAM,EAAE;YACV,MAAM,MAAM,CAAC;SACd;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,GAAc;QAC/C,MAAM,UAAU,GAAG,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACtE,IAAI,UAAU,KAAK,4CAA4C;YAC7D,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,UAAU,CAAC,CAAC;QAEpD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACtC,MAAM,YAAY,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC;QACvD,IAAI,YAAY,EAAE;YAChB,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,YAAY,CAAC,WAAsB;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE;YAClC,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YAClC,IAAI,MAAM,EAAE;gBACV,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;oBACxC,MAAM,IAAI,KAAK,CACb,iCAAiC,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CACzF,CAAC;aACL;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;aACxC;SACF;IACH,CAAC;IAEO,KAAK,CAAC,kCAAkC,CAC9C,GAAW,EACX,eAAuB,EACvB,YAAoB;QAEpB,IAAI,GAAG,CAAC;QACR,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,EAAa,CAAC;QAC9B,MAAM,GAAG,GAAc,MAAM,IAAA,2BAAqB,EAAC,GAAG,CAAC,CAAC;QACxD,MAAM,eAAe,GAAc,GAAG,CAAC;QACvC,MAAM,SAAS,GAAc,GAAG,CAAC,SAAS,CAAC;QAC3C,eAAe,EAAE;YACf,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;YAChC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;YAED,IAAI,YAAY,EAAE;gBAChB,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;aACrC;YAED,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;YAChD,IAAI,cAAc,EAAE;gBAClB,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE;oBAC3D,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;iBACzD;aACF;YAED,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAClC,IAAI,mBAAmB,EAAE,WAAW,CAAC;YACrC,IAAI,OAAO,EAAE;gBACX,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACjC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACzB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAE7B,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;wBACrC,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;wBAC1C,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;wBAClD,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC;qBACvD;iBACF;gBAED,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB;oBAClD,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC;oBACnC,CAAC,CAAC,IAAI,CAAC;gBACT,WAAW;oBACT,mBAAmB,IAAI,mBAAmB,CAAC,uBAAuB;wBAChE,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC,CAAC;wBAChD,CAAC,CAAC,IAAI,CAAC;gBACX,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/E,GAAG,GAAG,mEAAmE,CAAC;oBAC1E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;iBACtB;gBAED,IAAI,mBAAmB,EAAE;oBACvB,IAAI,WAAW,IAAI,WAAW,CAAC,CAAC,EAAE;wBAChC,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;wBACjD,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;wBACvD,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CACpD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,mBAAmB,EACnB,SAAS,CAAC,CAAC,CAAC,YAAY,CACzB,CAAC;wBAEF,MAAM,OAAO,GAAG,IAAI,CAAC,4BAA4B,CAC/C,KAAK,EACL,gBAAgB,EAChB,mBAAmB,EACnB,cAAc,CACf,CAAC;wBACF,IAAI,OAAO,EAAE;4BACX,MAAM,OAAO,CAAC;yBACf;qBACF;iBACF;aACF;YAED,iFAAiF;YACjF,mDAAmD;YACnD,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBACrC,IAAI,mBAAmB,EAAE;oBACvB,IAAI,WAAW,IAAI,WAAW,CAAC,CAAC,EAAE;wBAChC,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC;wBACvD,IAAI,YAAY,IAAI,mBAAmB,IAAI,mBAAmB,IAAI,YAAY,EAAE;4BAC9E,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;4BACnD,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;yBAC9C;6BAAM,IAAI,mBAAmB,EAAE;4BAC9B,IAAI,sBAAsB,GAAG,KAAK,CAAC;4BACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;4BACtE,IAAI,MAAM,EAAE;gCACV,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;gCACnC,IAAI,KAAK,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B;oCACxE,sBAAsB,GAAG,IAAI,CAAC;6BACjC;4BACD,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;4BACnD,IAAI,CAAC,sBAAsB,EAAE;gCAC3B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;6BAC9C;4BACD,MAAM,eAAe,CAAC;yBACvB;qBACF;iBACF;qBAAM;oBACL,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;oBACnD,MAAM,eAAe,CAAC;iBACvB;aACF;iBAAM;gBACL,MAAM,eAAe,CAAC;aACvB;SACF;QACD,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,IAAI,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,GAAG,GAAG,yDAAyD,CAAC;YAChE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,EAAE;YAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CACpD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAC9B,UAAU,CAAC,CAAC,CAAC,YAAY,EACzB,SAAS,CAAC,CAAC,CAAC,YAAY,CACzB,CAAC;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,4BAA4B,CAC9C,KAAK,EACL,UAAU,CAAC,CAAC,CAAC,SAAS,EACtB,UAAU,CAAC,CAAC,CAAC,YAAY,EACzB,cAAc,CACf,CAAC;YACF,IAAI,MAAM;gBAAE,MAAM,MAAM,CAAC;SAC1B;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,0BAA0B,CACjD,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,UAAU,CAAC,mBAAmB,CAC/B,CAAC;YACF,IAAI,WAAW;gBAAE,MAAM,WAAW,CAAC;SACpC;QAED,MAAM,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACxD,IAAI,kBAAkB,EAAE;YACtB,MAAM,UAAU,GAAgB,EAAE,CAAC,MAAM,CACvC,GAAG,kBAAkB;iBAClB,MAAM,CAAC,CAAC,IAAe,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC1D,GAAG,CAAC,CAAC,IAAe,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAC5C,CAAC;YAEF,MAAM,eAAe,GAAG,CAAC,KAAgB,EAAE,EAAE;gBAC3C,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;oBAClD,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC;gBACpC,CAAC,CAAC,CAAC;gBACH,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC;YAEF,IAAI,UAAU,EAAE;gBACd,MAAM,iBAAiB,GAA4B,EAAE,CAAC;gBAEtD,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC/B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,EAAE;wBACtE,sDAAsD;wBACtD,OAAO;qBACR;oBAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9B,MAAM,KAAK,GACT,SAAS,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC;wBACnC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;wBAC9C,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBAEpD,iBAAiB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;oBAEhC,oEAAoE;oBACpE,gEAAgE;oBAChE,kDAAkD;oBAClD,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;wBACvD,OAAO;qBACR;oBAED,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBACxB,CAAC,CAAC,CAAC;gBAEH,OAAO,CAAC,UAAU,GAAG,iBAAiB,CAAC;aACxC;SACF;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,mCAAmC,CAAC,EAAE;YACjE,qFAAqF;YACrF,mCAAmC;YACnC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,mCAAmC,CAAC,CAAC;SAC7D;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE;YAClC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;SAC9B;QAED,OAAO,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC/C,OAAO,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC;QAC7C,OAAO,CAAC,kBAAkB,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC;QAEnD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IACvC,CAAC;IAEO,4BAA4B,CAClC,KAAa,EACb,SAAiB,EACjB,YAAoB,EACpB,cAAuB;QAEvB,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,CAAC;YAAE,OAAO,IAAI,CAAC;QAExD,IAAI,SAAS,EAAE;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YACvE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,WAAW;gBACxD,OAAO,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACpD;QACD,IAAI,YAAY,EAAE;YAChB,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YAChF,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,cAAc;gBAC5D,OAAO,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;SACtE;QACD,IAAI,cAAc,EAAE;YAClB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,cAAc;gBAC5D,OAAO,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SACjE;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,0BAA0B,CAChC,gBAAwB,EACxB,oBAA8C;QAE9C,IAAI,CAAC,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5D,OAAO,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAC/D;QACD,MAAM,MAAM,GAAG,oBAAoB;aAChC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACnF,OAAO,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;aAC9E;YACD,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,gBAAgB,EAAE;gBAClD,OAAO,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;aACtD;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YACjB,OAAO,MAAM,KAAK,IAAI,CAAC;QACzB,CAAC,CAAC,CAAC;QACL,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SAClB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,wBAAwB,CAC5B,SAAiC;QAEjC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,GAAG,GAAG,IAAA,wBAAkB,EAAC,GAAG,CAAC,CAAC;QACpC,MAAM,GAAG,GAAG,MAAM,IAAA,2BAAqB,EAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;YAC5D,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QACD,OAAO,MAAM,oCAAoC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,IAAU,EAAE,GAAS;QACzC,MAAM,OAAO,GAAG,WAAK,CAAC,cAAc,CAClC,GAAG,EACH,2DAA2D,CAC5D,CAAC;QACF,MAAM,YAAY,GAAG,WAAK,CAAC,cAAc,CACvC,GAAG,EACH,gEAAgE,CACjE,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;SACtC;QACD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAA,wBAAc,EACzC,IAAI,CAAC,OAAO,CAAC,aAAa,EAC1B,qEAAqE,CACtE,CAAC;YAEF,MAAM,cAAc,GAAG,WAAK,CAAC,cAAc,CACzC,YAAY,CAAC,CAAC,CAAC,EACf,mCAAmC,CACpC,CAAC;YAEF,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC/B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,MAAM,gBAAgB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEtD,MAAM,YAAY,GAAG,MAAM,IAAA,gBAAU,EAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YACpF,MAAM,YAAY,GAAG,IAAA,wBAAkB,EAAC,YAAY,CAAC,CAAC;YACtD,MAAM,YAAY,GAAG,WAAK,CAAC,cAAc,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAAC;YACrF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;aACvD;YACD,OAAO,MAAM,iBAAiB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QACD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC;IAED,+BAA+B,CAAC,cAA6B,EAAE,WAA2B;QACxF,MAAM,QAAQ,GAAuB;YACnC,gBAAgB,EAAE;gBAChB,QAAQ,EAAE,sCAAsC;gBAChD,WAAW,EAAE,oCAAoC;gBACjD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBAChC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;gBAC9C,eAAe,EAAE;oBACf,6BAA6B,EAAE,sCAAsC;iBACtE;aACF;SACF,CAAC;QAEF,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE;YACtC,IAAI,CAAC,cAAc,EAAE;gBACnB,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;aACH;SACF;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;YACnC,IAAI,CAAC,WAAW,EAAE;gBAChB,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;aACH;SACF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;YACzE,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,aAAa,GAAG,EAAE,CAAC;YAC7D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE;gBACnC,WAAW,GAAG,WAAY,CAAC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;gBACtE,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;gBACnE,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEjD,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC3D,MAAM,EAAE,SAAS;oBACjB,YAAY,EAAE;wBACZ,aAAa,EAAE;4BACb,oBAAoB,EAAE;gCACpB,OAAO,EAAE,WAAW;6BACrB;yBACF;qBACF;iBACF,CAAC,CAAC;aACJ;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE;gBACtC,cAAc,GAAG,cAAe,CAAC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;gBAC5E,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;gBACzE,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAEvD,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC3D,MAAM,EAAE,YAAY;oBACpB,YAAY,EAAE;wBACZ,aAAa,EAAE;4BACb,oBAAoB,EAAE;gCACpB,OAAO,EAAE,cAAc;6BACxB;yBACF;qBACF;oBACD,gBAAgB,EAAE;wBAChB,0DAA0D;wBAC1D,EAAE,YAAY,EAAE,4CAA4C,EAAE;wBAC9D,EAAE,YAAY,EAAE,4CAA4C,EAAE;wBAC9D,EAAE,YAAY,EAAE,6CAA6C,EAAE;wBAC/D,EAAE,YAAY,EAAE,6CAA6C,EAAE;qBAChE;iBACF,CAAC,CAAC;aACJ;SACF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,EAAE;YAC1C,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,mBAAmB,GAAG;gBAC9D,UAAU,EAAE,gDAAgD;gBAC5D,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;aAC5C,CAAC;SACH;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,EAAE;YACzC,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;SACxF;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACrC,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC;SAC3E;QAED,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,wBAAwB,GAAG;YACnE,QAAQ,EAAE,GAAG;YACb,YAAY,EAAE,MAAM;YACpB,UAAU,EAAE,gDAAgD;YAC5D,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;SACnC,CAAC;QACF,OAAO,IAAA,2BAAqB,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,CAAC,GAAoB;QAC5B,GAAG,GAAG,IAAA,wBAAc,EAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;QAE7C,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,GAAG,CAAC;QACxC,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QAEhD,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzC,IAAI,UAAU,EAAE;YACd,MAAM,UAAU,GAAG;gBACjB,6BAA6B;gBAC7B,GAAG,UAAU;gBACb,2BAA2B;gBAC3B,EAAE;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,OAAO,UAAU,CAAC;SACnB;QAED,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAED;;;;;;;;OAQG;IACK,0BAA0B,CAChC,iBAAyB,EACzB,YAAoB,EACpB,YAAoB;QAEpB,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAChF,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAEhF,IAAI,iBAAiB,KAAK,CAAC,EAAE;YAC3B,OAAO,cAAc,CAAC;SACvB;QAED,MAAM,kBAAkB,GAAG,cAAc,GAAG,iBAAiB,CAAC;QAC9D,OAAO,kBAAkB,GAAG,cAAc,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,cAAc,CAAC;IACnF,CAAC;IAED;;;;;;;OAOG;IACK,qBAAqB,CAAC,UAAkB,EAAE,KAAa;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEtC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,iBAAiB,KAAK,MAAM,UAAU,uBAAuB,CAAC,CAAC;SAChF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAEQ,oBAAI", "sourcesContent": ["import Debug from \"debug\";\nconst debug = Debug(\"node-saml\");\nimport * as zlib from \"zlib\";\nimport * as crypto from \"crypto\";\nimport { URL } from \"url\";\nimport * as querystring from \"querystring\";\nimport * as util from \"util\";\nimport { CacheProvider as InMemoryCacheProvider } from \"./inmemory-cache-provider\";\nimport * as algorithms from \"./algorithms\";\nimport { signAuthnRequestPost } from \"./saml-post-signing\";\nimport { ParsedQs } from \"qs\";\nimport {\n  isValidSamlSigningOptions,\n  AudienceRestrictionXML,\n  AuthorizeRequestXML,\n  CertCallback,\n  LogoutRequestXML,\n  SamlIDPListConfig,\n  SamlIDPEntryConfig,\n  SamlOptions,\n  ServiceMetadataXML,\n  XMLInput,\n  XMLObject,\n  XMLOutput,\n} from \"./types\";\nimport {\n  AuthenticateOptions,\n  AuthorizeOptions,\n  Profile,\n  SamlConfig,\n  ErrorWithXmlStatus,\n} from \"../passport-saml/types\";\nimport { assertRequired } from \"./utility\";\nimport {\n  buildXml2JsObject,\n  buildXmlBuilderObject,\n  decryptXml,\n  parseDomFromString,\n  parseXml2JsFromString,\n  validateXmlSignatureForCert,\n  xpath,\n} from \"./xml\";\n\nconst inflateRawAsync = util.promisify(zlib.inflateRaw);\nconst deflateRawAsync = util.promisify(zlib.deflateRaw);\n\ninterface NameID {\n  value: string | null;\n  format: string | null;\n}\n\nasync function processValidlySignedPostRequestAsync(\n  self: SAML,\n  doc: XMLOutput,\n  dom: Document\n): Promise<{ profile?: Profile; loggedOut?: boolean }> {\n  const request = doc.LogoutRequest;\n  if (request) {\n    const profile = {} as Profile;\n    if (request.$.ID) {\n      profile.ID = request.$.ID;\n    } else {\n      throw new Error(\"Missing SAML LogoutRequest ID\");\n    }\n    const issuer = request.Issuer;\n    if (issuer && issuer[0]._) {\n      profile.issuer = issuer[0]._;\n    } else {\n      throw new Error(\"Missing SAML issuer\");\n    }\n    const nameID = await self._getNameIdAsync(self, dom);\n    if (nameID) {\n      profile.nameID = nameID.value!;\n      if (nameID.format) {\n        profile.nameIDFormat = nameID.format;\n      }\n    } else {\n      throw new Error(\"Missing SAML NameID\");\n    }\n    const sessionIndex = request.SessionIndex;\n    if (sessionIndex) {\n      profile.sessionIndex = sessionIndex[0]._;\n    }\n    return { profile, loggedOut: true };\n  } else {\n    throw new Error(\"Unknown SAML request message\");\n  }\n}\n\nasync function processValidlySignedSamlLogoutAsync(\n  self: SAML,\n  doc: XMLOutput,\n  dom: Document\n): Promise<{ profile?: Profile | null; loggedOut?: boolean }> {\n  const response = doc.LogoutResponse;\n  const request = doc.LogoutRequest;\n\n  if (response) {\n    return { profile: null, loggedOut: true };\n  } else if (request) {\n    return await processValidlySignedPostRequestAsync(self, doc, dom);\n  } else {\n    throw new Error(\"Unknown SAML response message\");\n  }\n}\n\nasync function promiseWithNameID(nameid: Node): Promise<NameID> {\n  const format = xpath.selectAttributes(nameid, \"@Format\");\n  return {\n    value: nameid.textContent,\n    format: format && format[0] && format[0].nodeValue,\n  };\n}\n\nclass SAML {\n  // note that some methods in SAML are not yet marked as private as they are used in testing.\n  // those methods start with an underscore, e.g. _generateUniqueID\n  options: SamlOptions;\n  // This is only for testing\n  cacheProvider!: InMemoryCacheProvider;\n\n  constructor(ctorOptions: SamlConfig) {\n    this.options = this.initialize(ctorOptions);\n    this.cacheProvider = this.options.cacheProvider;\n  }\n\n  initialize(ctorOptions: SamlConfig): SamlOptions {\n    if (!ctorOptions) {\n      throw new TypeError(\"SamlOptions required on construction\");\n    }\n\n    const options = {\n      ...ctorOptions,\n      passive: ctorOptions.passive ?? false,\n      disableRequestedAuthnContext: ctorOptions.disableRequestedAuthnContext ?? false,\n      additionalParams: ctorOptions.additionalParams ?? {},\n      additionalAuthorizeParams: ctorOptions.additionalAuthorizeParams ?? {},\n      additionalLogoutParams: ctorOptions.additionalLogoutParams ?? {},\n      forceAuthn: ctorOptions.forceAuthn ?? false,\n      skipRequestCompression: ctorOptions.skipRequestCompression ?? false,\n      disableRequestAcsUrl: ctorOptions.disableRequestAcsUrl ?? false,\n      acceptedClockSkewMs: ctorOptions.acceptedClockSkewMs ?? 0,\n      maxAssertionAgeMs: ctorOptions.maxAssertionAgeMs ?? 0,\n      path: ctorOptions.path ?? \"/saml/consume\",\n      host: ctorOptions.host ?? \"localhost\",\n      issuer: ctorOptions.issuer ?? \"onelogin_saml\",\n      identifierFormat:\n        ctorOptions.identifierFormat === undefined\n          ? \"urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress\"\n          : ctorOptions.identifierFormat,\n      wantAssertionsSigned: ctorOptions.wantAssertionsSigned ?? false,\n      authnContext: ctorOptions.authnContext ?? [\n        \"urn:oasis:names:tc:SAML:2.0:ac:classes:PasswordProtectedTransport\",\n      ],\n      validateInResponseTo: ctorOptions.validateInResponseTo ?? false,\n      cert: assertRequired(ctorOptions.cert, \"cert is required\"),\n      requestIdExpirationPeriodMs: ctorOptions.requestIdExpirationPeriodMs ?? 28800000, // 8 hours\n      cacheProvider:\n        ctorOptions.cacheProvider ??\n        new InMemoryCacheProvider({\n          keyExpirationPeriodMs: ctorOptions.requestIdExpirationPeriodMs,\n        }),\n      logoutUrl: ctorOptions.logoutUrl ?? ctorOptions.entryPoint ?? \"\", // Default to Entry Point\n      signatureAlgorithm: ctorOptions.signatureAlgorithm ?? \"sha1\", // sha1, sha256, or sha512\n      authnRequestBinding: ctorOptions.authnRequestBinding ?? \"HTTP-Redirect\",\n\n      racComparison: ctorOptions.racComparison ?? \"exact\",\n    };\n\n    /**\n     * List of possible values:\n     * - exact : Assertion context must exactly match a context in the list\n     * - minimum:  Assertion context must be at least as strong as a context in the list\n     * - maximum:  Assertion context must be no stronger than a context in the list\n     * - better:  Assertion context must be stronger than all contexts in the list\n     */\n    if (![\"exact\", \"minimum\", \"maximum\", \"better\"].includes(options.racComparison)) {\n      throw new TypeError(\"racComparison must be one of ['exact', 'minimum', 'maximum', 'better']\");\n    }\n\n    return options;\n  }\n\n  private getCallbackUrl(host?: string | undefined) {\n    // Post-auth destination\n    if (this.options.callbackUrl) {\n      return this.options.callbackUrl;\n    } else {\n      const url = new URL(\"http://localhost\");\n      if (host) {\n        url.host = host;\n      } else {\n        url.host = this.options.host;\n      }\n      if (this.options.protocol) {\n        url.protocol = this.options.protocol;\n      }\n      url.pathname = this.options.path;\n      return url.toString();\n    }\n  }\n\n  _generateUniqueID() {\n    return crypto.randomBytes(10).toString(\"hex\");\n  }\n\n  private generateInstant() {\n    return new Date().toISOString();\n  }\n\n  private signRequest(samlMessage: querystring.ParsedUrlQueryInput): void {\n    this.options.privateKey = assertRequired(this.options.privateKey, \"privateKey is required\");\n\n    const samlMessageToSign: querystring.ParsedUrlQueryInput = {};\n    samlMessage.SigAlg = algorithms.getSigningAlgorithm(this.options.signatureAlgorithm);\n    const signer = algorithms.getSigner(this.options.signatureAlgorithm);\n    if (samlMessage.SAMLRequest) {\n      samlMessageToSign.SAMLRequest = samlMessage.SAMLRequest;\n    }\n    if (samlMessage.SAMLResponse) {\n      samlMessageToSign.SAMLResponse = samlMessage.SAMLResponse;\n    }\n    if (samlMessage.RelayState) {\n      samlMessageToSign.RelayState = samlMessage.RelayState;\n    }\n    if (samlMessage.SigAlg) {\n      samlMessageToSign.SigAlg = samlMessage.SigAlg;\n    }\n    signer.update(querystring.stringify(samlMessageToSign));\n    samlMessage.Signature = signer.sign(this._keyToPEM(this.options.privateKey), \"base64\");\n  }\n\n  private async generateAuthorizeRequestAsync(\n    isPassive: boolean,\n    isHttpPostBinding: boolean,\n    host: string | undefined\n  ): Promise<string | undefined> {\n    this.options.entryPoint = assertRequired(this.options.entryPoint, \"entryPoint is required\");\n\n    const id = \"_\" + this._generateUniqueID();\n    const instant = this.generateInstant();\n\n    if (this.options.validateInResponseTo) {\n      await this.cacheProvider.saveAsync(id, instant);\n    }\n    const request: AuthorizeRequestXML = {\n      \"samlp:AuthnRequest\": {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"@ID\": id,\n        \"@Version\": \"2.0\",\n        \"@IssueInstant\": instant,\n        \"@ProtocolBinding\": \"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST\",\n        \"@Destination\": this.options.entryPoint,\n        \"saml:Issuer\": {\n          \"@xmlns:saml\": \"urn:oasis:names:tc:SAML:2.0:assertion\",\n          \"#text\": this.options.issuer,\n        },\n      },\n    };\n\n    if (isPassive) request[\"samlp:AuthnRequest\"][\"@IsPassive\"] = true;\n\n    if (this.options.forceAuthn) {\n      request[\"samlp:AuthnRequest\"][\"@ForceAuthn\"] = true;\n    }\n\n    if (!this.options.disableRequestAcsUrl) {\n      request[\"samlp:AuthnRequest\"][\"@AssertionConsumerServiceURL\"] = this.getCallbackUrl(host);\n    }\n\n    if (this.options.identifierFormat != null) {\n      request[\"samlp:AuthnRequest\"][\"samlp:NameIDPolicy\"] = {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"@Format\": this.options.identifierFormat,\n        \"@AllowCreate\": \"true\",\n      };\n    }\n\n    if (!this.options.disableRequestedAuthnContext) {\n      const authnContextClassRefs: XMLInput[] = [];\n      (this.options.authnContext as string[]).forEach(function (value) {\n        authnContextClassRefs.push({\n          \"@xmlns:saml\": \"urn:oasis:names:tc:SAML:2.0:assertion\",\n          \"#text\": value,\n        });\n      });\n\n      request[\"samlp:AuthnRequest\"][\"samlp:RequestedAuthnContext\"] = {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"@Comparison\": this.options.racComparison,\n        \"saml:AuthnContextClassRef\": authnContextClassRefs,\n      };\n    }\n\n    if (this.options.attributeConsumingServiceIndex != null) {\n      request[\"samlp:AuthnRequest\"][\"@AttributeConsumingServiceIndex\"] =\n        this.options.attributeConsumingServiceIndex;\n    }\n\n    if (this.options.providerName != null) {\n      request[\"samlp:AuthnRequest\"][\"@ProviderName\"] = this.options.providerName;\n    }\n\n    if (this.options.scoping != null) {\n      const scoping: XMLInput = {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n      };\n\n      if (typeof this.options.scoping.proxyCount === \"number\") {\n        scoping[\"@ProxyCount\"] = this.options.scoping.proxyCount;\n      }\n\n      if (this.options.scoping.idpList) {\n        scoping[\"samlp:IDPList\"] = this.options.scoping.idpList.map(\n          (idpListItem: SamlIDPListConfig) => {\n            const formattedIdpListItem: XMLInput = {\n              \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n            };\n\n            if (idpListItem.entries) {\n              formattedIdpListItem[\"samlp:IDPEntry\"] = idpListItem.entries.map(\n                (entry: SamlIDPEntryConfig) => {\n                  const formattedEntry: XMLInput = {\n                    \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n                  };\n\n                  formattedEntry[\"@ProviderID\"] = entry.providerId;\n\n                  if (entry.name) {\n                    formattedEntry[\"@Name\"] = entry.name;\n                  }\n\n                  if (entry.loc) {\n                    formattedEntry[\"@Loc\"] = entry.loc;\n                  }\n\n                  return formattedEntry;\n                }\n              );\n            }\n\n            if (idpListItem.getComplete) {\n              formattedIdpListItem[\"samlp:GetComplete\"] = idpListItem.getComplete;\n            }\n\n            return formattedIdpListItem;\n          }\n        );\n      }\n\n      if (this.options.scoping.requesterId) {\n        scoping[\"samlp:RequesterID\"] = this.options.scoping.requesterId;\n      }\n\n      request[\"samlp:AuthnRequest\"][\"samlp:Scoping\"] = scoping;\n    }\n\n    let stringRequest = buildXmlBuilderObject(request, false);\n    // TODO: maybe we should always sign here\n    if (isHttpPostBinding && isValidSamlSigningOptions(this.options)) {\n      stringRequest = signAuthnRequestPost(stringRequest, this.options);\n    }\n    return stringRequest;\n  }\n\n  async _generateLogoutRequest(user: Profile) {\n    const id = \"_\" + this._generateUniqueID();\n    const instant = this.generateInstant();\n\n    const request = {\n      \"samlp:LogoutRequest\": {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"@xmlns:saml\": \"urn:oasis:names:tc:SAML:2.0:assertion\",\n        \"@ID\": id,\n        \"@Version\": \"2.0\",\n        \"@IssueInstant\": instant,\n        \"@Destination\": this.options.logoutUrl,\n        \"saml:Issuer\": {\n          \"@xmlns:saml\": \"urn:oasis:names:tc:SAML:2.0:assertion\",\n          \"#text\": this.options.issuer,\n        },\n        \"saml:NameID\": {\n          \"@Format\": user!.nameIDFormat,\n          \"#text\": user!.nameID,\n        },\n      },\n    } as LogoutRequestXML;\n\n    if (user!.nameQualifier != null) {\n      request[\"samlp:LogoutRequest\"][\"saml:NameID\"][\"@NameQualifier\"] = user!.nameQualifier;\n    }\n\n    if (user!.spNameQualifier != null) {\n      request[\"samlp:LogoutRequest\"][\"saml:NameID\"][\"@SPNameQualifier\"] = user!.spNameQualifier;\n    }\n\n    if (user!.sessionIndex) {\n      request[\"samlp:LogoutRequest\"][\"saml2p:SessionIndex\"] = {\n        \"@xmlns:saml2p\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"#text\": user!.sessionIndex,\n      };\n    }\n\n    await this.cacheProvider.saveAsync(id, instant);\n    return buildXmlBuilderObject(request, false);\n  }\n\n  _generateLogoutResponse(logoutRequest: Profile) {\n    const id = \"_\" + this._generateUniqueID();\n    const instant = this.generateInstant();\n\n    const request = {\n      \"samlp:LogoutResponse\": {\n        \"@xmlns:samlp\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        \"@xmlns:saml\": \"urn:oasis:names:tc:SAML:2.0:assertion\",\n        \"@ID\": id,\n        \"@Version\": \"2.0\",\n        \"@IssueInstant\": instant,\n        \"@Destination\": this.options.logoutUrl,\n        \"@InResponseTo\": logoutRequest.ID,\n        \"saml:Issuer\": {\n          \"#text\": this.options.issuer,\n        },\n        \"samlp:Status\": {\n          \"samlp:StatusCode\": {\n            \"@Value\": \"urn:oasis:names:tc:SAML:2.0:status:Success\",\n          },\n        },\n      },\n    };\n\n    return buildXmlBuilderObject(request, false);\n  }\n\n  async _requestToUrlAsync(\n    request: string | null | undefined,\n    response: string | null,\n    operation: string,\n    additionalParameters: querystring.ParsedUrlQuery\n  ): Promise<string> {\n    this.options.entryPoint = assertRequired(this.options.entryPoint, \"entryPoint is required\");\n\n    let buffer: Buffer;\n    if (this.options.skipRequestCompression) {\n      buffer = Buffer.from((request || response)!, \"utf8\");\n    } else {\n      buffer = await deflateRawAsync((request || response)!);\n    }\n\n    const base64 = buffer.toString(\"base64\");\n    let target = new URL(this.options.entryPoint);\n\n    if (operation === \"logout\") {\n      if (this.options.logoutUrl) {\n        target = new URL(this.options.logoutUrl);\n      }\n    } else if (operation !== \"authorize\") {\n      throw new Error(\"Unknown operation: \" + operation);\n    }\n\n    const samlMessage: querystring.ParsedUrlQuery = request\n      ? {\n          SAMLRequest: base64,\n        }\n      : {\n          SAMLResponse: base64,\n        };\n    Object.keys(additionalParameters).forEach((k) => {\n      samlMessage[k] = additionalParameters[k];\n    });\n    if (this.options.privateKey != null) {\n      if (!this.options.entryPoint) {\n        throw new Error('\"entryPoint\" config parameter is required for signed messages');\n      }\n\n      // sets .SigAlg and .Signature\n      this.signRequest(samlMessage);\n    }\n    Object.keys(samlMessage).forEach((k) => {\n      target.searchParams.set(k, samlMessage[k] as string);\n    });\n\n    return target.toString();\n  }\n\n  _getAdditionalParams(\n    RelayState: string,\n    operation: string,\n    overrideParams?: querystring.ParsedUrlQuery\n  ): querystring.ParsedUrlQuery {\n    const additionalParams: querystring.ParsedUrlQuery = {};\n\n    if (typeof RelayState === \"string\" && RelayState.length > 0) {\n      additionalParams.RelayState = RelayState;\n    }\n\n    const optionsAdditionalParams = this.options.additionalParams;\n    Object.keys(optionsAdditionalParams).forEach(function (k) {\n      additionalParams[k] = optionsAdditionalParams[k];\n    });\n\n    let optionsAdditionalParamsForThisOperation: Record<string, string> = {};\n    if (operation == \"authorize\") {\n      optionsAdditionalParamsForThisOperation = this.options.additionalAuthorizeParams;\n    }\n    if (operation == \"logout\") {\n      optionsAdditionalParamsForThisOperation = this.options.additionalLogoutParams;\n    }\n\n    Object.keys(optionsAdditionalParamsForThisOperation).forEach(function (k) {\n      additionalParams[k] = optionsAdditionalParamsForThisOperation[k];\n    });\n\n    overrideParams = overrideParams ?? {};\n    Object.keys(overrideParams).forEach(function (k) {\n      additionalParams[k] = overrideParams![k];\n    });\n\n    return additionalParams;\n  }\n\n  async getAuthorizeUrlAsync(\n    RelayState: string,\n    host: string | undefined,\n    options: AuthorizeOptions\n  ): Promise<string> {\n    const request = await this.generateAuthorizeRequestAsync(this.options.passive, false, host);\n    const operation = \"authorize\";\n    const overrideParams = options ? options.additionalParams || {} : {};\n    return await this._requestToUrlAsync(\n      request,\n      null,\n      operation,\n      this._getAdditionalParams(RelayState, operation, overrideParams)\n    );\n  }\n\n  async getAuthorizeFormAsync(RelayState: string, host?: string): Promise<string> {\n    this.options.entryPoint = assertRequired(this.options.entryPoint, \"entryPoint is required\");\n\n    // The quoteattr() function is used in a context, where the result will not be evaluated by javascript\n    // but must be interpreted by an XML or HTML parser, and it must absolutely avoid breaking the syntax\n    // of an element attribute.\n    const quoteattr = function (\n      s:\n        | string\n        | number\n        | boolean\n        | undefined\n        | null\n        | readonly string[]\n        | readonly number[]\n        | readonly boolean[],\n      preserveCR?: boolean\n    ) {\n      const preserveCRChar = preserveCR ? \"&#13;\" : \"\\n\";\n      return (\n        (\"\" + s) // Forces the conversion to string.\n          .replace(/&/g, \"&amp;\") // This MUST be the 1st replacement.\n          .replace(/'/g, \"&apos;\") // The 4 other predefined entities, required.\n          .replace(/\"/g, \"&quot;\")\n          .replace(/</g, \"&lt;\")\n          .replace(/>/g, \"&gt;\")\n          // Add other replacements here for HTML only\n          // Or for XML, only if the named entities are defined in its DTD.\n          .replace(/\\r\\n/g, preserveCRChar) // Must be before the next replacement.\n          .replace(/[\\r\\n]/g, preserveCRChar)\n      );\n    };\n\n    const request = await this.generateAuthorizeRequestAsync(this.options.passive, true, host);\n    let buffer: Buffer;\n    if (this.options.skipRequestCompression) {\n      buffer = Buffer.from(request!, \"utf8\");\n    } else {\n      buffer = await deflateRawAsync(request!);\n    }\n\n    const operation = \"authorize\";\n    const additionalParameters = this._getAdditionalParams(RelayState, operation);\n    const samlMessage: querystring.ParsedUrlQueryInput = {\n      SAMLRequest: buffer!.toString(\"base64\"),\n    };\n\n    Object.keys(additionalParameters).forEach((k) => {\n      samlMessage[k] = additionalParameters[k] || \"\";\n    });\n\n    const formInputs = Object.keys(samlMessage)\n      .map((k) => {\n        return '<input type=\"hidden\" name=\"' + k + '\" value=\"' + quoteattr(samlMessage[k]) + '\" />';\n      })\n      .join(\"\\r\\n\");\n\n    return [\n      \"<!DOCTYPE html>\",\n      \"<html>\",\n      \"<head>\",\n      '<meta charset=\"utf-8\">',\n      '<meta http-equiv=\"x-ua-compatible\" content=\"ie=edge\">',\n      \"</head>\",\n      '<body onload=\"document.forms[0].submit()\">',\n      \"<noscript>\",\n      \"<p><strong>Note:</strong> Since your browser does not support JavaScript, you must press the button below once to proceed.</p>\",\n      \"</noscript>\",\n      '<form method=\"post\" action=\"' + encodeURI(this.options.entryPoint) + '\">',\n      formInputs,\n      '<input type=\"submit\" value=\"Submit\" />',\n      \"</form>\",\n      '<script>document.forms[0].style.display=\"none\";</script>', // Hide the form if JavaScript is enabled\n      \"</body>\",\n      \"</html>\",\n    ].join(\"\\r\\n\");\n  }\n\n  async getLogoutUrlAsync(\n    user: Profile,\n    RelayState: string,\n    options: AuthenticateOptions & AuthorizeOptions\n  ) {\n    const request = await this._generateLogoutRequest(user);\n    const operation = \"logout\";\n    const overrideParams = options ? options.additionalParams || {} : {};\n    return await this._requestToUrlAsync(\n      request,\n      null,\n      operation,\n      this._getAdditionalParams(RelayState, operation, overrideParams)\n    );\n  }\n\n  getLogoutResponseUrl(\n    samlLogoutRequest: Profile,\n    RelayState: string,\n    options: AuthenticateOptions & AuthorizeOptions,\n    callback: (err: Error | null, url?: string | null) => void\n  ): void {\n    util.callbackify(() => this.getLogoutResponseUrlAsync(samlLogoutRequest, RelayState, options))(\n      callback\n    );\n  }\n  private async getLogoutResponseUrlAsync(\n    samlLogoutRequest: Profile,\n    RelayState: string,\n    options: AuthenticateOptions & AuthorizeOptions // add RelayState,\n  ): Promise<string> {\n    const response = this._generateLogoutResponse(samlLogoutRequest);\n    const operation = \"logout\";\n    const overrideParams = options ? options.additionalParams || {} : {};\n    return await this._requestToUrlAsync(\n      null,\n      response,\n      operation,\n      this._getAdditionalParams(RelayState, operation, overrideParams)\n    );\n  }\n\n  _certToPEM(cert: string): string {\n    cert = cert.match(/.{1,64}/g)!.join(\"\\n\");\n\n    if (cert.indexOf(\"-BEGIN CERTIFICATE-\") === -1) cert = \"-----BEGIN CERTIFICATE-----\\n\" + cert;\n    if (cert.indexOf(\"-END CERTIFICATE-\") === -1) cert = cert + \"\\n-----END CERTIFICATE-----\\n\";\n\n    return cert;\n  }\n\n  private async certsToCheck(): Promise<string[]> {\n    let checkedCerts: string[];\n\n    if (typeof this.options.cert === \"function\") {\n      checkedCerts = await util\n        .promisify(this.options.cert as CertCallback)()\n        .then((certs) => {\n          certs = assertRequired(certs, \"callback didn't return cert\");\n          if (!Array.isArray(certs)) {\n            certs = [certs];\n          }\n          return certs;\n        });\n    } else if (Array.isArray(this.options.cert)) {\n      checkedCerts = this.options.cert;\n    } else {\n      checkedCerts = [this.options.cert];\n    }\n\n    checkedCerts.forEach((cert) => {\n      assertRequired(cert, \"unknown cert found\");\n    });\n\n    return checkedCerts;\n  }\n\n  // This function checks that the |currentNode| in the |fullXml| document contains exactly 1 valid\n  //   signature of the |currentNode|.\n  //\n  // See https://github.com/bergie/passport-saml/issues/19 for references to some of the attack\n  //   vectors against SAML signature verification.\n  validateSignature(fullXml: string, currentNode: Element, certs: string[]): boolean {\n    const xpathSigQuery =\n      \".//*[\" +\n      \"local-name(.)='Signature' and \" +\n      \"namespace-uri(.)='http://www.w3.org/2000/09/xmldsig#' and \" +\n      \"descendant::*[local-name(.)='Reference' and @URI='#\" +\n      currentNode.getAttribute(\"ID\") +\n      \"']\" +\n      \"]\";\n    const signatures = xpath.selectElements(currentNode, xpathSigQuery);\n    // This function is expecting to validate exactly one signature, so if we find more or fewer\n    //   than that, reject.\n    if (signatures.length !== 1) {\n      return false;\n    }\n    const xpathTransformQuery =\n      \".//*[\" +\n      \"local-name(.)='Transform' and \" +\n      \"namespace-uri(.)='http://www.w3.org/2000/09/xmldsig#' and \" +\n      \"ancestor::*[local-name(.)='Reference' and @URI='#\" +\n      currentNode.getAttribute(\"ID\") +\n      \"']\" +\n      \"]\";\n    const transforms = xpath.selectElements(currentNode, xpathTransformQuery);\n    // Reject also XMLDSIG with more than 2 Transform\n    if (transforms.length > 2) {\n      // do not return false, throw an error so that it can be caught by tests differently\n      throw new Error(\"Invalid signature, too many transforms\");\n    }\n\n    const signature = signatures[0];\n    return certs.some((certToCheck) => {\n      return validateXmlSignatureForCert(\n        signature,\n        this._certToPEM(certToCheck),\n        fullXml,\n        currentNode\n      );\n    });\n  }\n\n  async validatePostResponseAsync(\n    container: Record<string, string>\n  ): Promise<{ profile?: Profile | null; loggedOut?: boolean }> {\n    let xml: string, doc: Document, inResponseTo: string | null;\n    try {\n      xml = Buffer.from(container.SAMLResponse, \"base64\").toString(\"utf8\");\n      doc = parseDomFromString(xml);\n\n      if (!Object.prototype.hasOwnProperty.call(doc, \"documentElement\"))\n        throw new Error(\"SAMLResponse is not valid base64-encoded XML\");\n\n      const inResponseToNodes = xpath.selectAttributes(\n        doc,\n        \"/*[local-name()='Response']/@InResponseTo\"\n      );\n\n      if (inResponseToNodes) {\n        inResponseTo = inResponseToNodes.length ? inResponseToNodes[0].nodeValue : null;\n\n        await this.validateInResponseTo(inResponseTo);\n      }\n      const certs = await this.certsToCheck();\n      // Check if this document has a valid top-level signature which applies to the entire XML document\n      let validSignature = false;\n      if (\n        this.validateSignature(xml, doc.documentElement, certs) &&\n        Array.from(doc.childNodes as NodeListOf<Element>).filter(\n          (n) => n.tagName != null && n.childNodes != null\n        ).length === 1\n      ) {\n        validSignature = true;\n      }\n\n      const assertions = xpath.selectElements(\n        doc,\n        \"/*[local-name()='Response']/*[local-name()='Assertion']\"\n      );\n      const encryptedAssertions = xpath.selectElements(\n        doc,\n        \"/*[local-name()='Response']/*[local-name()='EncryptedAssertion']\"\n      );\n\n      if (assertions.length + encryptedAssertions.length > 1) {\n        // There's no reason I know of that we want to handle multiple assertions, and it seems like a\n        //   potential risk vector for signature scope issues, so treat this as an invalid signature\n        throw new Error(\"Invalid signature: multiple assertions\");\n      }\n\n      if (assertions.length == 1) {\n        if (\n          (this.options.wantAssertionsSigned || !validSignature) &&\n          !this.validateSignature(xml, assertions[0], certs)\n        ) {\n          throw new Error(\"Invalid signature\");\n        }\n        return await this.processValidlySignedAssertionAsync(\n          assertions[0].toString(),\n          xml,\n          inResponseTo!\n        );\n      }\n\n      if (encryptedAssertions.length == 1) {\n        this.options.decryptionPvk = assertRequired(\n          this.options.decryptionPvk,\n          \"No decryption key for encrypted SAML response\"\n        );\n\n        const encryptedAssertionXml = encryptedAssertions[0].toString();\n\n        const decryptedXml = await decryptXml(encryptedAssertionXml, this.options.decryptionPvk);\n        const decryptedDoc = parseDomFromString(decryptedXml);\n        const decryptedAssertions = xpath.selectElements(\n          decryptedDoc,\n          \"/*[local-name()='Assertion']\"\n        );\n        if (decryptedAssertions.length != 1) throw new Error(\"Invalid EncryptedAssertion content\");\n\n        if (\n          (this.options.wantAssertionsSigned || !validSignature) &&\n          !this.validateSignature(decryptedXml, decryptedAssertions[0], certs)\n        ) {\n          throw new Error(\"Invalid signature from encrypted assertion\");\n        }\n\n        return await this.processValidlySignedAssertionAsync(\n          decryptedAssertions[0].toString(),\n          xml,\n          inResponseTo!\n        );\n      }\n\n      // If there's no assertion, fall back on xml2js response parsing for the status &\n      //   LogoutResponse code.\n\n      const xmljsDoc = await parseXml2JsFromString(xml);\n      const response = xmljsDoc.Response;\n      if (response) {\n        const assertion = response.Assertion;\n        if (!assertion) {\n          const status = response.Status;\n          if (status) {\n            const statusCode = status[0].StatusCode;\n            if (\n              statusCode &&\n              statusCode[0].$.Value === \"urn:oasis:names:tc:SAML:2.0:status:Responder\"\n            ) {\n              const nestedStatusCode = statusCode[0].StatusCode;\n              if (\n                nestedStatusCode &&\n                nestedStatusCode[0].$.Value === \"urn:oasis:names:tc:SAML:2.0:status:NoPassive\"\n              ) {\n                if (!validSignature) {\n                  throw new Error(\"Invalid signature: NoPassive\");\n                }\n                return { profile: null, loggedOut: false };\n              }\n            }\n\n            // Note that we're not requiring a valid signature before this logic -- since we are\n            //   throwing an error in any case, and some providers don't sign error results,\n            //   let's go ahead and give the potentially more helpful error.\n            if (statusCode && statusCode[0].$.Value) {\n              const msgType = statusCode[0].$.Value.match(/[^:]*$/)[0];\n              if (msgType != \"Success\") {\n                let msg = \"unspecified\";\n                if (status[0].StatusMessage) {\n                  msg = status[0].StatusMessage[0]._;\n                } else if (statusCode[0].StatusCode) {\n                  msg = statusCode[0].StatusCode[0].$.Value.match(/[^:]*$/)[0];\n                }\n                const statusXml = buildXml2JsObject(\"Status\", status[0]);\n                throw new ErrorWithXmlStatus(\n                  \"SAML provider returned \" + msgType + \" error: \" + msg,\n                  statusXml\n                );\n              }\n            }\n          }\n        }\n        throw new Error(\"Missing SAML assertion\");\n      } else {\n        if (!validSignature) {\n          throw new Error(\"Invalid signature: No response found\");\n        }\n        const logoutResponse = xmljsDoc.LogoutResponse;\n        if (logoutResponse) {\n          return { profile: null, loggedOut: true };\n        } else {\n          throw new Error(\"Unknown SAML response message\");\n        }\n      }\n    } catch (err) {\n      debug(\"validatePostResponse resulted in an error: %s\", err);\n      if (this.options.validateInResponseTo) {\n        await this.cacheProvider.removeAsync(inResponseTo!);\n      }\n      throw err;\n    }\n  }\n\n  private async validateInResponseTo(inResponseTo: string | null): Promise<undefined> {\n    if (this.options.validateInResponseTo) {\n      if (inResponseTo) {\n        const result = await this.cacheProvider.getAsync(inResponseTo);\n        if (!result) throw new Error(\"InResponseTo is not valid\");\n        return;\n      } else {\n        throw new Error(\"InResponseTo is missing from response\");\n      }\n    } else {\n      return;\n    }\n  }\n\n  async validateRedirectAsync(\n    container: ParsedQs,\n    originalQuery: string | null\n  ): Promise<{ profile?: Profile | null; loggedOut?: boolean }> {\n    const samlMessageType = container.SAMLRequest ? \"SAMLRequest\" : \"SAMLResponse\";\n\n    const data = Buffer.from(container[samlMessageType] as string, \"base64\");\n    const inflated = await inflateRawAsync(data);\n\n    const dom = parseDomFromString(inflated.toString());\n    const doc: XMLOutput = await parseXml2JsFromString(inflated);\n    samlMessageType === \"SAMLResponse\"\n      ? await this.verifyLogoutResponse(doc)\n      : this.verifyLogoutRequest(doc);\n    await this.hasValidSignatureForRedirect(container, originalQuery);\n    return await processValidlySignedSamlLogoutAsync(this, doc, dom);\n  }\n\n  private async hasValidSignatureForRedirect(\n    container: ParsedQs,\n    originalQuery: string | null\n  ): Promise<boolean | void> {\n    const tokens = originalQuery!.split(\"&\");\n    const getParam = (key: string) => {\n      const exists = tokens.filter((t) => {\n        return new RegExp(key).test(t);\n      });\n      return exists[0];\n    };\n\n    if (container.Signature) {\n      let urlString = getParam(\"SAMLRequest\") || getParam(\"SAMLResponse\");\n\n      if (getParam(\"RelayState\")) {\n        urlString += \"&\" + getParam(\"RelayState\");\n      }\n\n      urlString += \"&\" + getParam(\"SigAlg\");\n\n      const certs = await this.certsToCheck();\n      const hasValidQuerySignature = certs.some((cert) => {\n        return this.validateSignatureForRedirect(\n          urlString,\n          container.Signature as string,\n          container.SigAlg as string,\n          cert\n        );\n      });\n      if (!hasValidQuerySignature) {\n        throw new Error(\"Invalid query signature\");\n      }\n    } else {\n      return true;\n    }\n  }\n\n  private validateSignatureForRedirect(\n    urlString: crypto.BinaryLike,\n    signature: string,\n    alg: string,\n    cert: string\n  ) {\n    // See if we support a matching algorithm, case-insensitive. Otherwise, throw error.\n    function hasMatch(ourAlgo: string) {\n      // The incoming algorithm is forwarded as a URL.\n      // We trim everything before the last # get something we can compare to the Node.js list\n      const algFromURI = alg.toLowerCase().replace(/.*#(.*)$/, \"$1\");\n      return ourAlgo.toLowerCase() === algFromURI;\n    }\n    const i = crypto.getHashes().findIndex(hasMatch);\n    let matchingAlgo;\n    if (i > -1) {\n      matchingAlgo = crypto.getHashes()[i];\n    } else {\n      throw new Error(alg + \" is not supported\");\n    }\n\n    const verifier = crypto.createVerify(matchingAlgo);\n    verifier.update(urlString);\n\n    return verifier.verify(this._certToPEM(cert), signature, \"base64\");\n  }\n\n  private verifyLogoutRequest(doc: XMLOutput) {\n    this.verifyIssuer(doc.LogoutRequest);\n    const nowMs = new Date().getTime();\n    const conditions = doc.LogoutRequest.$;\n    const conErr = this.checkTimestampsValidityError(\n      nowMs,\n      conditions.NotBefore,\n      conditions.NotOnOrAfter\n    );\n    if (conErr) {\n      throw conErr;\n    }\n  }\n\n  private async verifyLogoutResponse(doc: XMLOutput) {\n    const statusCode = doc.LogoutResponse.Status[0].StatusCode[0].$.Value;\n    if (statusCode !== \"urn:oasis:names:tc:SAML:2.0:status:Success\")\n      throw new Error(\"Bad status code: \" + statusCode);\n\n    this.verifyIssuer(doc.LogoutResponse);\n    const inResponseTo = doc.LogoutResponse.$.InResponseTo;\n    if (inResponseTo) {\n      return this.validateInResponseTo(inResponseTo);\n    }\n\n    return true;\n  }\n\n  private verifyIssuer(samlMessage: XMLOutput) {\n    if (this.options.idpIssuer != null) {\n      const issuer = samlMessage.Issuer;\n      if (issuer) {\n        if (issuer[0]._ !== this.options.idpIssuer)\n          throw new Error(\n            \"Unknown SAML issuer. Expected: \" + this.options.idpIssuer + \" Received: \" + issuer[0]._\n          );\n      } else {\n        throw new Error(\"Missing SAML issuer\");\n      }\n    }\n  }\n\n  private async processValidlySignedAssertionAsync(\n    xml: string,\n    samlResponseXml: string,\n    inResponseTo: string\n  ) {\n    let msg;\n    const nowMs = new Date().getTime();\n    const profile = {} as Profile;\n    const doc: XMLOutput = await parseXml2JsFromString(xml);\n    const parsedAssertion: XMLOutput = doc;\n    const assertion: XMLOutput = doc.Assertion;\n    getInResponseTo: {\n      const issuer = assertion.Issuer;\n      if (issuer && issuer[0]._) {\n        profile.issuer = issuer[0]._;\n      }\n\n      if (inResponseTo) {\n        profile.inResponseTo = inResponseTo;\n      }\n\n      const authnStatement = assertion.AuthnStatement;\n      if (authnStatement) {\n        if (authnStatement[0].$ && authnStatement[0].$.SessionIndex) {\n          profile.sessionIndex = authnStatement[0].$.SessionIndex;\n        }\n      }\n\n      const subject = assertion.Subject;\n      let subjectConfirmation, confirmData;\n      if (subject) {\n        const nameID = subject[0].NameID;\n        if (nameID && nameID[0]._) {\n          profile.nameID = nameID[0]._;\n\n          if (nameID[0].$ && nameID[0].$.Format) {\n            profile.nameIDFormat = nameID[0].$.Format;\n            profile.nameQualifier = nameID[0].$.NameQualifier;\n            profile.spNameQualifier = nameID[0].$.SPNameQualifier;\n          }\n        }\n\n        subjectConfirmation = subject[0].SubjectConfirmation\n          ? subject[0].SubjectConfirmation[0]\n          : null;\n        confirmData =\n          subjectConfirmation && subjectConfirmation.SubjectConfirmationData\n            ? subjectConfirmation.SubjectConfirmationData[0]\n            : null;\n        if (subject[0].SubjectConfirmation && subject[0].SubjectConfirmation.length > 1) {\n          msg = \"Unable to process multiple SubjectConfirmations in SAML assertion\";\n          throw new Error(msg);\n        }\n\n        if (subjectConfirmation) {\n          if (confirmData && confirmData.$) {\n            const subjectNotBefore = confirmData.$.NotBefore;\n            const subjectNotOnOrAfter = confirmData.$.NotOnOrAfter;\n            const maxTimeLimitMs = this.processMaxAgeAssertionTime(\n              this.options.maxAssertionAgeMs,\n              subjectNotOnOrAfter,\n              assertion.$.IssueInstant\n            );\n\n            const subjErr = this.checkTimestampsValidityError(\n              nowMs,\n              subjectNotBefore,\n              subjectNotOnOrAfter,\n              maxTimeLimitMs\n            );\n            if (subjErr) {\n              throw subjErr;\n            }\n          }\n        }\n      }\n\n      // Test to see that if we have a SubjectConfirmation InResponseTo that it matches\n      // the 'InResponseTo' attribute set in the Response\n      if (this.options.validateInResponseTo) {\n        if (subjectConfirmation) {\n          if (confirmData && confirmData.$) {\n            const subjectInResponseTo = confirmData.$.InResponseTo;\n            if (inResponseTo && subjectInResponseTo && subjectInResponseTo != inResponseTo) {\n              await this.cacheProvider.removeAsync(inResponseTo);\n              throw new Error(\"InResponseTo is not valid\");\n            } else if (subjectInResponseTo) {\n              let foundValidInResponseTo = false;\n              const result = await this.cacheProvider.getAsync(subjectInResponseTo);\n              if (result) {\n                const createdAt = new Date(result);\n                if (nowMs < createdAt.getTime() + this.options.requestIdExpirationPeriodMs)\n                  foundValidInResponseTo = true;\n              }\n              await this.cacheProvider.removeAsync(inResponseTo);\n              if (!foundValidInResponseTo) {\n                throw new Error(\"InResponseTo is not valid\");\n              }\n              break getInResponseTo;\n            }\n          }\n        } else {\n          await this.cacheProvider.removeAsync(inResponseTo);\n          break getInResponseTo;\n        }\n      } else {\n        break getInResponseTo;\n      }\n    }\n    const conditions = assertion.Conditions ? assertion.Conditions[0] : null;\n    if (assertion.Conditions && assertion.Conditions.length > 1) {\n      msg = \"Unable to process multiple conditions in SAML assertion\";\n      throw new Error(msg);\n    }\n    if (conditions && conditions.$) {\n      const maxTimeLimitMs = this.processMaxAgeAssertionTime(\n        this.options.maxAssertionAgeMs,\n        conditions.$.NotOnOrAfter,\n        assertion.$.IssueInstant\n      );\n      const conErr = this.checkTimestampsValidityError(\n        nowMs,\n        conditions.$.NotBefore,\n        conditions.$.NotOnOrAfter,\n        maxTimeLimitMs\n      );\n      if (conErr) throw conErr;\n    }\n\n    if (this.options.audience != null) {\n      const audienceErr = this.checkAudienceValidityError(\n        this.options.audience,\n        conditions.AudienceRestriction\n      );\n      if (audienceErr) throw audienceErr;\n    }\n\n    const attributeStatement = assertion.AttributeStatement;\n    if (attributeStatement) {\n      const attributes: XMLOutput[] = [].concat(\n        ...attributeStatement\n          .filter((attr: XMLObject) => Array.isArray(attr.Attribute))\n          .map((attr: XMLObject) => attr.Attribute)\n      );\n\n      const attrValueMapper = (value: XMLObject) => {\n        const hasChildren = Object.keys(value).some((cur) => {\n          return cur !== \"_\" && cur !== \"$\";\n        });\n        return hasChildren ? value : value._;\n      };\n\n      if (attributes) {\n        const profileAttributes: Record<string, unknown> = {};\n\n        attributes.forEach((attribute) => {\n          if (!Object.prototype.hasOwnProperty.call(attribute, \"AttributeValue\")) {\n            // if attributes has no AttributeValue child, continue\n            return;\n          }\n\n          const name = attribute.$.Name;\n          const value =\n            attribute.AttributeValue.length === 1\n              ? attrValueMapper(attribute.AttributeValue[0])\n              : attribute.AttributeValue.map(attrValueMapper);\n\n          profileAttributes[name] = value;\n\n          // If any property is already present in profile and is also present\n          // in attributes, then skip the one from attributes. Handle this\n          // conflict gracefully without returning any error\n          if (Object.prototype.hasOwnProperty.call(profile, name)) {\n            return;\n          }\n\n          profile[name] = value;\n        });\n\n        profile.attributes = profileAttributes;\n      }\n    }\n\n    if (!profile.mail && profile[\"urn:oid:0.9.2342.19200300.100.1.3\"]) {\n      // See https://spaces.internet2.edu/display/InCFederation/Supported+Attribute+Summary\n      // for definition of attribute OIDs\n      profile.mail = profile[\"urn:oid:0.9.2342.19200300.100.1.3\"];\n    }\n\n    if (!profile.email && profile.mail) {\n      profile.email = profile.mail;\n    }\n\n    profile.getAssertionXml = () => xml.toString();\n    profile.getAssertion = () => parsedAssertion;\n    profile.getSamlResponseXml = () => samlResponseXml;\n\n    return { profile, loggedOut: false };\n  }\n\n  private checkTimestampsValidityError(\n    nowMs: number,\n    notBefore: string,\n    notOnOrAfter: string,\n    maxTimeLimitMs?: number\n  ) {\n    if (this.options.acceptedClockSkewMs == -1) return null;\n\n    if (notBefore) {\n      const notBeforeMs = this.dateStringToTimestamp(notBefore, \"NotBefore\");\n      if (nowMs + this.options.acceptedClockSkewMs < notBeforeMs)\n        return new Error(\"SAML assertion not yet valid\");\n    }\n    if (notOnOrAfter) {\n      const notOnOrAfterMs = this.dateStringToTimestamp(notOnOrAfter, \"NotOnOrAfter\");\n      if (nowMs - this.options.acceptedClockSkewMs >= notOnOrAfterMs)\n        return new Error(\"SAML assertion expired: clocks skewed too much\");\n    }\n    if (maxTimeLimitMs) {\n      if (nowMs - this.options.acceptedClockSkewMs >= maxTimeLimitMs)\n        return new Error(\"SAML assertion expired: assertion too old\");\n    }\n\n    return null;\n  }\n\n  private checkAudienceValidityError(\n    expectedAudience: string,\n    audienceRestrictions: AudienceRestrictionXML[]\n  ) {\n    if (!audienceRestrictions || audienceRestrictions.length < 1) {\n      return new Error(\"SAML assertion has no AudienceRestriction\");\n    }\n    const errors = audienceRestrictions\n      .map((restriction) => {\n        if (!restriction.Audience || !restriction.Audience[0] || !restriction.Audience[0]._) {\n          return new Error(\"SAML assertion AudienceRestriction has no Audience value\");\n        }\n        if (restriction.Audience[0]._ !== expectedAudience) {\n          return new Error(\"SAML assertion audience mismatch\");\n        }\n        return null;\n      })\n      .filter((result) => {\n        return result !== null;\n      });\n    if (errors.length > 0) {\n      return errors[0];\n    }\n    return null;\n  }\n\n  async validatePostRequestAsync(\n    container: Record<string, string>\n  ): Promise<{ profile?: Profile; loggedOut?: boolean }> {\n    const xml = Buffer.from(container.SAMLRequest, \"base64\").toString(\"utf8\");\n    const dom = parseDomFromString(xml);\n    const doc = await parseXml2JsFromString(xml);\n    const certs = await this.certsToCheck();\n    if (!this.validateSignature(xml, dom.documentElement, certs)) {\n      throw new Error(\"Invalid signature on documentElement\");\n    }\n    return await processValidlySignedPostRequestAsync(this, doc, dom);\n  }\n\n  async _getNameIdAsync(self: SAML, doc: Node): Promise<NameID> {\n    const nameIds = xpath.selectElements(\n      doc,\n      \"/*[local-name()='LogoutRequest']/*[local-name()='NameID']\"\n    );\n    const encryptedIds = xpath.selectElements(\n      doc,\n      \"/*[local-name()='LogoutRequest']/*[local-name()='EncryptedID']\"\n    );\n\n    if (nameIds.length + encryptedIds.length > 1) {\n      throw new Error(\"Invalid LogoutRequest\");\n    }\n    if (nameIds.length === 1) {\n      return promiseWithNameID(nameIds[0]);\n    }\n    if (encryptedIds.length === 1) {\n      self.options.decryptionPvk = assertRequired(\n        self.options.decryptionPvk,\n        \"No decryption key found getting name ID for encrypted SAML response\"\n      );\n\n      const encryptedDatas = xpath.selectElements(\n        encryptedIds[0],\n        \"./*[local-name()='EncryptedData']\"\n      );\n\n      if (encryptedDatas.length !== 1) {\n        throw new Error(\"Invalid LogoutRequest\");\n      }\n      const encryptedDataXml = encryptedDatas[0].toString();\n\n      const decryptedXml = await decryptXml(encryptedDataXml, self.options.decryptionPvk);\n      const decryptedDoc = parseDomFromString(decryptedXml);\n      const decryptedIds = xpath.selectElements(decryptedDoc, \"/*[local-name()='NameID']\");\n      if (decryptedIds.length !== 1) {\n        throw new Error(\"Invalid EncryptedAssertion content\");\n      }\n      return await promiseWithNameID(decryptedIds[0]);\n    }\n    throw new Error(\"Missing SAML NameID\");\n  }\n\n  generateServiceProviderMetadata(decryptionCert: string | null, signingCert?: string | null) {\n    const metadata: ServiceMetadataXML = {\n      EntityDescriptor: {\n        \"@xmlns\": \"urn:oasis:names:tc:SAML:2.0:metadata\",\n        \"@xmlns:ds\": \"http://www.w3.org/2000/09/xmldsig#\",\n        \"@entityID\": this.options.issuer,\n        \"@ID\": this.options.issuer.replace(/\\W/g, \"_\"),\n        SPSSODescriptor: {\n          \"@protocolSupportEnumeration\": \"urn:oasis:names:tc:SAML:2.0:protocol\",\n        },\n      },\n    };\n\n    if (this.options.decryptionPvk != null) {\n      if (!decryptionCert) {\n        throw new Error(\n          \"Missing decryptionCert while generating metadata for decrypting service provider\"\n        );\n      }\n    }\n    if (this.options.privateKey != null) {\n      if (!signingCert) {\n        throw new Error(\n          \"Missing signingCert while generating metadata for signing service provider messages\"\n        );\n      }\n    }\n\n    if (this.options.decryptionPvk != null || this.options.privateKey != null) {\n      metadata.EntityDescriptor.SPSSODescriptor.KeyDescriptor = [];\n      if (this.options.privateKey != null) {\n        signingCert = signingCert!.replace(/-+BEGIN CERTIFICATE-+\\r?\\n?/, \"\");\n        signingCert = signingCert.replace(/-+END CERTIFICATE-+\\r?\\n?/, \"\");\n        signingCert = signingCert.replace(/\\r\\n/g, \"\\n\");\n\n        metadata.EntityDescriptor.SPSSODescriptor.KeyDescriptor.push({\n          \"@use\": \"signing\",\n          \"ds:KeyInfo\": {\n            \"ds:X509Data\": {\n              \"ds:X509Certificate\": {\n                \"#text\": signingCert,\n              },\n            },\n          },\n        });\n      }\n\n      if (this.options.decryptionPvk != null) {\n        decryptionCert = decryptionCert!.replace(/-+BEGIN CERTIFICATE-+\\r?\\n?/, \"\");\n        decryptionCert = decryptionCert.replace(/-+END CERTIFICATE-+\\r?\\n?/, \"\");\n        decryptionCert = decryptionCert.replace(/\\r\\n/g, \"\\n\");\n\n        metadata.EntityDescriptor.SPSSODescriptor.KeyDescriptor.push({\n          \"@use\": \"encryption\",\n          \"ds:KeyInfo\": {\n            \"ds:X509Data\": {\n              \"ds:X509Certificate\": {\n                \"#text\": decryptionCert,\n              },\n            },\n          },\n          EncryptionMethod: [\n            // this should be the set that the xmlenc library supports\n            { \"@Algorithm\": \"http://www.w3.org/2009/xmlenc11#aes256-gcm\" },\n            { \"@Algorithm\": \"http://www.w3.org/2009/xmlenc11#aes128-gcm\" },\n            { \"@Algorithm\": \"http://www.w3.org/2001/04/xmlenc#aes256-cbc\" },\n            { \"@Algorithm\": \"http://www.w3.org/2001/04/xmlenc#aes128-cbc\" },\n          ],\n        });\n      }\n    }\n\n    if (this.options.logoutCallbackUrl != null) {\n      metadata.EntityDescriptor.SPSSODescriptor.SingleLogoutService = {\n        \"@Binding\": \"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST\",\n        \"@Location\": this.options.logoutCallbackUrl,\n      };\n    }\n\n    if (this.options.identifierFormat != null) {\n      metadata.EntityDescriptor.SPSSODescriptor.NameIDFormat = this.options.identifierFormat;\n    }\n\n    if (this.options.wantAssertionsSigned) {\n      metadata.EntityDescriptor.SPSSODescriptor[\"@WantAssertionsSigned\"] = true;\n    }\n\n    metadata.EntityDescriptor.SPSSODescriptor.AssertionConsumerService = {\n      \"@index\": \"1\",\n      \"@isDefault\": \"true\",\n      \"@Binding\": \"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST\",\n      \"@Location\": this.getCallbackUrl(),\n    };\n    return buildXmlBuilderObject(metadata, true);\n  }\n\n  _keyToPEM(key: string | Buffer): typeof key extends string | Buffer ? string | Buffer : Error {\n    key = assertRequired(key, \"key is required\");\n\n    if (typeof key !== \"string\") return key;\n    if (key.split(/\\r?\\n/).length !== 1) return key;\n\n    const matchedKey = key.match(/.{1,64}/g);\n\n    if (matchedKey) {\n      const wrappedKey = [\n        \"-----BEGIN PRIVATE KEY-----\",\n        ...matchedKey,\n        \"-----END PRIVATE KEY-----\",\n        \"\",\n      ].join(\"\\n\");\n      return wrappedKey;\n    }\n\n    throw new Error(\"Invalid key\");\n  }\n\n  /**\n   * Process max age assertion and use it if it is more restrictive than the NotOnOrAfter age\n   * assertion received in the SAMLResponse.\n   *\n   * @param maxAssertionAgeMs Max time after IssueInstant that we will accept assertion, in Ms.\n   * @param notOnOrAfter Expiration provided in response.\n   * @param issueInstant Time when response was issued.\n   * @returns {*} The expiration time to be used, in Ms.\n   */\n  private processMaxAgeAssertionTime(\n    maxAssertionAgeMs: number,\n    notOnOrAfter: string,\n    issueInstant: string\n  ): number {\n    const notOnOrAfterMs = this.dateStringToTimestamp(notOnOrAfter, \"NotOnOrAfter\");\n    const issueInstantMs = this.dateStringToTimestamp(issueInstant, \"IssueInstant\");\n\n    if (maxAssertionAgeMs === 0) {\n      return notOnOrAfterMs;\n    }\n\n    const maxAssertionTimeMs = issueInstantMs + maxAssertionAgeMs;\n    return maxAssertionTimeMs < notOnOrAfterMs ? maxAssertionTimeMs : notOnOrAfterMs;\n  }\n\n  /**\n   * Convert a date string to a timestamp (in milliseconds).\n   *\n   * @param dateString A string representation of a date\n   * @param label Descriptive name of the date being passed in, e.g. \"NotOnOrAfter\"\n   * @throws Will throw an error if parsing `dateString` returns `NaN`\n   * @returns {number} The timestamp (in milliseconds) representation of the given date\n   */\n  private dateStringToTimestamp(dateString: string, label: string): number {\n    const dateMs = Date.parse(dateString);\n\n    if (isNaN(dateMs)) {\n      throw new Error(`Error parsing ${label}: '${dateString}' is not a valid date`);\n    }\n\n    return dateMs;\n  }\n}\n\nexport { SAML };\n"]}