{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\fallback.service.spec.ts", "mappings": ";;AAAA,6CAAsD;AACtD,0DAA0F;AAE1F,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;IACjC,IAAI,OAA0B,CAAC;IAE/B,MAAM,aAAa,GAAqB;QACtC,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,CAAC;QACX,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;QACvD,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;KACzC,CAAC;IAEF,MAAM,aAAa,GAAqB;QACtC,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,CAAC;QACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;QACrB,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;KACzC,CAAC;IAEF,MAAM,aAAa,GAAmB;QACpC,cAAc,EAAE,IAAI;QACpB,kBAAkB,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;QAClD,YAAY,EAAE,IAAI;QAClB,QAAQ,EAAE,IAAI;QACd,YAAY,EAAE,GAAG;QACjB,eAAe,EAAE,UAAU;KAC5B,CAAC;IAEF,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE,CAAC,oCAAiB,CAAC;SAC/B,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAoB,oCAAiB,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,8CAA8C;QAC9C,OAAO,CAAC,aAAa,EAAE,CAAC;QACxB,OAAO,CAAC,eAAe,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,4DAA4D,EAAE,GAAG,EAAE;YACpE,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC3D,MAAM,UAAU,GAAG,eAAe,CAAC;YACnC,MAAM,YAAY,GAAG,QAAQ,CAAC;YAE9B,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAElE,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7C,MAAM,CAAC,OAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,cAAc,GAAmB;gBACrC,GAAG,aAAa;gBAChB,cAAc,EAAE,KAAK;aACtB,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEpE,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,CACV,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAC1E,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACnE,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAEnD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;YAE9F,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;YAEhD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;YAEhD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;YAE9F,qEAAqE;YACrE,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sDAAsD,EAAE,KAAK,IAAI,EAAE;YACpE,MAAM,eAAe,GAAqB;gBACxC,IAAI,EAAE,kBAAkB;gBACxB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;gBACrB,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;aACvD,CAAC;YAEF,MAAM,yBAAyB,GAAmB;gBAChD,GAAG,aAAa;gBAChB,kBAAkB,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;aACrD,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,yBAAyB,CAAC,CAAC;YAE/E,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAE/F,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,gBAAgB,GAAqB;gBACzC,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;gBACrB,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;aACxD,CAAC;YAEF,MAAM,gBAAgB,GAAqB;gBACzC,IAAI,EAAE,mBAAmB;gBACzB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;gBACrB,OAAO,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;aACxD,CAAC;YAEF,MAAM,2BAA2B,GAAmB;gBAClD,GAAG,aAAa;gBAChB,kBAAkB,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;aACzD,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,2BAA2B,CAAC,CAAC;YAEjF,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,CACV,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAC1E,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC5D,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,CACV,OAAO,CAAC,eAAe,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAC7E,CAAC,OAAO,CAAC,OAAO,CAAC,sEAAsE,CAAC,CAAC;QAC5F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,aAAa,GAAqB;gBACtC,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;gBACrB,OAAO,EAAE,KAAK,IAAI,EAAE;oBAClB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;oBACtD,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC;gBACtC,CAAC;aACF,CAAC;YAEF,MAAM,uBAAuB,GAAmB;gBAC9C,GAAG,aAAa;gBAChB,kBAAkB,EAAE,CAAC,aAAa,CAAC;aACpC,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,uBAAuB,CAAC,CAAC;YAE7E,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAE/F,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,YAAY,GAAqB;gBACrC,IAAI,EAAE,eAAe;gBACrB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;gBACrB,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,KAAK,IAAI,EAAE;oBAClB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;oBACvD,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC;gBACrC,CAAC;aACF,CAAC;YAEF,MAAM,sBAAsB,GAAmB;gBAC7C,GAAG,aAAa;gBAChB,kBAAkB,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;aAClD,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;YAE5E,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAE/F,0DAA0D;YAC1D,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6CAA6C,EAAE,KAAK,IAAI,EAAE;YAC3D,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAEpD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,0BAA0B,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAE1G,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC1C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE;QACvB,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC;YAE/C,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEtF,qBAAqB;YACrB,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC;YAE/C,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEtF,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE;gBAC7F,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,cAAc,GAAmB;gBACrC,GAAG,aAAa;gBAChB,QAAQ,EAAE,EAAE,EAAE,iBAAiB;aAChC,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEpE,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,iBAAiB,EAAE,CAAC;YAC/C,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEtF,2BAA2B;YAC3B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE;gBAC7F,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAC;YAEH,oFAAoF;YACpF,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,gBAAgB,GAAmB;gBACvC,GAAG,aAAa;gBAChB,YAAY,EAAE,CAAC;aAChB,CAAC;YAEF,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5C,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAEtE,yBAAyB;YACzB,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAC/E,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/E,mCAAmC;YACnC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/E,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,kCAAkC;QAChE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACrC,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAC/E,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/E,MAAM,YAAY,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACjE,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;YAChC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAC/E,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE3E,MAAM,YAAY,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YAC7C,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7B,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE,GAAG,EAAE;YACvD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5D,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,CAAC,OAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC9D,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YAEpD,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAEhF,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC5D,MAAM,CAAC,OAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpD,MAAM,CAAC,OAAQ,CAAC,oBAAoB,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;YAChE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,GAAG,EAAE;YAC5D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC/D,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAEhE,MAAM,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,KAAK,IAAI,EAAE;YACpB,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAEnE,wBAAwB;YACxB,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;YACpD,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAClF,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,uBAAuB;YACvB,IAAI,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAExC,gBAAgB;YAChB,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAE9C,2BAA2B;YAC3B,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACtD,MAAM,CAAC,OAAQ,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,CAAC,OAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACpC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,GAAG,EAAE;YAC9C,MAAM,SAAS,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC;YAEzD,OAAO,CAAC,oBAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;YAEzD,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,MAAM,CAAC,MAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,MAAM,CAAC,MAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAClC,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACjE,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YAElE,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;YACxD,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/E,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC7B,UAAU,CAAC,GAAG,EAAE;YACd,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,kDAAkD;YAClD,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;YAE/E,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAElD,qBAAqB;YACrB,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,EAAE,aAAa,EAAE;gBACnE,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;aAC5B,CAAC,CAAC;YAEH,sBAAsB;YACtB,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,EAAE,aAAa,EAAE;gBACnE,WAAW,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE;aAC5B,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,oCAAiB,CAAC,mBAAmB,EAAE,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAC/C,MAAM,MAAM,GAAG,oCAAiB,CAAC,mBAAmB,EAAE,CAAC;YAEvD,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACnD,MAAM,MAAM,GAAG,oCAAiB,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;YAEhE,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAClD,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;YAClF,MAAM,gBAAgB,GAAmB;gBACvC,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,EAAE,EAAE,gBAAgB;gBACxC,YAAY,EAAE,IAAI;gBAClB,QAAQ,EAAE,EAAE,EAAE,kCAAkC;gBAChD,YAAY,EAAE,GAAG;gBACjB,eAAe,EAAE,aAAa;aAC/B,CAAC;YAEF,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAEtE,mBAAmB;YACnB,MAAM,QAAQ,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC;YAC9C,OAAO,CAAC,aAAa,CAAC,eAAe,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;YAEtF,iCAAiC;YACjC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;YAEtD,MAAM,aAAa,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE;gBAC7F,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;aAC/B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\__tests__\\fallback.service.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { AIFallbackService, FallbackConfig, FallbackStrategy } from '../fallback.service';\r\n\r\ndescribe('AIFallbackService', () => {\r\n  let service: AIFallbackService;\r\n\r\n  const mockStrategy1: FallbackStrategy = {\r\n    name: 'strategy1',\r\n    priority: 1,\r\n    condition: (error) => error.message.includes('timeout'),\r\n    handler: () => ({ result: 'fallback1' }),\r\n  };\r\n\r\n  const mockStrategy2: FallbackStrategy = {\r\n    name: 'strategy2',\r\n    priority: 2,\r\n    condition: () => true,\r\n    handler: () => ({ result: 'fallback2' }),\r\n  };\r\n\r\n  const defaultConfig: FallbackConfig = {\r\n    enableFallback: true,\r\n    fallbackStrategies: [mockStrategy1, mockStrategy2],\r\n    cacheEnabled: true,\r\n    cacheTtl: 5000,\r\n    maxCacheSize: 100,\r\n    degradationMode: 'graceful',\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      providers: [AIFallbackService],\r\n    }).compile();\r\n\r\n    service = module.get<AIFallbackService>(AIFallbackService);\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Clean up all registered providers and cache\r\n    service.clearAllCache();\r\n    service.resetAllMetrics();\r\n  });\r\n\r\n  describe('registerProvider', () => {\r\n    it('should register a new provider with fallback configuration', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const config = service.getProviderConfig(providerId);\r\n      expect(config).toEqual(defaultConfig);\r\n    });\r\n\r\n    it('should initialize metrics for registered provider', () => {\r\n      const providerId = 'test-provider';\r\n      const providerType = 'openai';\r\n\r\n      service.registerProvider(providerId, providerType, defaultConfig);\r\n\r\n      const metrics = service.getProviderMetrics(providerId);\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe(providerId);\r\n      expect(metrics!.totalFallbacks).toBe(0);\r\n      expect(metrics!.strategyUsage).toEqual({});\r\n    });\r\n  });\r\n\r\n  describe('executeFallback', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should throw original error when fallback is disabled', async () => {\r\n      const disabledConfig: FallbackConfig = {\r\n        ...defaultConfig,\r\n        enableFallback: false,\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', disabledConfig);\r\n\r\n      const originalError = new Error('original error');\r\n\r\n      await expect(\r\n        service.executeFallback('test-provider', 'test-operation', originalError)\r\n      ).rejects.toThrow('original error');\r\n    });\r\n\r\n    it('should execute fallback strategy based on condition', async () => {\r\n      const timeoutError = new Error('timeout occurred');\r\n\r\n      const result = await service.executeFallback('test-provider', 'test-operation', timeoutError);\r\n\r\n      expect(result).toEqual({ result: 'fallback1' });\r\n      \r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalFallbacks).toBe(1);\r\n      expect(metrics!.strategyUsage['strategy1']).toBe(1);\r\n    });\r\n\r\n    it('should execute fallback strategy in priority order', async () => {\r\n      const genericError = new Error('generic error');\r\n\r\n      const result = await service.executeFallback('test-provider', 'test-operation', genericError);\r\n\r\n      // Should use strategy2 because strategy1 only matches timeout errors\r\n      expect(result).toEqual({ result: 'fallback2' });\r\n    });\r\n\r\n    it('should fall back to next strategy if first one fails', async () => {\r\n      const failingStrategy: FallbackStrategy = {\r\n        name: 'failing-strategy',\r\n        priority: 1,\r\n        condition: () => true,\r\n        handler: () => { throw new Error('strategy failed'); },\r\n      };\r\n\r\n      const configWithFailingStrategy: FallbackConfig = {\r\n        ...defaultConfig,\r\n        fallbackStrategies: [failingStrategy, mockStrategy2],\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', configWithFailingStrategy);\r\n\r\n      const originalError = new Error('original error');\r\n\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError);\r\n\r\n      expect(result).toEqual({ result: 'fallback2' });\r\n    });\r\n\r\n    it('should throw original error if all strategies fail', async () => {\r\n      const failingStrategy1: FallbackStrategy = {\r\n        name: 'failing-strategy1',\r\n        priority: 1,\r\n        condition: () => true,\r\n        handler: () => { throw new Error('strategy1 failed'); },\r\n      };\r\n\r\n      const failingStrategy2: FallbackStrategy = {\r\n        name: 'failing-strategy2',\r\n        priority: 2,\r\n        condition: () => true,\r\n        handler: () => { throw new Error('strategy2 failed'); },\r\n      };\r\n\r\n      const configWithFailingStrategies: FallbackConfig = {\r\n        ...defaultConfig,\r\n        fallbackStrategies: [failingStrategy1, failingStrategy2],\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', configWithFailingStrategies);\r\n\r\n      const originalError = new Error('original error');\r\n\r\n      await expect(\r\n        service.executeFallback('test-provider', 'test-operation', originalError)\r\n      ).rejects.toThrow('original error');\r\n    });\r\n\r\n    it('should throw error for unregistered provider', async () => {\r\n      const originalError = new Error('original error');\r\n\r\n      await expect(\r\n        service.executeFallback('unknown-provider', 'test-operation', originalError)\r\n      ).rejects.toThrow('Fallback configuration not registered for provider: unknown-provider');\r\n    });\r\n\r\n    it('should handle async strategy handlers', async () => {\r\n      const asyncStrategy: FallbackStrategy = {\r\n        name: 'async-strategy',\r\n        priority: 1,\r\n        condition: () => true,\r\n        handler: async () => {\r\n          await new Promise(resolve => setTimeout(resolve, 10));\r\n          return { result: 'async-fallback' };\r\n        },\r\n      };\r\n\r\n      const configWithAsyncStrategy: FallbackConfig = {\r\n        ...defaultConfig,\r\n        fallbackStrategies: [asyncStrategy],\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', configWithAsyncStrategy);\r\n\r\n      const originalError = new Error('original error');\r\n\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError);\r\n\r\n      expect(result).toEqual({ result: 'async-fallback' });\r\n    });\r\n\r\n    it('should handle strategy timeout', async () => {\r\n      const slowStrategy: FallbackStrategy = {\r\n        name: 'slow-strategy',\r\n        priority: 1,\r\n        condition: () => true,\r\n        timeout: 50,\r\n        handler: async () => {\r\n          await new Promise(resolve => setTimeout(resolve, 100));\r\n          return { result: 'slow-fallback' };\r\n        },\r\n      };\r\n\r\n      const configWithSlowStrategy: FallbackConfig = {\r\n        ...defaultConfig,\r\n        fallbackStrategies: [slowStrategy, mockStrategy2],\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', configWithSlowStrategy);\r\n\r\n      const originalError = new Error('original error');\r\n\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError);\r\n\r\n      // Should fall back to strategy2 after strategy1 times out\r\n      expect(result).toEqual({ result: 'fallback2' });\r\n    });\r\n  });\r\n\r\n  describe('executeFallbackWithDetails', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return detailed fallback information', async () => {\r\n      const originalError = new Error('timeout occurred');\r\n\r\n      const result = await service.executeFallbackWithDetails('test-provider', 'test-operation', originalError);\r\n\r\n      expect(result.result).toEqual({ result: 'fallback1' });\r\n      expect(result.strategy).toBe('strategy1');\r\n      expect(result.fromCache).toBe(false);\r\n      expect(result.degraded).toBe(true);\r\n      expect(result.executionTime).toBeGreaterThanOrEqual(0);\r\n    });\r\n  });\r\n\r\n  describe('caching', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should cache response when caching is enabled', () => {\r\n      const response = { result: 'cached-response' };\r\n      \r\n      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);\r\n\r\n      // Verify cache stats\r\n      const stats = service.getCacheStats();\r\n      expect(stats.size).toBe(1);\r\n    });\r\n\r\n    it('should return cached result when available', async () => {\r\n      const response = { result: 'cached-response' };\r\n      \r\n      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);\r\n\r\n      const originalError = new Error('original error');\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {\r\n        requestData: { input: 'test' },\r\n      });\r\n\r\n      expect(result).toEqual(response);\r\n      \r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.cacheHits).toBe(1);\r\n    });\r\n\r\n    it('should not return expired cached result', async () => {\r\n      const shortTtlConfig: FallbackConfig = {\r\n        ...defaultConfig,\r\n        cacheTtl: 10, // Very short TTL\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', shortTtlConfig);\r\n\r\n      const response = { result: 'cached-response' };\r\n      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);\r\n\r\n      // Wait for cache to expire\r\n      await new Promise(resolve => setTimeout(resolve, 20));\r\n\r\n      const originalError = new Error('original error');\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {\r\n        requestData: { input: 'test' },\r\n      });\r\n\r\n      // Should use fallback strategy2, not cached result (strategy1 only matches timeout)\r\n      expect(result).toEqual({ result: 'fallback2' });\r\n    });\r\n\r\n    it('should evict oldest entry when cache is full', () => {\r\n      const smallCacheConfig: FallbackConfig = {\r\n        ...defaultConfig,\r\n        maxCacheSize: 2,\r\n      };\r\n\r\n      service.unregisterProvider('test-provider');\r\n      service.registerProvider('test-provider', 'openai', smallCacheConfig);\r\n\r\n      // Fill cache to capacity\r\n      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });\r\n      service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });\r\n      \r\n      // Add one more to trigger eviction\r\n      service.cacheResponse('test-provider', 'op3', { input: '3' }, { result: '3' });\r\n\r\n      const stats = service.getCacheStats();\r\n      expect(stats.size).toBe(2); // Should still be at max capacity\r\n    });\r\n\r\n    it('should clear provider cache', () => {\r\n      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });\r\n      service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });\r\n\r\n      const clearedCount = service.clearProviderCache('test-provider');\r\n      expect(clearedCount).toBe(2);\r\n\r\n      const stats = service.getCacheStats();\r\n      expect(stats.size).toBe(0);\r\n    });\r\n\r\n    it('should clear all cache', () => {\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n      \r\n      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });\r\n      service.cacheResponse('provider2', 'op2', { input: '2' }, { result: '2' });\r\n\r\n      const clearedCount = service.clearAllCache();\r\n      expect(clearedCount).toBe(2);\r\n\r\n      const stats = service.getCacheStats();\r\n      expect(stats.size).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('getProviderMetrics', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return metrics for registered provider', () => {\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n\r\n      expect(metrics).toBeDefined();\r\n      expect(metrics!.providerId).toBe('test-provider');\r\n    });\r\n\r\n    it('should return null for unregistered provider', () => {\r\n      const metrics = service.getProviderMetrics('unknown-provider');\r\n      expect(metrics).toBeNull();\r\n    });\r\n\r\n    it('should update metrics after fallback execution', async () => {\r\n      const originalError = new Error('timeout occurred');\r\n\r\n      await service.executeFallback('test-provider', 'test-operation', originalError);\r\n\r\n      const metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalFallbacks).toBe(1);\r\n      expect(metrics!.strategyUsage['strategy1']).toBe(1);\r\n      expect(metrics!.averageExecutionTime).toBeGreaterThanOrEqual(0);\r\n    });\r\n  });\r\n\r\n  describe('getAllProviderMetrics', () => {\r\n    it('should return empty array when no providers registered', () => {\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toEqual([]);\r\n    });\r\n\r\n    it('should return metrics for all registered providers', () => {\r\n      service.registerProvider('provider1', 'openai', defaultConfig);\r\n      service.registerProvider('provider2', 'bedrock', defaultConfig);\r\n\r\n      const metrics = service.getAllProviderMetrics();\r\n      expect(metrics).toHaveLength(2);\r\n      expect(metrics.map(m => m.providerId)).toContain('provider1');\r\n      expect(metrics.map(m => m.providerId)).toContain('provider2');\r\n    });\r\n  });\r\n\r\n  describe('resetProviderMetrics', () => {\r\n    beforeEach(async () => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n      \r\n      // Generate some metrics\r\n      const originalError = new Error('timeout occurred');\r\n      await service.executeFallback('test-provider', 'test-operation', originalError);\r\n    });\r\n\r\n    it('should reset metrics for specific provider', () => {\r\n      // Verify metrics exist\r\n      let metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalFallbacks).toBe(1);\r\n\r\n      // Reset metrics\r\n      service.resetProviderMetrics('test-provider');\r\n\r\n      // Verify metrics are reset\r\n      metrics = service.getProviderMetrics('test-provider');\r\n      expect(metrics!.totalFallbacks).toBe(0);\r\n      expect(metrics!.strategyUsage).toEqual({});\r\n    });\r\n  });\r\n\r\n  describe('updateProviderConfig', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should update provider configuration', () => {\r\n      const newConfig = { cacheTtl: 10000, maxCacheSize: 200 };\r\n      \r\n      service.updateProviderConfig('test-provider', newConfig);\r\n\r\n      const config = service.getProviderConfig('test-provider');\r\n      expect(config!.cacheTtl).toBe(10000);\r\n      expect(config!.maxCacheSize).toBe(200);\r\n      expect(config!.enableFallback).toBe(true); // Should keep existing values\r\n    });\r\n  });\r\n\r\n  describe('unregisterProvider', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should remove provider configuration and metrics', () => {\r\n      expect(service.getProviderConfig('test-provider')).toBeDefined();\r\n      expect(service.getProviderMetrics('test-provider')).toBeDefined();\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      expect(service.getProviderConfig('test-provider')).toBeNull();\r\n      expect(service.getProviderMetrics('test-provider')).toBeNull();\r\n    });\r\n\r\n    it('should clear provider cache when unregistering', () => {\r\n      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });\r\n      \r\n      expect(service.getCacheStats().size).toBe(1);\r\n\r\n      service.unregisterProvider('test-provider');\r\n\r\n      expect(service.getCacheStats().size).toBe(0);\r\n    });\r\n  });\r\n\r\n  describe('getCacheStats', () => {\r\n    beforeEach(() => {\r\n      service.registerProvider('test-provider', 'openai', defaultConfig);\r\n    });\r\n\r\n    it('should return cache statistics', async () => {\r\n      // Add some cache entries and generate hits/misses\r\n      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });\r\n      \r\n      const originalError = new Error('original error');\r\n      \r\n      // Generate cache hit\r\n      await service.executeFallback('test-provider', 'op1', originalError, {\r\n        requestData: { input: '1' },\r\n      });\r\n      \r\n      // Generate cache miss\r\n      await service.executeFallback('test-provider', 'op2', originalError, {\r\n        requestData: { input: '2' },\r\n      });\r\n\r\n      const stats = service.getCacheStats();\r\n      expect(stats.size).toBeGreaterThan(0);\r\n      expect(stats.totalHits).toBe(1);\r\n      expect(stats.totalMisses).toBe(1);\r\n      expect(stats.hitRate).toBe(0.5);\r\n    });\r\n  });\r\n\r\n  describe('static factory methods', () => {\r\n    it('should create default fallback config', () => {\r\n      const config = AIFallbackService.createDefaultConfig();\r\n\r\n      expect(config.enableFallback).toBe(true);\r\n      expect(config.cacheEnabled).toBe(true);\r\n      expect(config.fallbackStrategies).toHaveLength(2);\r\n      expect(config.degradationMode).toBe('graceful');\r\n    });\r\n\r\n    it('should create minimal fallback config', () => {\r\n      const config = AIFallbackService.createMinimalConfig();\r\n\r\n      expect(config.enableFallback).toBe(true);\r\n      expect(config.cacheEnabled).toBe(false);\r\n      expect(config.fallbackStrategies).toHaveLength(1);\r\n      expect(config.degradationMode).toBe('minimal');\r\n    });\r\n\r\n    it('should create cached-only fallback config', () => {\r\n      const config = AIFallbackService.createCachedOnlyConfig(300000);\r\n\r\n      expect(config.enableFallback).toBe(true);\r\n      expect(config.cacheEnabled).toBe(true);\r\n      expect(config.cacheTtl).toBe(300000);\r\n      expect(config.fallbackStrategies).toHaveLength(0);\r\n      expect(config.degradationMode).toBe('cached_only');\r\n    });\r\n  });\r\n\r\n  describe('degradation modes', () => {\r\n    it('should use stale cache in cached_only mode when no strategies work', async () => {\r\n      const cachedOnlyConfig: FallbackConfig = {\r\n        enableFallback: true,\r\n        fallbackStrategies: [], // No strategies\r\n        cacheEnabled: true,\r\n        cacheTtl: 10, // Very short TTL to make it stale\r\n        maxCacheSize: 100,\r\n        degradationMode: 'cached_only',\r\n      };\r\n\r\n      service.registerProvider('test-provider', 'openai', cachedOnlyConfig);\r\n\r\n      // Cache a response\r\n      const response = { result: 'stale-response' };\r\n      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);\r\n\r\n      // Wait for cache to become stale\r\n      await new Promise(resolve => setTimeout(resolve, 20));\r\n\r\n      const originalError = new Error('original error');\r\n      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {\r\n        requestData: { input: 'test' },\r\n      });\r\n\r\n      expect(result).toEqual(response);\r\n    });\r\n  });\r\n});"], "version": 3}