a0050388f0f8724788c14fc9984c7d31
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const fallback_service_1 = require("../fallback.service");
describe('AIFallbackService', () => {
    let service;
    const mockStrategy1 = {
        name: 'strategy1',
        priority: 1,
        condition: (error) => error.message.includes('timeout'),
        handler: () => ({ result: 'fallback1' }),
    };
    const mockStrategy2 = {
        name: 'strategy2',
        priority: 2,
        condition: () => true,
        handler: () => ({ result: 'fallback2' }),
    };
    const defaultConfig = {
        enableFallback: true,
        fallbackStrategies: [mockStrategy1, mockStrategy2],
        cacheEnabled: true,
        cacheTtl: 5000,
        maxCacheSize: 100,
        degradationMode: 'graceful',
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [fallback_service_1.AIFallbackService],
        }).compile();
        service = module.get(fallback_service_1.AIFallbackService);
    });
    afterEach(() => {
        // Clean up all registered providers and cache
        service.clearAllCache();
        service.resetAllMetrics();
    });
    describe('registerProvider', () => {
        it('should register a new provider with fallback configuration', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const config = service.getProviderConfig(providerId);
            expect(config).toEqual(defaultConfig);
        });
        it('should initialize metrics for registered provider', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const metrics = service.getProviderMetrics(providerId);
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe(providerId);
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('executeFallback', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should throw original error when fallback is disabled', async () => {
            const disabledConfig = {
                ...defaultConfig,
                enableFallback: false,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', disabledConfig);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should execute fallback strategy based on condition', async () => {
            const timeoutError = new Error('timeout occurred');
            const result = await service.executeFallback('test-provider', 'test-operation', timeoutError);
            expect(result).toEqual({ result: 'fallback1' });
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
        });
        it('should execute fallback strategy in priority order', async () => {
            const genericError = new Error('generic error');
            const result = await service.executeFallback('test-provider', 'test-operation', genericError);
            // Should use strategy2 because strategy1 only matches timeout errors
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should fall back to next strategy if first one fails', async () => {
            const failingStrategy = {
                name: 'failing-strategy',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy failed'); },
            };
            const configWithFailingStrategy = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should throw original error if all strategies fail', async () => {
            const failingStrategy1 = {
                name: 'failing-strategy1',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy1 failed'); },
            };
            const failingStrategy2 = {
                name: 'failing-strategy2',
                priority: 2,
                condition: () => true,
                handler: () => { throw new Error('strategy2 failed'); },
            };
            const configWithFailingStrategies = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy1, failingStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategies);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should throw error for unregistered provider', async () => {
            const originalError = new Error('original error');
            await expect(service.executeFallback('unknown-provider', 'test-operation', originalError)).rejects.toThrow('Fallback configuration not registered for provider: unknown-provider');
        });
        it('should handle async strategy handlers', async () => {
            const asyncStrategy = {
                name: 'async-strategy',
                priority: 1,
                condition: () => true,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 10));
                    return { result: 'async-fallback' };
                },
            };
            const configWithAsyncStrategy = {
                ...defaultConfig,
                fallbackStrategies: [asyncStrategy],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithAsyncStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'async-fallback' });
        });
        it('should handle strategy timeout', async () => {
            const slowStrategy = {
                name: 'slow-strategy',
                priority: 1,
                condition: () => true,
                timeout: 50,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    return { result: 'slow-fallback' };
                },
            };
            const configWithSlowStrategy = {
                ...defaultConfig,
                fallbackStrategies: [slowStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithSlowStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            // Should fall back to strategy2 after strategy1 times out
            expect(result).toEqual({ result: 'fallback2' });
        });
    });
    describe('executeFallbackWithDetails', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return detailed fallback information', async () => {
            const originalError = new Error('timeout occurred');
            const result = await service.executeFallbackWithDetails('test-provider', 'test-operation', originalError);
            expect(result.result).toEqual({ result: 'fallback1' });
            expect(result.strategy).toBe('strategy1');
            expect(result.fromCache).toBe(false);
            expect(result.degraded).toBe(true);
            expect(result.executionTime).toBeGreaterThan(0);
        });
    });
    describe('caching', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should cache response when caching is enabled', () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Verify cache stats
            const stats = service.getCacheStats();
            expect(stats.size).toBe(1);
        });
        it('should return cached result when available', async () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.cacheHits).toBe(1);
        });
        it('should not return expired cached result', async () => {
            const shortTtlConfig = {
                ...defaultConfig,
                cacheTtl: 10, // Very short TTL
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', shortTtlConfig);
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to expire
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            // Should use fallback strategy2, not cached result (strategy1 only matches timeout)
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should evict oldest entry when cache is full', () => {
            const smallCacheConfig = {
                ...defaultConfig,
                maxCacheSize: 2,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', smallCacheConfig);
            // Fill cache to capacity
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            // Add one more to trigger eviction
            service.cacheResponse('test-provider', 'op3', { input: '3' }, { result: '3' });
            const stats = service.getCacheStats();
            expect(stats.size).toBe(2); // Should still be at max capacity
        });
        it('should clear provider cache', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearProviderCache('test-provider');
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
        it('should clear all cache', () => {
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('provider2', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearAllCache();
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after fallback execution', async () => {
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
            expect(metrics.averageExecutionTime).toBeGreaterThan(0);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('resetProviderMetrics', () => {
        beforeEach(async () => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
            // Generate some metrics
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
        });
        it('should reset metrics for specific provider', () => {
            // Verify metrics exist
            let metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            // Reset metrics
            service.resetProviderMetrics('test-provider');
            // Verify metrics are reset
            metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { cacheTtl: 10000, maxCacheSize: 200 };
            service.updateProviderConfig('test-provider', newConfig);
            const config = service.getProviderConfig('test-provider');
            expect(config.cacheTtl).toBe(10000);
            expect(config.maxCacheSize).toBe(200);
            expect(config.enableFallback).toBe(true); // Should keep existing values
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider configuration and metrics', () => {
            expect(service.getProviderConfig('test-provider')).toBeDefined();
            expect(service.getProviderMetrics('test-provider')).toBeDefined();
            service.unregisterProvider('test-provider');
            expect(service.getProviderConfig('test-provider')).toBeNull();
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
        it('should clear provider cache when unregistering', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            expect(service.getCacheStats().size).toBe(1);
            service.unregisterProvider('test-provider');
            expect(service.getCacheStats().size).toBe(0);
        });
    });
    describe('getCacheStats', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return cache statistics', async () => {
            // Add some cache entries and generate hits/misses
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            const originalError = new Error('original error');
            // Generate cache hit
            await service.executeFallback('test-provider', 'op1', originalError, {
                requestData: { input: '1' },
            });
            // Generate cache miss
            await service.executeFallback('test-provider', 'op2', originalError, {
                requestData: { input: '2' },
            });
            const stats = service.getCacheStats();
            expect(stats.size).toBeGreaterThan(0);
            expect(stats.totalHits).toBe(1);
            expect(stats.totalMisses).toBe(1);
            expect(stats.hitRate).toBe(0.5);
        });
    });
    describe('static factory methods', () => {
        it('should create default fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createDefaultConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.fallbackStrategies).toHaveLength(2);
            expect(config.degradationMode).toBe('graceful');
        });
        it('should create minimal fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createMinimalConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(false);
            expect(config.fallbackStrategies).toHaveLength(1);
            expect(config.degradationMode).toBe('minimal');
        });
        it('should create cached-only fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createCachedOnlyConfig(300000);
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.cacheTtl).toBe(300000);
            expect(config.fallbackStrategies).toHaveLength(0);
            expect(config.degradationMode).toBe('cached_only');
        });
    });
    describe('degradation modes', () => {
        it('should use stale cache in cached_only mode when no strategies work', async () => {
            const cachedOnlyConfig = {
                enableFallback: true,
                fallbackStrategies: [], // No strategies
                cacheEnabled: true,
                cacheTtl: 10, // Very short TTL to make it stale
                maxCacheSize: 100,
                degradationMode: 'cached_only',
            };
            service.registerProvider('test-provider', 'openai', cachedOnlyConfig);
            // Cache a response
            const response = { result: 'stale-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to become stale
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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