import { Injectable, Logger } from '@nestjs/common';

export interface FallbackConfig {
  enableFallback: boolean;
  fallbackStrategies: FallbackStrategy[];
  cacheEnabled: boolean;
  cacheTtl: number; // Cache TTL in milliseconds
  maxCacheSize: number;
  degradationMode: 'graceful' | 'minimal' | 'cached_only';
}

export interface FallbackStrategy {
  name: string;
  priority: number; // Lower number = higher priority
  condition: (error: Error, context: FallbackContext) => boolean;
  handler: (context: FallbackContext) => Promise<any> | any;
  timeout?: number;
}

export interface FallbackContext {
  providerId: string;
  operationType: string;
  originalError: Error;
  attempt: number;
  requestData?: any;
  metadata?: Record<string, any>;
}

export interface FallbackResult<T> {
  result: T;
  strategy: string;
  fromCache: boolean;
  degraded: boolean;
  executionTime: number;
}

export interface FallbackMetrics {
  providerId: string;
  totalFallbacks: number;
  strategyUsage: Record<string, number>;
  cacheHits: number;
  cacheMisses: number;
  averageExecutionTime: number;
  lastFallbackTime?: Date;
}

/**
 * AI-specific fallback service for graceful service degradation
 */
@Injectable()
export class AIFallbackService {
  private readonly logger = new Logger(AIFallbackService.name);
  private readonly configs = new Map<string, FallbackConfig>();
  private readonly cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly metrics = new Map<string, FallbackMetrics>();

  /**
   * Register fallback configuration for an AI provider
   */
  registerProvider(
    providerId: string,
    providerType: string,
    config: FallbackConfig
  ): void {
    this.configs.set(providerId, config);
    
    // Initialize metrics
    this.metrics.set(providerId, {
      providerId,
      totalFallbacks: 0,
      strategyUsage: {},
      cacheHits: 0,
      cacheMisses: 0,
      averageExecutionTime: 0,
    });

    this.logger.log(
      `Registered fallback configuration for ${providerType} provider ${providerId} with ${config.fallbackStrategies.length} strategies`
    );
  }

  /**
   * Execute fallback logic when primary operation fails
   */
  async executeFallback<T>(
    providerId: string,
    operationType: string,
    originalError: Error,
    context: Partial<FallbackContext> = {}
  ): Promise<T> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
    }

    if (!config.enableFallback) {
      throw originalError;
    }

    const fallbackContext: FallbackContext = {
      providerId,
      operationType,
      originalError,
      attempt: context.attempt || 1,
      requestData: context.requestData,
      metadata: context.metadata,
    };

    const startTime = Date.now();

    try {
      // Try cache first if enabled
      if (config.cacheEnabled) {
        const cachedResult = this.getCachedResult<T>(providerId, operationType, fallbackContext);
        if (cachedResult !== null) {
          this.updateMetrics(providerId, 'cache', Date.now() - startTime, true);
          return cachedResult;
        }
      }

      // Execute fallback strategies in priority order
      const sortedStrategies = [...config.fallbackStrategies].sort((a, b) => a.priority - b.priority);
      
      for (const strategy of sortedStrategies) {
        if (strategy.condition(originalError, fallbackContext)) {
          this.logger.warn(
            `Executing fallback strategy '${strategy.name}' for provider ${providerId} due to: ${originalError.message}`
          );

          try {
            const result = await this.executeStrategy<T>(strategy, fallbackContext);
            
            // Cache the result if caching is enabled
            if (config.cacheEnabled && result !== null && result !== undefined) {
              this.cacheResult(providerId, operationType, fallbackContext, result, config.cacheTtl);
            }

            this.updateMetrics(providerId, strategy.name, Date.now() - startTime, false);
            return result;
          } catch (strategyError) {
            this.logger.warn(
              `Fallback strategy '${strategy.name}' failed for provider ${providerId}: ${strategyError.message}`
            );
            continue;
          }
        }
      }

      // If no strategy worked, check degradation mode
      if (config.degradationMode === 'cached_only') {
        const staleResult = this.getStaleResult<T>(providerId, operationType, fallbackContext);
        if (staleResult !== null) {
          this.logger.warn(`Using stale cached result for provider ${providerId}`);
          this.updateMetrics(providerId, 'stale_cache', Date.now() - startTime, true);
          return staleResult;
        }
      }

      // All fallback strategies failed
      throw originalError;
    } catch (error) {
      this.logger.error(
        `All fallback strategies failed for provider ${providerId}`,
        error
      );
      throw error;
    }
  }

  /**
   * Execute fallback with detailed result information
   */
  async executeFallbackWithDetails<T>(
    providerId: string,
    operationType: string,
    originalError: Error,
    context: Partial<FallbackContext> = {}
  ): Promise<FallbackResult<T>> {
    const config = this.configs.get(providerId);
    if (!config) {
      throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
    }

    const startTime = Date.now();
    let fromCache = false;
    let strategy = 'none';
    let degraded = true;

    try {
      const result = await this.executeFallback<T>(providerId, operationType, originalError, context);
      
      // Determine which strategy was used based on metrics
      const metrics = this.metrics.get(providerId);
      if (metrics) {
        const lastStrategy = Object.keys(metrics.strategyUsage).reduce((a, b) => 
          metrics.strategyUsage[a] > metrics.strategyUsage[b] ? a : b
        );
        strategy = lastStrategy || 'unknown';
        fromCache = strategy === 'cache' || strategy === 'stale_cache';
      }

      return {
        result,
        strategy,
        fromCache,
        degraded,
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Add a cached response for future fallback use
   */
  cacheResponse<T>(
    providerId: string,
    operationType: string,
    requestData: any,
    response: T,
    ttl?: number
  ): void {
    const config = this.configs.get(providerId);
    if (!config || !config.cacheEnabled) {
      return;
    }

    const cacheKey = this.generateCacheKey(providerId, operationType, requestData);
    const cacheTtl = ttl || config.cacheTtl;
    
    // Check cache size limit and evict if necessary
    while (this.cache.size >= config.maxCacheSize) {
      this.evictOldestCacheEntry();
    }

    this.cache.set(cacheKey, {
      data: response,
      timestamp: Date.now(),
      ttl: cacheTtl,
    });

    this.logger.debug(`Cached response for ${providerId}:${operationType}`);
  }

  /**
   * Clear cache for a specific provider
   */
  clearProviderCache(providerId: string): number {
    let clearedCount = 0;
    
    for (const [key] of this.cache) {
      if (key.startsWith(`${providerId}:`)) {
        this.cache.delete(key);
        clearedCount++;
      }
    }

    if (clearedCount > 0) {
      this.logger.log(`Cleared ${clearedCount} cache entries for provider ${providerId}`);
    }

    return clearedCount;
  }

  /**
   * Clear all cache entries
   */
  clearAllCache(): number {
    const totalEntries = this.cache.size;
    this.cache.clear();
    
    if (totalEntries > 0) {
      this.logger.log(`Cleared ${totalEntries} cache entries`);
    }

    return totalEntries;
  }

  /**
   * Get fallback metrics for a specific provider
   */
  getProviderMetrics(providerId: string): FallbackMetrics | null {
    return this.metrics.get(providerId) || null;
  }

  /**
   * Get metrics for all registered providers
   */
  getAllProviderMetrics(): FallbackMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Reset metrics for a specific provider
   */
  resetProviderMetrics(providerId: string): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      this.metrics.set(providerId, {
        providerId,
        totalFallbacks: 0,
        strategyUsage: {},
        cacheHits: 0,
        cacheMisses: 0,
        averageExecutionTime: 0,
      });
      this.logger.log(`Reset fallback metrics for provider ${providerId}`);
    }
  }

  /**
   * Reset metrics for all providers
   */
  resetAllMetrics(): void {
    for (const [providerId] of this.metrics) {
      this.resetProviderMetrics(providerId);
    }
    this.logger.log('Reset fallback metrics for all providers');
  }

  /**
   * Update provider configuration
   */
  updateProviderConfig(providerId: string, config: Partial<FallbackConfig>): void {
    const existingConfig = this.configs.get(providerId);
    if (existingConfig) {
      const updatedConfig = { ...existingConfig, ...config };
      this.configs.set(providerId, updatedConfig);
      
      this.logger.log(`Updated fallback configuration for provider ${providerId}`);
    }
  }

  /**
   * Remove a provider's fallback configuration
   */
  unregisterProvider(providerId: string): void {
    this.clearProviderCache(providerId);
    this.configs.delete(providerId);
    this.metrics.delete(providerId);
    this.logger.log(`Unregistered fallback configuration for provider ${providerId}`);
  }

  /**
   * Get provider configuration
   */
  getProviderConfig(providerId: string): FallbackConfig | null {
    return this.configs.get(providerId) || null;
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number; totalHits: number; totalMisses: number } {
    let totalHits = 0;
    let totalMisses = 0;

    for (const metrics of this.metrics.values()) {
      totalHits += metrics.cacheHits;
      totalMisses += metrics.cacheMisses;
    }

    const hitRate = totalHits + totalMisses > 0 ? totalHits / (totalHits + totalMisses) : 0;

    return {
      size: this.cache.size,
      hitRate,
      totalHits,
      totalMisses,
    };
  }

  /**
   * Execute a fallback strategy
   */
  private async executeStrategy<T>(
    strategy: FallbackStrategy,
    context: FallbackContext
  ): Promise<T> {
    if (strategy.timeout) {
      return Promise.race([
        Promise.resolve(strategy.handler(context)),
        new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Fallback strategy '${strategy.name}' timed out after ${strategy.timeout}ms`));
          }, strategy.timeout);
        }),
      ]);
    }

    return Promise.resolve(strategy.handler(context));
  }

  /**
   * Get cached result if available and not expired
   */
  private getCachedResult<T>(
    providerId: string,
    operationType: string,
    context: FallbackContext
  ): T | null {
    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      this.logger.debug(`Cache hit for ${providerId}:${operationType}`);
      return cached.data;
    }

    const config = this.configs.get(providerId);
    if (cached && config?.degradationMode !== 'cached_only') {
      // Remove expired entry only if not in cached_only mode
      this.cache.delete(cacheKey);
    }

    return null;
  }

  /**
   * Get stale cached result (expired but still available)
   */
  private getStaleResult<T>(
    providerId: string,
    operationType: string,
    context: FallbackContext
  ): T | null {
    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
    const cached = this.cache.get(cacheKey);

    if (cached) {
      this.logger.debug(`Using stale cache for ${providerId}:${operationType}`);
      return cached.data;
    }

    return null;
  }

  /**
   * Cache a result
   */
  private cacheResult<T>(
    providerId: string,
    operationType: string,
    context: FallbackContext,
    result: T,
    ttl: number
  ): void {
    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
    
    this.cache.set(cacheKey, {
      data: result,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(providerId: string, operationType: string, requestData?: any): string {
    const dataHash = requestData ? JSON.stringify(requestData) : '';
    return `${providerId}:${operationType}:${Buffer.from(dataHash).toString('base64')}`;
  }

  /**
   * Evict oldest cache entry when cache is full
   */
  private evictOldestCacheEntry(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Update fallback metrics
   */
  private updateMetrics(
    providerId: string,
    strategy: string,
    executionTime: number,
    fromCache: boolean
  ): void {
    const metrics = this.metrics.get(providerId);
    if (metrics) {
      metrics.totalFallbacks++;
      metrics.strategyUsage[strategy] = (metrics.strategyUsage[strategy] || 0) + 1;
      
      if (fromCache) {
        metrics.cacheHits++;
      } else {
        metrics.cacheMisses++;
      }
      
      metrics.averageExecutionTime = 
        (metrics.averageExecutionTime * (metrics.totalFallbacks - 1) + executionTime) / 
        metrics.totalFallbacks;
      
      metrics.lastFallbackTime = new Date();
      
      this.metrics.set(providerId, metrics);
    }
  }

  /**
   * Create predefined fallback configurations
   */
  static createDefaultConfig(): FallbackConfig {
    return {
      enableFallback: true,
      fallbackStrategies: [
        {
          name: 'cached_response',
          priority: 1,
          condition: () => true,
          handler: () => null, // Will be handled by cache logic
        },
        {
          name: 'default_response',
          priority: 10,
          condition: () => true,
          handler: () => ({ message: 'Service temporarily unavailable', status: 'fallback' }),
        },
      ],
      cacheEnabled: true,
      cacheTtl: 300000, // 5 minutes
      maxCacheSize: 1000,
      degradationMode: 'graceful',
    };
  }

  static createMinimalConfig(): FallbackConfig {
    return {
      enableFallback: true,
      fallbackStrategies: [
        {
          name: 'minimal_response',
          priority: 1,
          condition: () => true,
          handler: () => ({ error: 'Service unavailable' }),
        },
      ],
      cacheEnabled: false,
      cacheTtl: 0,
      maxCacheSize: 0,
      degradationMode: 'minimal',
    };
  }

  static createCachedOnlyConfig(cacheTtl: number = 600000): FallbackConfig {
    return {
      enableFallback: true,
      fallbackStrategies: [],
      cacheEnabled: true,
      cacheTtl,
      maxCacheSize: 500,
      degradationMode: 'cached_only',
    };
  }
}