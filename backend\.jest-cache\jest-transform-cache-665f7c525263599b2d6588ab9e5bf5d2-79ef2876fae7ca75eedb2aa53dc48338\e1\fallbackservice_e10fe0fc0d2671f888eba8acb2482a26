28fc91fa99954bf0232d24b6743e9c7a
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AIFallbackService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIFallbackService = void 0;
const common_1 = require("@nestjs/common");
/**
 * AI-specific fallback service for graceful service degradation
 */
let AIFallbackService = AIFallbackService_1 = class AIFallbackService {
    constructor() {
        this.logger = new common_1.Logger(AIFallbackService_1.name);
        this.configs = new Map();
        this.cache = new Map();
        this.metrics = new Map();
    }
    /**
     * Register fallback configuration for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        this.configs.set(providerId, config);
        // Initialize metrics
        this.metrics.set(providerId, {
            providerId,
            totalFallbacks: 0,
            strategyUsage: {},
            cacheHits: 0,
            cacheMisses: 0,
            averageExecutionTime: 0,
        });
        this.logger.log(`Registered fallback configuration for ${providerType} provider ${providerId} with ${config.fallbackStrategies.length} strategies`);
    }
    /**
     * Execute fallback logic when primary operation fails
     */
    async executeFallback(providerId, operationType, originalError, context = {}) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
        }
        if (!config.enableFallback) {
            throw originalError;
        }
        const fallbackContext = {
            providerId,
            operationType,
            originalError,
            attempt: context.attempt || 1,
            requestData: context.requestData,
            metadata: context.metadata,
        };
        const startTime = Date.now();
        try {
            // Try cache first if enabled
            if (config.cacheEnabled) {
                const cachedResult = this.getCachedResult(providerId, operationType, fallbackContext);
                if (cachedResult !== null) {
                    this.updateMetrics(providerId, 'cache', Date.now() - startTime, true);
                    return cachedResult;
                }
            }
            // Execute fallback strategies in priority order
            const sortedStrategies = [...config.fallbackStrategies].sort((a, b) => a.priority - b.priority);
            for (const strategy of sortedStrategies) {
                if (strategy.condition(originalError, fallbackContext)) {
                    this.logger.warn(`Executing fallback strategy '${strategy.name}' for provider ${providerId} due to: ${originalError.message}`);
                    try {
                        const result = await this.executeStrategy(strategy, fallbackContext);
                        // Cache the result if caching is enabled
                        if (config.cacheEnabled && result !== null && result !== undefined) {
                            this.cacheResult(providerId, operationType, fallbackContext, result, config.cacheTtl);
                        }
                        this.updateMetrics(providerId, strategy.name, Date.now() - startTime, false);
                        return result;
                    }
                    catch (strategyError) {
                        this.logger.warn(`Fallback strategy '${strategy.name}' failed for provider ${providerId}: ${strategyError.message}`);
                        continue;
                    }
                }
            }
            // If no strategy worked, check degradation mode
            if (config.degradationMode === 'cached_only') {
                const staleResult = this.getStaleResult(providerId, operationType, fallbackContext);
                if (staleResult !== null) {
                    this.logger.warn(`Using stale cached result for provider ${providerId}`);
                    this.updateMetrics(providerId, 'stale_cache', Date.now() - startTime, true);
                    return staleResult;
                }
            }
            // All fallback strategies failed
            throw originalError;
        }
        catch (error) {
            this.logger.error(`All fallback strategies failed for provider ${providerId}`, error);
            throw error;
        }
    }
    /**
     * Execute fallback with detailed result information
     */
    async executeFallbackWithDetails(providerId, operationType, originalError, context = {}) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
        }
        const startTime = Date.now();
        let fromCache = false;
        let strategy = 'none';
        let degraded = true;
        try {
            const result = await this.executeFallback(providerId, operationType, originalError, context);
            // Determine which strategy was used based on metrics
            const metrics = this.metrics.get(providerId);
            if (metrics) {
                const lastStrategy = Object.keys(metrics.strategyUsage).reduce((a, b) => metrics.strategyUsage[a] > metrics.strategyUsage[b] ? a : b);
                strategy = lastStrategy || 'unknown';
                fromCache = strategy === 'cache' || strategy === 'stale_cache';
            }
            return {
                result,
                strategy,
                fromCache,
                degraded,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * Add a cached response for future fallback use
     */
    cacheResponse(providerId, operationType, requestData, response, ttl) {
        const config = this.configs.get(providerId);
        if (!config || !config.cacheEnabled) {
            return;
        }
        const cacheKey = this.generateCacheKey(providerId, operationType, requestData);
        const cacheTtl = ttl || config.cacheTtl;
        // Check cache size limit and evict if necessary
        while (this.cache.size >= config.maxCacheSize) {
            this.evictOldestCacheEntry();
        }
        this.cache.set(cacheKey, {
            data: response,
            timestamp: Date.now(),
            ttl: cacheTtl,
        });
        this.logger.debug(`Cached response for ${providerId}:${operationType}`);
    }
    /**
     * Clear cache for a specific provider
     */
    clearProviderCache(providerId) {
        let clearedCount = 0;
        for (const [key] of this.cache) {
            if (key.startsWith(`${providerId}:`)) {
                this.cache.delete(key);
                clearedCount++;
            }
        }
        if (clearedCount > 0) {
            this.logger.log(`Cleared ${clearedCount} cache entries for provider ${providerId}`);
        }
        return clearedCount;
    }
    /**
     * Clear all cache entries
     */
    clearAllCache() {
        const totalEntries = this.cache.size;
        this.cache.clear();
        if (totalEntries > 0) {
            this.logger.log(`Cleared ${totalEntries} cache entries`);
        }
        return totalEntries;
    }
    /**
     * Get fallback metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        return this.metrics.get(providerId) || null;
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        return Array.from(this.metrics.values());
    }
    /**
     * Reset metrics for a specific provider
     */
    resetProviderMetrics(providerId) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            this.metrics.set(providerId, {
                providerId,
                totalFallbacks: 0,
                strategyUsage: {},
                cacheHits: 0,
                cacheMisses: 0,
                averageExecutionTime: 0,
            });
            this.logger.log(`Reset fallback metrics for provider ${providerId}`);
        }
    }
    /**
     * Reset metrics for all providers
     */
    resetAllMetrics() {
        for (const [providerId] of this.metrics) {
            this.resetProviderMetrics(providerId);
        }
        this.logger.log('Reset fallback metrics for all providers');
    }
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            this.logger.log(`Updated fallback configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's fallback configuration
     */
    unregisterProvider(providerId) {
        this.clearProviderCache(providerId);
        this.configs.delete(providerId);
        this.metrics.delete(providerId);
        this.logger.log(`Unregistered fallback configuration for provider ${providerId}`);
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerId) {
        return this.configs.get(providerId) || null;
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        let totalHits = 0;
        let totalMisses = 0;
        for (const metrics of this.metrics.values()) {
            totalHits += metrics.cacheHits;
            totalMisses += metrics.cacheMisses;
        }
        const hitRate = totalHits + totalMisses > 0 ? totalHits / (totalHits + totalMisses) : 0;
        return {
            size: this.cache.size,
            hitRate,
            totalHits,
            totalMisses,
        };
    }
    /**
     * Execute a fallback strategy
     */
    async executeStrategy(strategy, context) {
        if (strategy.timeout) {
            return Promise.race([
                Promise.resolve(strategy.handler(context)),
                new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error(`Fallback strategy '${strategy.name}' timed out after ${strategy.timeout}ms`));
                    }, strategy.timeout);
                }),
            ]);
        }
        return Promise.resolve(strategy.handler(context));
    }
    /**
     * Get cached result if available and not expired
     */
    getCachedResult(providerId, operationType, context) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
            this.logger.debug(`Cache hit for ${providerId}:${operationType}`);
            return cached.data;
        }
        if (cached) {
            // Remove expired entry
            this.cache.delete(cacheKey);
        }
        return null;
    }
    /**
     * Get stale cached result (expired but still available)
     */
    getStaleResult(providerId, operationType, context) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        const cached = this.cache.get(cacheKey);
        if (cached) {
            this.logger.debug(`Using stale cache for ${providerId}:${operationType}`);
            return cached.data;
        }
        return null;
    }
    /**
     * Cache a result
     */
    cacheResult(providerId, operationType, context, result, ttl) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            ttl,
        });
    }
    /**
     * Generate cache key
     */
    generateCacheKey(providerId, operationType, requestData) {
        const dataHash = requestData ? JSON.stringify(requestData) : '';
        return `${providerId}:${operationType}:${Buffer.from(dataHash).toString('base64')}`;
    }
    /**
     * Evict oldest cache entry when cache is full
     */
    evictOldestCacheEntry() {
        let oldestKey = null;
        let oldestTimestamp = Date.now();
        for (const [key, entry] of this.cache) {
            if (entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
    /**
     * Update fallback metrics
     */
    updateMetrics(providerId, strategy, executionTime, fromCache) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalFallbacks++;
            metrics.strategyUsage[strategy] = (metrics.strategyUsage[strategy] || 0) + 1;
            if (fromCache) {
                metrics.cacheHits++;
            }
            else {
                metrics.cacheMisses++;
            }
            metrics.averageExecutionTime =
                (metrics.averageExecutionTime * (metrics.totalFallbacks - 1) + executionTime) /
                    metrics.totalFallbacks;
            metrics.lastFallbackTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Create predefined fallback configurations
     */
    static createDefaultConfig() {
        return {
            enableFallback: true,
            fallbackStrategies: [
                {
                    name: 'cached_response',
                    priority: 1,
                    condition: () => true,
                    handler: () => null, // Will be handled by cache logic
                },
                {
                    name: 'default_response',
                    priority: 10,
                    condition: () => true,
                    handler: () => ({ message: 'Service temporarily unavailable', status: 'fallback' }),
                },
            ],
            cacheEnabled: true,
            cacheTtl: 300000, // 5 minutes
            maxCacheSize: 1000,
            degradationMode: 'graceful',
        };
    }
    static createMinimalConfig() {
        return {
            enableFallback: true,
            fallbackStrategies: [
                {
                    name: 'minimal_response',
                    priority: 1,
                    condition: () => true,
                    handler: () => ({ error: 'Service unavailable' }),
                },
            ],
            cacheEnabled: false,
            cacheTtl: 0,
            maxCacheSize: 0,
            degradationMode: 'minimal',
        };
    }
    static createCachedOnlyConfig(cacheTtl = 600000) {
        return {
            enableFallback: true,
            fallbackStrategies: [],
            cacheEnabled: true,
            cacheTtl,
            maxCacheSize: 500,
            degradationMode: 'cached_only',
        };
    }
};
exports.AIFallbackService = AIFallbackService;
exports.AIFallbackService = AIFallbackService = AIFallbackService_1 = __decorate([
    (0, common_1.Injectable)()
], AIFallbackService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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