{"name": "xml-crypto", "version": "2.1.6", "description": "Xml digital signature and encryption library for Node.js", "engines": {"node": ">=0.4.0"}, "author": "<PERSON><PERSON> Naveh <<EMAIL>> (http://webservices20.blogspot.com/)", "contributors": ["LoneRifle <<EMAIL>>"], "dependencies": {"@xmldom/xmldom": "^0.7.9", "xpath": "0.0.32"}, "devDependencies": {"ejs": "2.6.1", "nodeunit": "^0.11.3"}, "repository": {"type": "git", "url": "https://github.com/yaronn/xml-crypto.git"}, "main": "./index.js", "directories": {"lib": "./lib"}, "keywords": ["xml", "digital signature", "xml encryption", "x.509 certificate"], "license": "MIT", "scripts": {"test": "nodeunit ./test/canonicalization-unit-tests.js ./test/c14nWithComments-unit-tests.js ./test/signature-unit-tests.js ./test/saml-response-test.js ./test/signature-integration-tests.js ./test/document-test.js ./test/wsfed-metadata-test.js ./test/hmac-tests.js ./test/c14n-non-exclusive-unit-test.js"}}