2e0abf34d0735da540a600aa5b75029e
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const fallback_service_1 = require("../fallback.service");
describe('AIFallbackService', () => {
    let service;
    const mockStrategy1 = {
        name: 'strategy1',
        priority: 1,
        condition: (error) => error.message.includes('timeout'),
        handler: () => ({ result: 'fallback1' }),
    };
    const mockStrategy2 = {
        name: 'strategy2',
        priority: 2,
        condition: () => true,
        handler: () => ({ result: 'fallback2' }),
    };
    const defaultConfig = {
        enableFallback: true,
        fallbackStrategies: [mockStrategy1, mockStrategy2],
        cacheEnabled: true,
        cacheTtl: 5000,
        maxCacheSize: 100,
        degradationMode: 'graceful',
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [fallback_service_1.AIFallbackService],
        }).compile();
        service = module.get(fallback_service_1.AIFallbackService);
    });
    afterEach(() => {
        // Clean up all registered providers and cache
        service.clearAllCache();
        service.resetAllMetrics();
    });
    describe('registerProvider', () => {
        it('should register a new provider with fallback configuration', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const config = service.getProviderConfig(providerId);
            expect(config).toEqual(defaultConfig);
        });
        it('should initialize metrics for registered provider', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const metrics = service.getProviderMetrics(providerId);
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe(providerId);
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('executeFallback', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should throw original error when fallback is disabled', async () => {
            const disabledConfig = {
                ...defaultConfig,
                enableFallback: false,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', disabledConfig);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should execute fallback strategy based on condition', async () => {
            const timeoutError = new Error('timeout occurred');
            const result = await service.executeFallback('test-provider', 'test-operation', timeoutError);
            expect(result).toEqual({ result: 'fallback1' });
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
        });
        it('should execute fallback strategy in priority order', async () => {
            const genericError = new Error('generic error');
            const result = await service.executeFallback('test-provider', 'test-operation', genericError);
            // Should use strategy1 first (lower priority number)
            expect(result).toEqual({ result: 'fallback1' });
        });
        it('should fall back to next strategy if first one fails', async () => {
            const failingStrategy = {
                name: 'failing-strategy',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy failed'); },
            };
            const configWithFailingStrategy = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should throw original error if all strategies fail', async () => {
            const failingStrategy1 = {
                name: 'failing-strategy1',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy1 failed'); },
            };
            const failingStrategy2 = {
                name: 'failing-strategy2',
                priority: 2,
                condition: () => true,
                handler: () => { throw new Error('strategy2 failed'); },
            };
            const configWithFailingStrategies = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy1, failingStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategies);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should throw error for unregistered provider', async () => {
            const originalError = new Error('original error');
            await expect(service.executeFallback('unknown-provider', 'test-operation', originalError)).rejects.toThrow('Fallback configuration not registered for provider: unknown-provider');
        });
        it('should handle async strategy handlers', async () => {
            const asyncStrategy = {
                name: 'async-strategy',
                priority: 1,
                condition: () => true,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 10));
                    return { result: 'async-fallback' };
                },
            };
            const configWithAsyncStrategy = {
                ...defaultConfig,
                fallbackStrategies: [asyncStrategy],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithAsyncStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'async-fallback' });
        });
        it('should handle strategy timeout', async () => {
            const slowStrategy = {
                name: 'slow-strategy',
                priority: 1,
                condition: () => true,
                timeout: 50,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    return { result: 'slow-fallback' };
                },
            };
            const configWithSlowStrategy = {
                ...defaultConfig,
                fallbackStrategies: [slowStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithSlowStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            // Should fall back to strategy2 after strategy1 times out
            expect(result).toEqual({ result: 'fallback2' });
        });
    });
    describe('executeFallbackWithDetails', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return detailed fallback information', async () => {
            const originalError = new Error('timeout occurred');
            const result = await service.executeFallbackWithDetails('test-provider', 'test-operation', originalError);
            expect(result.result).toEqual({ result: 'fallback1' });
            expect(result.strategy).toBe('strategy1');
            expect(result.fromCache).toBe(false);
            expect(result.degraded).toBe(true);
            expect(result.executionTime).toBeGreaterThan(0);
        });
    });
    describe('caching', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should cache response when caching is enabled', () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Verify cache stats
            const stats = service.getCacheStats();
            expect(stats.size).toBe(1);
        });
        it('should return cached result when available', async () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.cacheHits).toBe(1);
        });
        it('should not return expired cached result', async () => {
            const shortTtlConfig = {
                ...defaultConfig,
                cacheTtl: 10, // Very short TTL
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', shortTtlConfig);
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to expire
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            // Should use fallback strategy, not cached result
            expect(result).toEqual({ result: 'fallback1' });
        });
        it('should evict oldest entry when cache is full', () => {
            const smallCacheConfig = {
                ...defaultConfig,
                maxCacheSize: 2,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', smallCacheConfig);
            // Fill cache to capacity
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            // Add one more to trigger eviction
            service.cacheResponse('test-provider', 'op3', { input: '3' }, { result: '3' });
            const stats = service.getCacheStats();
            expect(stats.size).toBe(2); // Should still be at max capacity
        });
        it('should clear provider cache', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearProviderCache('test-provider');
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
        it('should clear all cache', () => {
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('provider2', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearAllCache();
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after fallback execution', async () => {
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
            expect(metrics.averageExecutionTime).toBeGreaterThan(0);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('resetProviderMetrics', () => {
        beforeEach(async () => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
            // Generate some metrics
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
        });
        it('should reset metrics for specific provider', () => {
            // Verify metrics exist
            let metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            // Reset metrics
            service.resetProviderMetrics('test-provider');
            // Verify metrics are reset
            metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { cacheTtl: 10000, maxCacheSize: 200 };
            service.updateProviderConfig('test-provider', newConfig);
            const config = service.getProviderConfig('test-provider');
            expect(config.cacheTtl).toBe(10000);
            expect(config.maxCacheSize).toBe(200);
            expect(config.enableFallback).toBe(true); // Should keep existing values
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider configuration and metrics', () => {
            expect(service.getProviderConfig('test-provider')).toBeDefined();
            expect(service.getProviderMetrics('test-provider')).toBeDefined();
            service.unregisterProvider('test-provider');
            expect(service.getProviderConfig('test-provider')).toBeNull();
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
        it('should clear provider cache when unregistering', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            expect(service.getCacheStats().size).toBe(1);
            service.unregisterProvider('test-provider');
            expect(service.getCacheStats().size).toBe(0);
        });
    });
    describe('getCacheStats', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return cache statistics', async () => {
            // Add some cache entries and generate hits/misses
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            const originalError = new Error('original error');
            // Generate cache hit
            await service.executeFallback('test-provider', 'op1', originalError, {
                requestData: { input: '1' },
            });
            // Generate cache miss
            await service.executeFallback('test-provider', 'op2', originalError, {
                requestData: { input: '2' },
            });
            const stats = service.getCacheStats();
            expect(stats.size).toBeGreaterThan(0);
            expect(stats.totalHits).toBe(1);
            expect(stats.totalMisses).toBe(1);
            expect(stats.hitRate).toBe(0.5);
        });
    });
    describe('static factory methods', () => {
        it('should create default fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createDefaultConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.fallbackStrategies).toHaveLength(2);
            expect(config.degradationMode).toBe('graceful');
        });
        it('should create minimal fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createMinimalConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(false);
            expect(config.fallbackStrategies).toHaveLength(1);
            expect(config.degradationMode).toBe('minimal');
        });
        it('should create cached-only fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createCachedOnlyConfig(300000);
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.cacheTtl).toBe(300000);
            expect(config.fallbackStrategies).toHaveLength(0);
            expect(config.degradationMode).toBe('cached_only');
        });
    });
    describe('degradation modes', () => {
        it('should use stale cache in cached_only mode when no strategies work', async () => {
            const cachedOnlyConfig = {
                enableFallback: true,
                fallbackStrategies: [], // No strategies
                cacheEnabled: true,
                cacheTtl: 10, // Very short TTL to make it stale
                maxCacheSize: 100,
                degradationMode: 'cached_only',
            };
            service.registerProvider('test-provider', 'openai', cachedOnlyConfig);
            // Cache a response
            const response = { result: 'stale-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to become stale
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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