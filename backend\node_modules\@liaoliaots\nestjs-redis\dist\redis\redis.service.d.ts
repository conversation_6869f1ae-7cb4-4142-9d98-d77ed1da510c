import type { Redis } from 'ioredis';
import { RedisClients } from './interfaces';
import { Namespace } from '../interfaces';
/**
 * Manager for redis connections.
 */
export declare class RedisService {
    private readonly clients;
    constructor(clients: RedisClients);
    /**
     * Retrieves a redis connection by namespace.
     * However, if the query does not find a connection, it returns ClientNotFoundError: No Connection found error.
     *
     * @param namespace - The namespace
     * @returns A redis connection
     */
    getOrThrow(namespace?: Namespace): Redis;
    /**
     * Retrieves a redis connection by namespace, if the query does not find a connection, it returns `null`;
     *
     * @param namespace - The namespace
     * @returns A redis connection or nil
     */
    getOrNil(namespace?: Namespace): Redis | null;
}
