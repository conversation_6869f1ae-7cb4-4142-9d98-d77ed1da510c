import type { Cluster } from 'ioredis';
import { ClusterClients } from './interfaces';
import { Namespace } from '../interfaces';
/**
 * Manager for cluster connections.
 */
export declare class ClusterService {
    private readonly clients;
    constructor(clients: ClusterClients);
    /**
     * Retrieves a cluster connection by namespace.
     * However, if the query does not find a connection, it returns ClientNotFoundError: No Connection found error.
     *
     * @param namespace - The namespace
     * @returns A cluster connection
     */
    getOrThrow(namespace?: Namespace): Cluster;
    /**
     * Retrieves a cluster connection by namespace, if the query does not find a connection, it returns `null`;
     *
     * @param namespace - The namespace
     * @returns A cluster connection or nil
     */
    getOrNil(namespace?: Namespace): Cluster | null;
}
