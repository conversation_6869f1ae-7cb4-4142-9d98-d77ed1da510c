!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.circuitBreaker=t():e.circuitBreaker=t()}(self,(()=>{return e={7:e=>{"use strict";var t,n="object"==typeof Reflect?Reflect:null,o=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var r=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise((function(n,o){function r(n){e.removeListener(t,i),o(n)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",r),n([].slice.call(arguments))}m(e,t,i,{once:!0}),"error"!==t&&function(e,t){"function"==typeof e.on&&m(e,"error",t,{once:!0})}(e,r)}))},i.EventEmitter=i,i.prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var s=10;function c(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function a(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function u(e,t,n,o){var r,i,s,u;if(c(n),void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),s=i[t]),void 0===s)s=i[t]=n,++e._eventsCount;else if("function"==typeof s?s=i[t]=o?[n,s]:[s,n]:o?s.unshift(n):s.push(n),(r=a(e))>0&&s.length>r&&!s.warned){s.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=s.length,u=l,console&&console.warn&&console.warn(u)}return e}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function f(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=l.bind(o);return r.listener=n,o.wrapFn=r,r}function p(e,t,n){var o=e._events;if(void 0===o)return[];var r=o[t];return void 0===r?[]:"function"==typeof r?n?[r.listener||r]:[r]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(r):y(r,r.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function y(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}function m(e,t,n,o){if("function"==typeof e.on)o.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function r(i){o.once&&e.removeEventListener(t,r),n(i)}))}}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||r(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),i.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||r(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return a(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var s;if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var c=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw c.context=s,c}var a=i[e];if(void 0===a)return!1;if("function"==typeof a)o(a,this,t);else{var u=a.length,l=y(a,u);for(n=0;n<u;++n)o(l[n],this,t)}return!0},i.prototype.addListener=function(e,t){return u(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return u(this,e,t,!0)},i.prototype.once=function(e,t){return c(t),this.on(e,f(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,f(this,e,t)),this},i.prototype.removeListener=function(e,t){var n,o,r,i,s;if(c(t),void 0===(o=this._events))return this;if(void 0===(n=o[e]))return this;if(n===t||n.listener===t)0===--this._eventsCount?this._events=Object.create(null):(delete o[e],o.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(r=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){s=n[i].listener,r=i;break}if(r<0)return this;0===r?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,r),1===n.length&&(o[e]=n[0]),void 0!==o.removeListener&&this.emit("removeListener",e,s||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,n,o;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0===--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var r,i=Object.keys(n);for(o=0;o<i.length;++o)"removeListener"!==(r=i[o])&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},i.prototype.listeners=function(e){return p(this,e,!0)},i.prototype.rawListeners=function(e){return p(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},i.prototype.listenerCount=h,i.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},158:(e,t)=>{"use strict";e.exports=function(e){var t=[],n=e,o={take:r,release:i,test:function(){return!(n<1)&&r()&&!0}};return Object.defineProperty(o,"count",{get:function(e){return n},enumerable:!0}),o;function r(e){return n>0?(--n,Promise.resolve(i)):new Promise((function(o,r){t.push((function(e){--n,o(i)})),e&&setTimeout((function(n){t.shift();var o=new Error("Timed out after ".concat(e,"ms"));o.code="ETIMEDOUT",r(o)}),e)}))}function i(){n++,t.length>0&&t.shift()()}}},299:(e,t,n)=>{"use strict";function o(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function s(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,c(o.key),o)}}function c(e){var t=function(e){if("object"!=i(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==i(t)?t:t+""}function a(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(a=function(){return!!e})()}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},l(e,t)}var f=n(7),p=n(676),h=n(158),y=n(310),m=Symbol("state"),v=Symbol("open"),b=Symbol("closed"),d=Symbol("half-open"),w=Symbol("pending-close"),g=Symbol("shutdown"),T=Symbol("fallback"),k=Symbol("status"),O=Symbol("name"),C=Symbol("group"),S=Symbol("Enabled"),j=Symbol("warming-up"),x=Symbol("volume-threshold"),E=Symbol("our-error"),P=Symbol("reset-timeout"),L=Symbol("warmup-timeout"),_=Symbol("last-timer-at"),A=function(e){function t(e){var n,o,r,s,c,l,f,E,A,B,R=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(B=function(e,t,n){return t=u(t),function(e,t){if(t&&("object"==i(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,a()?Reflect.construct(t,n||[],u(e).constructor):t.apply(e,n))}(this,t)).options=R,B.options.timeout=null!==(n=R.timeout)&&void 0!==n?n:1e4,B.options.resetTimeout=null!==(o=R.resetTimeout)&&void 0!==o?o:3e4,B.options.errorThresholdPercentage=null!==(r=R.errorThresholdPercentage)&&void 0!==r?r:50,B.options.rollingCountTimeout=null!==(s=R.rollingCountTimeout)&&void 0!==s?s:1e4,B.options.rollingCountBuckets=null!==(c=R.rollingCountBuckets)&&void 0!==c?c:10,B.options.rollingPercentilesEnabled=!1!==R.rollingPercentilesEnabled,B.options.capacity=Number.isInteger(R.capacity)?R.capacity:Number.MAX_SAFE_INTEGER,B.options.errorFilter=R.errorFilter||function(e){return!1},B.options.cacheTTL=null!==(l=R.cacheTTL)&&void 0!==l?l:0,B.options.cacheGetKey=null!==(f=R.cacheGetKey)&&void 0!==f?f:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return JSON.stringify(t)},B.options.enableSnapshots=!1!==R.enableSnapshots,B.options.rotateBucketController=R.rotateBucketController,B.options.coalesce=!!R.coalesce,B.options.coalesceTTL=null!==(E=R.coalesceTTL)&&void 0!==E?E:B.options.timeout,B.options.coalesceResetOn=(null===(A=R.coalesceResetOn)||void 0===A?void 0:A.filter((function(e){return["error","success","timeout"].includes(e)})))||[],B.options.cache)if(void 0===B.options.cacheTransport)B.options.cacheTransport=new y(R.cacheSize);else if("object"!==i(B.options.cacheTransport)||!B.options.cacheTransport.get||!B.options.cacheTransport.set||!B.options.cacheTransport.flush)throw new TypeError("options.cacheTransport should be an object with `get`, `set` and `flush` methods");if(B.options.coalesce&&(B.options.coalesceCache=new y(R.coalesceSize)),B.semaphore=new h(B.options.capacity),!e)throw new TypeError("No action provided. Cannot construct a CircuitBreaker without an invocable action.");if(R.autoRenewAbortController&&!R.abortController&&(R.abortController=new AbortController),R.abortController&&"function"!=typeof R.abortController.abort)throw new TypeError("AbortController does not contain `abort()` method");if(B[x]=Number.isInteger(R.volumeThreshold)?R.volumeThreshold:0,B[j]=!0===R.allowWarmUp,B.options.status?B.options.status instanceof p?B[k]=B.options.status:B[k]=new p({stats:B.options.status}):B[k]=new p(B.options),B[m]=b,R.state?(B[S]=!1!==R.state.enabled,B[j]=R.state.warmUp||B[j],B[b]=!1!==R.state.closed,B[d]=B[w]=R.state.halfOpen||!1,B[v]=!B[b]&&!B[d],B[g]=R.state.shutdown||!1):(B[w]=!1,B[S]=!1!==R.enabled),B[T]=null,B[O]=R.name||e.name||N(),B[C]=R.group||B[O],B[j]){var M=B[L]=setTimeout((function(e){return B[j]=!1}),B.options.rollingCountTimeout);"function"==typeof M.unref&&M.unref()}B.action="function"!=typeof e?function(t){return Promise.resolve(e)}:e,R.maxFailures&&console.error("options.maxFailures is deprecated. Please use options.errorThresholdPercentage");var D,I=function(e){return function(t,n){return B[k].increment(e,n)}};function F(e){e[m]=d,e[w]=!0,e._renewAbortControllerIfNeeded(),e.emit("halfOpen",e.options.resetTimeout)}return B.on("success",I("successes")),B.on("failure",I("failures")),B.on("fallback",I("fallbacks")),B.on("timeout",I("timeouts")),B.on("fire",I("fires")),B.on("reject",I("rejects")),B.on("cacheHit",I("cacheHits")),B.on("cacheMiss",I("cacheMisses")),B.on("coalesceCacheHit",I("coalesceCacheHits")),B.on("coalesceCacheMiss",I("coalesceCacheMisses")),B.on("open",(function(e){return B[k].open()})),B.on("close",(function(e){return B[k].close()})),B.on("semaphoreLocked",I("semaphoreRejections")),B.on("open",((D=B)[_]=Date.now(),function(e){var t=D[P]=setTimeout((function(){F(D)}),D.options.resetTimeout);"function"==typeof t.unref&&t.unref()})),B.on("success",(function(e){B.halfOpen&&B.close()})),B[g]?(B[m]=g,B.shutdown()):B[b]?B.close():B[v]?void 0!==B.options.state.lastTimerAt&&Date.now()-B.options.state.lastTimerAt>B.options.resetTimeout?F(B):B.open():B[d]&&(B[m]=d),B}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,e),n=t,o=[{key:"_renewAbortControllerIfNeeded",value:function(){this.options.autoRenewAbortController&&this.options.abortController&&this.options.abortController.signal.aborted&&(this.options.abortController=new AbortController)}},{key:"close",value:function(){this[m]!==b&&(this[P]&&clearTimeout(this[P]),this[m]=b,this[w]=!1,this._renewAbortControllerIfNeeded(),this.emit("close"))}},{key:"open",value:function(){this[m]!==v&&(this[m]=v,this[w]=!1,this.emit("open"))}},{key:"shutdown",value:function(){this.emit("shutdown"),this.disable(),this.removeAllListeners(),this[P]&&clearTimeout(this[P]),this[L]&&clearTimeout(this[L]),this.status.shutdown(),this[m]=g,this.clearCache()}},{key:"isShutdown",get:function(){return this[m]===g}},{key:"name",get:function(){return this[O]}},{key:"group",get:function(){return this[C]}},{key:"pendingClose",get:function(){return this[w]}},{key:"closed",get:function(){return this[m]===b}},{key:"opened",get:function(){return this[m]===v}},{key:"halfOpen",get:function(){return this[m]===d}},{key:"status",get:function(){return this[k]}},{key:"stats",get:function(){return this[k].stats}},{key:"toJSON",value:function(){return{state:{name:this.name,enabled:this.enabled,closed:this.closed,open:this.opened,halfOpen:this.halfOpen,warmUp:this.warmUp,shutdown:this.isShutdown,lastTimerAt:this[_]},status:this.status.stats}}},{key:"enabled",get:function(){return this[S]}},{key:"warmUp",get:function(){return this[j]}},{key:"volumeThreshold",get:function(){return this[x]}},{key:"fallback",value:function(e){var n=e;return e instanceof t&&(n=function(){return e.fire.apply(e,arguments)}),this[T]=n,this}},{key:"fire",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.call.apply(this,[this.action].concat(t))}},{key:"call",value:function(e){var t=this;if(this.isShutdown){var n=D("The circuit has been shutdown.","ESHUTDOWN");return Promise.reject(n)}for(var o=arguments.length,r=new Array(o>1?o-1:0),i=1;i<o;i++)r[i-1]=arguments[i];var s=r.slice();if(this.emit("fire",s),!this[S]){var c=this.action.apply(e,s);return"function"==typeof c.then?c:Promise.resolve(c)}var a,u=this.options.cache||this.options.coalesce?this.options.cacheGetKey.apply(this,r):"";if(this.options.cache){var l=this.options.cacheTransport.get(u);if(l)return this.emit("cacheHit"),l;this.emit("cacheMiss")}if(this.options.coalesce){var f=this.options.coalesceCache.get(u);if(f)return this.emit("coalesceCacheHit"),f;this.emit("coalesceCacheMiss")}if(!this.closed&&!this.pendingClose){var p=D("Breaker is open","EOPENBREAKER");return this.emit("reject",p),R(this,p,s)||Promise.reject(p)}this[w]=!1;var h=!1,y=new Promise((function(n,o){var r=Date.now();if(t.semaphore.test()){t.options.timeout&&(a=setTimeout((function(){h=!0;var e=D("Timed out after ".concat(t.options.timeout,"ms"),"ETIMEDOUT"),i=Date.now()-r;t.semaphore.release(),t.emit("timeout",e,i,s),B(e,t,a,s,i,n,o),M(t,u,"timeout"),t.options.abortController&&t.options.abortController.abort()}),t.options.timeout));try{var i=t.action.apply(e,s),c="function"==typeof i.then?i:Promise.resolve(i);c.then((function(e){h||(clearTimeout(a),t.emit("success",e,Date.now()-r),M(t,u,"success"),t.semaphore.release(),n(e),t.options.cache&&t.options.cacheTransport.set(u,c,t.options.cacheTTL>0?Date.now()+t.options.cacheTTL:0))})).catch((function(e){if(!h){t.semaphore.release();var i=Date.now()-r;B(e,t,a,s,i,n,o),M(t,u,"error")}}))}catch(e){t.semaphore.release();var l=Date.now()-r;B(e,t,a,s,l,n,o),M(t,u,"error")}}else{var f=Date.now()-r,p=D("Semaphore locked","ESEMLOCKED");t.emit("semaphoreLocked",p,f),B(p,t,a,s,f,n,o),M(t,u)}}));return this.options.coalesce&&this.options.coalesceCache.set(u,y,this.options.coalesceTTL>0?Date.now()+this.options.coalesceTTL:0),y}},{key:"clearCache",value:function(){this.options.cache&&this.options.cacheTransport.flush(),this.options.coalesceCache&&this.options.coalesceCache.flush()}},{key:"healthCheck",value:function(e,t){var n=this;if(t=t||5e3,"function"!=typeof e)throw new TypeError("Health check function must be a function");if(isNaN(t))throw new TypeError("Health check interval must be a number");var o=function(t){e.apply(n).catch((function(e){n.emit("healthCheckFailed",e),n.open()}))},r=setInterval(o,t);"function"==typeof r.unref&&r.unref(),o()}},{key:"enable",value:function(){this[S]=!0,this.status.startListeneningForRotateEvent()}},{key:"disable",value:function(){this[S]=!1,this.status.removeRotateBucketControllerListener()}},{key:"getSignal",value:function(){if(this.options.abortController&&this.options.abortController.signal)return this.options.abortController.signal}},{key:"getAbortController",value:function(){return this.options.abortController}}],r=[{key:"isOurError",value:function(e){return!!e[E]}},{key:"newStatus",value:function(e){return new p(e)}}],o&&s(n.prototype,o),r&&s(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,r}(f);function B(e,t,n,r,i,s,c){var a;if(clearTimeout(n),(a=t.options).errorFilter.apply(a,[e].concat(o(r))))t.emit("success",e,i);else{!function(e,t,n,o){if(e.emit("failure",t,o,n),!e.warmUp){var r=e.stats;r.fires<e.volumeThreshold&&!e.halfOpen||(r.failures/r.fires*100>e.options.errorThresholdPercentage||e.halfOpen)&&e.open()}}(t,e,r,i);var u=R(t,e,r);if(u)return s(u)}c(e)}function R(e,t,n){if(e[T])try{var r=e[T].apply(e[T],[].concat(o(n),[t]));return e.emit("fallback",r,t),r instanceof Promise?r:Promise.resolve(r)}catch(e){return Promise.reject(e)}}function M(e,t,n){var o;n&&!e.options.coalesceResetOn.includes(n)||null===(o=e.options.coalesceCache)||void 0===o||o.delete(t)}function D(e,t){var n=new Error(e);return n.code=t,n[E]=!0,n}var N=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))};e.exports=A},310:(e,t)=>{function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}function r(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var o=t.call(e,"string");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=function(){return e=function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.cache=new Map,this.maxEntries=null!=t?t:Math.pow(2,24)-1},(t=[{key:"get",value:function(e){var t=this.cache.get(e);if(t){if(t.expiresAt>Date.now()||0===t.expiresAt)return t.value;this.cache.delete(e)}}},{key:"set",value:function(e,t,n){this.cache.size===this.maxEntries&&void 0===this.get(e)&&this.cache.delete(this.cache.keys().next().value),this.cache.set(e,{expiresAt:n,value:t})}},{key:"delete",value:function(e){this.cache.delete(e)}},{key:"flush",value:function(){this.cache.clear()}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=i},660:(e,t,n)=>{"use strict";e.exports=n(299)},676:(e,t,n)=>{"use strict";function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e){if("object"!=o(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==o(t)?t:t+""}function u(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(u=function(){return!!e})()}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function f(e,t){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},f(e,t)}var p=Symbol("window"),h=Symbol("buckets"),y=Symbol("timeout"),m=Symbol("percentiles"),v=Symbol("bucket-interval"),b=Symbol("snapshot-interval"),d=Symbol("rotate-event-name"),w=function(e){function t(e){var n;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(n=function(e,t,n){return t=l(t),function(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,u()?Reflect.construct(t,n||[],l(e).constructor):t.apply(e,n))}(this,t))[h]=e.rollingCountBuckets||10,n[y]=e.rollingCountTimeout||1e4,n[p]=new Array(n[h]),n[m]=[0,.25,.5,.75,.9,.95,.99,.995,1],n[d]="rotate",n.rollingPercentilesEnabled=!1!==e.rollingPercentilesEnabled,n.enableSnapshots=!1!==e.enableSnapshots,n.rotateBucketController=e.rotateBucketController,n.rotateBucket=g(n[p]);for(var r=0;r<n[h];r++)n[p][r]=T();var s=Math.floor(n[y]/n[h]);return n.rotateBucketController?n.startListeneningForRotateEvent():(n[v]=setInterval(n.rotateBucket,s),"function"==typeof n[v].unref&&n[v].unref()),n.enableSnapshots&&(n[b]=setInterval((function(e){return n.emit("snapshot",n.stats)}),s),"function"==typeof n[b].unref&&n[b].unref()),e.stats&&(n[p][0]=i(i({},T()),e.stats)),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}(t,e),n=t,(r=[{key:"stats",get:function(){var e=this,t=this[p].reduce((function(t,n){return n?(Object.keys(t).forEach((function(e){"latencyTimes"!==e&&"percentiles"!==e&&(t[e]+=n[e]||0)})),e.rollingPercentilesEnabled&&n.latencyTimes&&(t.latencyTimes=t.latencyTimes.concat(n.latencyTimes)),t):t}),T());return this.rollingPercentilesEnabled?(t.latencyTimes.sort((function(e,t){return e-t})),t.latencyTimes.length?t.latencyMean=t.latencyTimes.reduce((function(e,t){return e+t}),0)/t.latencyTimes.length:t.latencyMean=0,this[m].forEach((function(e){t.percentiles[e]=function(e,t){return 0===e?t[0]||0:t[Math.ceil(e*t.length)-1]||0}(e,t.latencyTimes)}))):(t.latencyMean=-1,this[m].forEach((function(e){t.percentiles[e]=-1}))),t}},{key:"window",get:function(){return this[p].slice()}},{key:"increment",value:function(e,t){this[p][0][e]++,"successes"!==e&&"failures"!==e&&"timeouts"!==e||this[p][0].latencyTimes.push(t||0)}},{key:"open",value:function(){this[p][0].isCircuitBreakerOpen=!0}},{key:"close",value:function(){this[p][0].isCircuitBreakerOpen=!1}},{key:"shutdown",value:function(){this.removeAllListeners(),void 0===this.rotateBucketController?clearInterval(this[v]):this.removeRotateBucketControllerListener(),this.enableSnapshots&&clearInterval(this[b])}},{key:"removeRotateBucketControllerListener",value:function(){this.rotateBucketController&&this.rotateBucketController.removeListener(this[d],this.rotateBucket)}},{key:"startListeneningForRotateEvent",value:function(){this.rotateBucketController&&0===this.rotateBucketController.listenerCount(this[d],this.rotateBucket)&&this.rotateBucketController.on(this[d],this.rotateBucket)}}])&&c(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r}(n(7).EventEmitter),g=function(e){return function(t){e.pop(),e.unshift(T())}},T=function(e){return{failures:0,fallbacks:0,successes:0,rejects:0,fires:0,timeouts:0,cacheHits:0,cacheMisses:0,coalesceCacheHits:0,coalesceCacheMisses:0,semaphoreRejections:0,percentiles:{},latencyTimes:[]}};e.exports=w}},t={},function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}(660);var e,t}));
//# sourceMappingURL=opossum.min.map