{"version": 3, "file": "saml-post-signing.js", "sourceRoot": "", "sources": ["../../src/node-saml/saml-post-signing.ts"], "names": [], "mappings": ";;;AACA,+BAAgC;AAEhC,MAAM,iBAAiB,GACrB,8FAA8F,CAAC;AACjG,MAAM,WAAW,GACf,yFAAyF,CAAC;AAE5F,SAAgB,YAAY,CAC1B,WAAmB,EACnB,KAAa,EACb,OAA2B;IAE3B,OAAO,IAAA,aAAO,EAAC,WAAW,EAAE,KAAK,EAAE,EAAE,SAAS,EAAE,KAAK,GAAG,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;AACnG,CAAC;AAND,oCAMC;AAED,SAAgB,oBAAoB,CAAC,YAAoB,EAAE,OAA2B;IACpF,OAAO,YAAY,CAAC,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAFD,oDAEC", "sourcesContent": ["import { SamlSigningOptions } from \"./types\";\nimport { signXml } from \"./xml\";\n\nconst authnRequestXPath =\n  '/*[local-name(.)=\"AuthnRequest\" and namespace-uri(.)=\"urn:oasis:names:tc:SAML:2.0:protocol\"]';\nconst issuerXPath =\n  '/*[local-name(.)=\"Issuer\" and namespace-uri(.)=\"urn:oasis:names:tc:SAML:2.0:assertion\"]';\n\nexport function signSamlPost(\n  samlMessage: string,\n  xpath: string,\n  options: SamlSigningOptions\n): string {\n  return signXml(samlMessage, xpath, { reference: xpath + issuerXPath, action: \"after\" }, options);\n}\n\nexport function signAuthnRequestPost(authnRequest: string, options: SamlSigningOptions): string {\n  return signSamlPost(authnRequest, authnRequestXPath, options);\n}\n"]}