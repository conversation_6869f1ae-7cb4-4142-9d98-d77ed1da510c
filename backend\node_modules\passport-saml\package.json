{"name": "passport-saml", "version": "3.2.4", "description": "SAML 2.0 authentication strategy for Passport", "keywords": ["saml", "adfs", "sso", "shibboleth"], "repository": {"type": "git", "url": "https://github.com/node-saml/passport-saml.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://bergie.iki.fi"}, "contributors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "main": "./lib/passport-saml", "files": ["lib", "README.md", "LICENSE"], "scripts": {"build": "tsc", "changelog": "gren changelog --override --generate --head 3.x", "lint": "eslint --ext .ts \"**/*.ts\" --cache", "lint-watch": "onchange -k -p 100 \"**/*.ts\" -- eslint {{file}}", "lint:fix": "eslint --ext .ts --fix src", "prepare": "tsc", "prettier-check": "prettier --config .prettierrc.json --check .", "prettier-format": "prettier --config .prettierrc.json --write .", "prettier-watch": "onchange -k -p 100 \".\" -- prettier --config .prettierrc.json --write {{file}}", "prerelease": "git clean -xfd && npm ci && npm test && npm run build", "release": "release-it", "test": "npm run prettier-check && npm run lint && npm run tsc && mocha", "test-watch": "mocha --watch", "tsc": "tsc", "tsc-watch": "tsc --watch", "watch": "concurrently --kill-others \"npm:*-watch\""}, "dependencies": {"@xmldom/xmldom": "^0.7.6", "debug": "^4.3.2", "passport-strategy": "^1.0.0", "xml-crypto": "^2.1.3", "xml-encryption": "^2.0.0", "xml2js": "^0.4.23", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@cjbarth/github-release-notes": "^2.1.0", "@types/debug": "^4.1.7", "@types/mocha": "^8.2.3", "@types/node": "^14.17.17", "@types/passport": "^1.0.4", "@types/passport-strategy": "^0.2.35", "@types/request": "^2.48.7", "@types/sinon": "^10.0.2", "@types/xml-crypto": "^1.4.2", "@types/xml-encryption": "^1.2.1", "@types/xml2js": "^0.4.9", "@typescript-eslint/eslint-plugin": "^4.31.1", "@typescript-eslint/parser": "^4.31.1", "body-parser": "^1.19.0", "choma": "^1.2.1", "concurrently": "^6.2.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "express": "^4.17.1", "mocha": "^8.4.0", "onchange": "^7.1.0", "passport": "^0.4.1", "prettier": "^2.4.1", "prettier-plugin-packagejson": "^2.2.12", "release-it": "^14.12.3", "request": "^2.83.0", "should": "^13.2.3", "sinon": "^10.0.0", "ts-node": "^9.1.1", "typescript": "^4.4.3"}, "engines": {"node": ">= 12"}}