import { Provider, FactoryProvider, ValueProvider } from '@nestjs/common';
import { RedisModuleOptions, RedisModuleAsyncOptions, RedisOptionsFactory, RedisClients } from './interfaces';
export declare const createOptionsProvider: (options: RedisModuleOptions) => ValueProvider<RedisModuleOptions>;
export declare const createAsyncProviders: (options: RedisModuleAsyncOptions) => Provider[];
export declare const createAsyncOptions: (optionsFactory: RedisOptionsFactory) => Promise<RedisModuleOptions>;
export declare const createAsyncOptionsProvider: (options: RedisModuleAsyncOptions) => Provider;
export declare const redisClientsProvider: FactoryProvider<RedisClients>;
export declare const mergedOptionsProvider: FactoryProvider<RedisModuleOptions>;
