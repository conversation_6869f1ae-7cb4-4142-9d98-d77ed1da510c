"use strict";
var RedisModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisModule = void 0;
const tslib_1 = require("tslib");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const redis_service_1 = require("./redis.service");
const redis_providers_1 = require("./redis.providers");
const redis_constants_1 = require("./redis.constants");
const utils_1 = require("../utils");
const redis_logger_1 = require("./redis-logger");
const errors_1 = require("../errors");
const messages_1 = require("../messages");
let RedisModule = RedisModule_1 = class RedisModule {
    constructor(moduleRef) {
        this.moduleRef = moduleRef;
    }
    /**
     * Registers the module synchronously.
     *
     * @param options - The module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRoot(options = {}, isGlobal = true) {
        const providers = [
            (0, redis_providers_1.createOptionsProvider)(options),
            redis_providers_1.redisClientsProvider,
            redis_providers_1.mergedOptionsProvider,
            redis_service_1.RedisService
        ];
        return {
            global: isGlobal,
            module: RedisModule_1,
            providers,
            exports: [redis_service_1.RedisService]
        };
    }
    /**
     * Registers the module asynchronously.
     *
     * @param options - The async module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRootAsync(options, isGlobal = true) {
        if (!options.useFactory && !options.useClass && !options.useExisting) {
            throw new errors_1.MissingConfigurationsError();
        }
        const providers = [
            ...(0, redis_providers_1.createAsyncProviders)(options),
            redis_providers_1.redisClientsProvider,
            redis_providers_1.mergedOptionsProvider,
            redis_service_1.RedisService,
            ...(options.extraProviders ?? [])
        ];
        return {
            global: isGlobal,
            module: RedisModule_1,
            imports: options.imports,
            providers,
            exports: [redis_service_1.RedisService]
        };
    }
    async onApplicationShutdown() {
        const { closeClient } = this.moduleRef.get(redis_constants_1.REDIS_MERGED_OPTIONS, { strict: false });
        if (closeClient) {
            const clients = this.moduleRef.get(redis_constants_1.REDIS_CLIENTS, { strict: false });
            for (const [namespace, client] of clients) {
                if (client.status === 'end')
                    continue;
                if (client.status === 'ready') {
                    try {
                        await client.quit();
                    }
                    catch (e) {
                        if ((0, utils_1.isError)(e))
                            redis_logger_1.logger.error((0, messages_1.ERROR_LOG)((0, utils_1.parseNamespace)(namespace), e.message), e.stack);
                    }
                    continue;
                }
                client.disconnect();
            }
        }
    }
};
exports.RedisModule = RedisModule;
exports.RedisModule = RedisModule = RedisModule_1 = tslib_1.__decorate([
    (0, common_1.Module)({}),
    tslib_1.__metadata("design:paramtypes", [core_1.ModuleRef])
], RedisModule);
