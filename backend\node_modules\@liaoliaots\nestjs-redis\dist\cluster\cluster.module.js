"use strict";
var ClusterModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClusterModule = void 0;
const tslib_1 = require("tslib");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const cluster_service_1 = require("./cluster.service");
const cluster_providers_1 = require("./cluster.providers");
const cluster_constants_1 = require("./cluster.constants");
const utils_1 = require("../utils");
const cluster_logger_1 = require("./cluster-logger");
const errors_1 = require("../errors");
const messages_1 = require("../messages");
let ClusterModule = ClusterModule_1 = class ClusterModule {
    constructor(moduleRef) {
        this.moduleRef = moduleRef;
    }
    /**
     * Registers the module synchronously.
     *
     * @param options - The module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRoot(options, isGlobal = true) {
        const providers = [
            (0, cluster_providers_1.createOptionsProvider)(options),
            cluster_providers_1.clusterClientsProvider,
            cluster_providers_1.mergedOptionsProvider,
            cluster_service_1.ClusterService
        ];
        return {
            global: isGlobal,
            module: ClusterModule_1,
            providers,
            exports: [cluster_service_1.ClusterService]
        };
    }
    /**
     * Registers the module asynchronously.
     *
     * @param options - The async module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRootAsync(options, isGlobal = true) {
        if (!options.useFactory && !options.useClass && !options.useExisting) {
            throw new errors_1.MissingConfigurationsError();
        }
        const providers = [
            ...(0, cluster_providers_1.createAsyncProviders)(options),
            cluster_providers_1.clusterClientsProvider,
            cluster_providers_1.mergedOptionsProvider,
            cluster_service_1.ClusterService,
            ...(options.extraProviders ?? [])
        ];
        return {
            global: isGlobal,
            module: ClusterModule_1,
            imports: options.imports,
            providers,
            exports: [cluster_service_1.ClusterService]
        };
    }
    async onApplicationShutdown() {
        const { closeClient } = this.moduleRef.get(cluster_constants_1.CLUSTER_MERGED_OPTIONS, { strict: false });
        if (closeClient) {
            const clients = this.moduleRef.get(cluster_constants_1.CLUSTER_CLIENTS, { strict: false });
            for (const [namespace, client] of clients) {
                if (client.status === 'end')
                    continue;
                if (client.status === 'ready') {
                    try {
                        await client.quit();
                    }
                    catch (e) {
                        if ((0, utils_1.isError)(e))
                            cluster_logger_1.logger.error((0, messages_1.ERROR_LOG)((0, utils_1.parseNamespace)(namespace), e.message), e.stack);
                    }
                    continue;
                }
                client.disconnect();
            }
        }
    }
};
exports.ClusterModule = ClusterModule;
exports.ClusterModule = ClusterModule = ClusterModule_1 = tslib_1.__decorate([
    (0, common_1.Module)({}),
    tslib_1.__metadata("design:paramtypes", [core_1.ModuleRef])
], ClusterModule);
