import { Provider } from '@nestjs/common';
import { WinstonModuleAsyncOptions, WinstonModuleOptions } from './winston.interfaces';
import { <PERSON><PERSON><PERSON><PERSON> } from './winston.classes';
export declare function createNestWinstonLogger(loggerOpts: WinstonModuleOptions): WinstonLogger;
export declare function createWinstonProviders(loggerOpts: WinstonModuleOptions): Provider[];
export declare function createWinstonAsyncProviders(options: WinstonModuleAsyncOptions): Provider[];
