{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../../src/api/context.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EACL,SAAS,EACT,cAAc,EACd,gBAAgB,GACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEjC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,IAAM,oBAAoB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAEtD;;GAEG;AACH;IAGE,+FAA+F;IAC/F;IAAuB,CAAC;IAExB,oDAAoD;IACtC,sBAAW,GAAzB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,4CAAuB,GAA9B,UAA+B,cAA8B;QAC3D,OAAO,cAAc,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,2BAAM,GAAb;QACE,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;;;OAOG;IACI,yBAAI,GAAX,UACE,OAAgB,EAChB,EAAK,EACL,OAA8B;;QAC9B,cAAU;aAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;YAAV,6BAAU;;QAEV,OAAO,CAAA,KAAA,IAAI,CAAC,kBAAkB,EAAE,CAAA,CAAC,IAAI,0BAAC,OAAO,EAAE,EAAE,EAAE,OAAO,UAAK,IAAI,WAAE;IACvE,CAAC;IAED;;;;;OAKG;IACI,yBAAI,GAAX,UAAe,OAAgB,EAAE,MAAS;QACxC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAEO,uCAAkB,GAA1B;QACE,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC;IACrD,CAAC;IAED,oDAAoD;IAC7C,4BAAO,GAAd;QACE,IAAI,CAAC,kBAAkB,EAAE,CAAC,OAAO,EAAE,CAAC;QACpC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IACH,iBAAC;AAAD,CAAC,AAnED,IAmEC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { NoopContextManager } from '../context/NoopContextManager';\nimport { Context, ContextManager } from '../context/types';\nimport {\n  getGlobal,\n  registerGlobal,\n  unregisterGlobal,\n} from '../internal/global-utils';\nimport { DiagAPI } from './diag';\n\nconst API_NAME = 'context';\nconst NOOP_CONTEXT_MANAGER = new NoopContextManager();\n\n/**\n * Singleton object which represents the entry point to the OpenTelemetry Context API\n */\nexport class ContextAPI {\n  private static _instance?: ContextAPI;\n\n  /** Empty private constructor prevents end users from constructing a new instance of the API */\n  private constructor() {}\n\n  /** Get the singleton instance of the Context API */\n  public static getInstance(): ContextAPI {\n    if (!this._instance) {\n      this._instance = new ContextAPI();\n    }\n\n    return this._instance;\n  }\n\n  /**\n   * Set the current context manager.\n   *\n   * @returns true if the context manager was successfully registered, else false\n   */\n  public setGlobalContextManager(contextManager: ContextManager): boolean {\n    return registerGlobal(API_NAME, contextManager, DiagAPI.instance());\n  }\n\n  /**\n   * Get the currently active context\n   */\n  public active(): Context {\n    return this._getContextManager().active();\n  }\n\n  /**\n   * Execute a function with an active context\n   *\n   * @param context context to be active during function execution\n   * @param fn function to execute in a context\n   * @param thisArg optional receiver to be used for calling fn\n   * @param args optional arguments forwarded to fn\n   */\n  public with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    context: Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    return this._getContextManager().with(context, fn, thisArg, ...args);\n  }\n\n  /**\n   * Bind a context to a target function or event emitter\n   *\n   * @param context context to bind to the event emitter or function. Defaults to the currently active context\n   * @param target function or event emitter to bind\n   */\n  public bind<T>(context: Context, target: T): T {\n    return this._getContextManager().bind(context, target);\n  }\n\n  private _getContextManager(): ContextManager {\n    return getGlobal(API_NAME) || NOOP_CONTEXT_MANAGER;\n  }\n\n  /** Disable and remove the global context manager */\n  public disable() {\n    this._getContextManager().disable();\n    unregisterGlobal(API_NAME, DiagAPI.instance());\n  }\n}\n"]}