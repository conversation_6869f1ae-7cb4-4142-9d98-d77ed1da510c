"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isError = exports.isString = void 0;
/**
 * Returns `true` if the value is of type `string`.
 *
 * @param value - Any value
 * @returns A boolean value for Type Guard
 */
const isString = (value) => typeof value === 'string';
exports.isString = isString;
/**
 * Returns `true` if the value is an instance of `Error`.
 *
 * @param value - Any value
 * @returns A boolean value for Type Guard
 */
const isError = (value) => {
    const typeName = Object.prototype.toString.call(value).slice(8, -1);
    return typeName === 'Error';
};
exports.isError = isError;
