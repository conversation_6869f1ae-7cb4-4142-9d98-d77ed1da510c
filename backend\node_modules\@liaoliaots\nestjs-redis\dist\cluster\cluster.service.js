"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClusterService = void 0;
const tslib_1 = require("tslib");
const common_1 = require("@nestjs/common");
const cluster_constants_1 = require("./cluster.constants");
const utils_1 = require("../utils");
const errors_1 = require("../errors");
/**
 * Manager for cluster connections.
 */
let ClusterService = class ClusterService {
    constructor(clients) {
        this.clients = clients;
    }
    /**
     * Retrieves a cluster connection by namespace.
     * However, if the query does not find a connection, it returns ClientNotFoundError: No Connection found error.
     *
     * @param namespace - The namespace
     * @returns A cluster connection
     */
    getOrThrow(namespace = cluster_constants_1.DEFAULT_CLUSTER) {
        const client = this.clients.get(namespace);
        if (!client)
            throw new errors_1.ClientNotFoundError((0, utils_1.parseNamespace)(namespace));
        return client;
    }
    /**
     * Retrieves a cluster connection by namespace, if the query does not find a connection, it returns `null`;
     *
     * @param namespace - The namespace
     * @returns A cluster connection or nil
     */
    getOrNil(namespace = cluster_constants_1.DEFAULT_CLUSTER) {
        const client = this.clients.get(namespace);
        if (!client)
            return null;
        return client;
    }
};
exports.ClusterService = ClusterService;
exports.ClusterService = ClusterService = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__param(0, (0, common_1.Inject)(cluster_constants_1.CLUSTER_CLIENTS)),
    tslib_1.__metadata("design:paramtypes", [Object])
], ClusterService);
