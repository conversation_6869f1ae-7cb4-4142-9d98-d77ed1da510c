{"file": "C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\fallback.service.ts", "mappings": ";;;;;;;;;;AAAA,2CAAoD;AA8CpD;;GAEG;AAEI,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAAvB;QACY,WAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,YAAO,GAAG,IAAI,GAAG,EAA0B,CAAC;QAC5C,UAAK,GAAG,IAAI,GAAG,EAAyD,CAAC;QACzE,YAAO,GAAG,IAAI,GAAG,EAA2B,CAAC;IAkgBhE,CAAC;IAhgBC;;OAEG;IACH,gBAAgB,CACd,UAAkB,EAClB,YAAoB,EACpB,MAAsB;QAEtB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAErC,qBAAqB;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;YAC3B,UAAU;YACV,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,CAAC;YACd,oBAAoB,EAAE,CAAC;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,yCAAyC,YAAY,aAAa,UAAU,SAAS,MAAM,CAAC,kBAAkB,CAAC,MAAM,aAAa,CACnI,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACnB,UAAkB,EAClB,aAAqB,EACrB,aAAoB,EACpB,UAAoC,EAAE;QAEtC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,uDAAuD,UAAU,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,aAAa,CAAC;QACtB,CAAC;QAED,MAAM,eAAe,GAAoB;YACvC,UAAU;YACV,aAAa;YACb,aAAa;YACb,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,CAAC;YAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,QAAQ,EAAE,OAAO,CAAC,QAAQ;SAC3B,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAI,UAAU,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;gBACzF,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;oBAC1B,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;oBACtE,OAAO,YAAY,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,MAAM,gBAAgB,GAAG,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;YAEhG,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;gBACxC,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAE,eAAe,CAAC,EAAE,CAAC;oBACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,gCAAgC,QAAQ,CAAC,IAAI,kBAAkB,UAAU,YAAY,aAAa,CAAC,OAAO,EAAE,CAC7G,CAAC;oBAEF,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAI,QAAQ,EAAE,eAAe,CAAC,CAAC;wBAExE,yCAAyC;wBACzC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;4BACnE,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;wBACxF,CAAC;wBAED,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;wBAC7E,OAAO,MAAM,CAAC;oBAChB,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC;wBACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,sBAAsB,QAAQ,CAAC,IAAI,yBAAyB,UAAU,KAAK,aAAa,CAAC,OAAO,EAAE,CACnG,CAAC;wBACF,SAAS;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gDAAgD;YAChD,IAAI,MAAM,CAAC,eAAe,KAAK,aAAa,EAAE,CAAC;gBAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAI,UAAU,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;gBACvF,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;oBACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,UAAU,EAAE,CAAC,CAAC;oBACzE,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC5E,OAAO,WAAW,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,iCAAiC;YACjC,MAAM,aAAa,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,+CAA+C,UAAU,EAAE,EAC3D,KAAK,CACN,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC9B,UAAkB,EAClB,aAAqB,EACrB,aAAoB,EACpB,UAAoC,EAAE;QAEtC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,uDAAuD,UAAU,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,QAAQ,GAAG,MAAM,CAAC;QACtB,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAI,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;YAEhG,qDAAqD;YACrD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACtE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5D,CAAC;gBACF,QAAQ,GAAG,YAAY,IAAI,SAAS,CAAC;gBACrC,SAAS,GAAG,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,aAAa,CAAC;YACjE,CAAC;YAED,OAAO;gBACL,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CACX,UAAkB,EAClB,aAAqB,EACrB,WAAgB,EAChB,QAAW,EACX,GAAY;QAEZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpC,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QAC/E,MAAM,QAAQ,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC;QAExC,yBAAyB;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;YACvB,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG,EAAE,QAAQ;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,UAAU,IAAI,aAAa,EAAE,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACvB,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,YAAY,+BAA+B,UAAU,EAAE,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,aAAa;QACX,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAEnB,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,YAAY,gBAAgB,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE;gBAC3B,UAAU;gBACV,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,EAAE;gBACjB,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,CAAC;gBACd,oBAAoB,EAAE,CAAC;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,UAAU,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe;QACb,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,UAAkB,EAAE,MAA+B;QACtE,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE,GAAG,cAAc,EAAE,GAAG,MAAM,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,UAAU,EAAE,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,UAAkB;QACnC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oDAAoD,UAAU,EAAE,CAAC,CAAC;IACpF,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAC5C,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;YAC/B,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC;QACrC,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExF,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,OAAO;YACP,SAAS;YACT,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAC3B,QAA0B,EAC1B,OAAwB;QAExB,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC,IAAI,CAAC;gBAClB,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1C,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;oBAC/B,UAAU,CAAC,GAAG,EAAE;wBACd,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,QAAQ,CAAC,IAAI,qBAAqB,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC;oBAClG,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACvB,CAAC,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,eAAe,CACrB,UAAkB,EAClB,aAAqB,EACrB,OAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACvF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAExC,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,UAAU,IAAI,aAAa,EAAE,CAAC,CAAC;YAClE,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,uBAAuB;YACvB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,UAAkB,EAClB,aAAqB,EACrB,OAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QACvF,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAExC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,UAAU,IAAI,aAAa,EAAE,CAAC,CAAC;YAC1E,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,WAAW,CACjB,UAAkB,EAClB,aAAqB,EACrB,OAAwB,EACxB,MAAS,EACT,GAAW;QAEX,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QAEvF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;YACvB,IAAI,EAAE,MAAM;YACZ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,GAAG;SACJ,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,UAAkB,EAAE,aAAqB,EAAE,WAAiB;QACnF,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,OAAO,GAAG,UAAU,IAAI,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,SAAS,GAAkB,IAAI,CAAC;QACpC,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;gBACtC,eAAe,GAAG,KAAK,CAAC,SAAS,CAAC;gBAClC,SAAS,GAAG,GAAG,CAAC;YAClB,CAAC;QACH,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CACnB,UAAkB,EAClB,QAAgB,EAChB,aAAqB,EACrB,SAAkB;QAElB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAE7E,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,CAAC;YAED,OAAO,CAAC,oBAAoB;gBAC1B,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;oBAC7E,OAAO,CAAC,cAAc,CAAC;YAEzB,OAAO,CAAC,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;YAEtC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,kBAAkB,EAAE;gBAClB;oBACE,IAAI,EAAE,iBAAiB;oBACvB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;oBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,iCAAiC;iBACvD;gBACD;oBACE,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;oBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;iBACpF;aACF;YACD,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,MAAM,EAAE,YAAY;YAC9B,YAAY,EAAE,IAAI;YAClB,eAAe,EAAE,UAAU;SAC5B,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,kBAAkB,EAAE;gBAClB;oBACE,IAAI,EAAE,kBAAkB;oBACxB,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI;oBACrB,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC;iBAClD;aACF;YACD,YAAY,EAAE,KAAK;YACnB,QAAQ,EAAE,CAAC;YACX,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,SAAS;SAC3B,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,WAAmB,MAAM;QACrD,OAAO;YACL,cAAc,EAAE,IAAI;YACpB,kBAAkB,EAAE,EAAE;YACtB,YAAY,EAAE,IAAI;YAClB,QAAQ;YACR,YAAY,EAAE,GAAG;YACjB,eAAe,EAAE,aAAa;SAC/B,CAAC;IACJ,CAAC;CACF,CAAA;AAtgBY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CAsgB7B", "names": [], "sources": ["C:\\Users\\<USER>\\sentinel\\backend\\src\\modules\\ai\\application\\services\\resilience\\fallback.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\n\r\nexport interface FallbackConfig {\r\n  enableFallback: boolean;\r\n  fallbackStrategies: FallbackStrategy[];\r\n  cacheEnabled: boolean;\r\n  cacheTtl: number; // Cache TTL in milliseconds\r\n  maxCacheSize: number;\r\n  degradationMode: 'graceful' | 'minimal' | 'cached_only';\r\n}\r\n\r\nexport interface FallbackStrategy {\r\n  name: string;\r\n  priority: number; // Lower number = higher priority\r\n  condition: (error: Error, context: FallbackContext) => boolean;\r\n  handler: (context: FallbackContext) => Promise<any> | any;\r\n  timeout?: number;\r\n}\r\n\r\nexport interface FallbackContext {\r\n  providerId: string;\r\n  operationType: string;\r\n  originalError: Error;\r\n  attempt: number;\r\n  requestData?: any;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface FallbackResult<T> {\r\n  result: T;\r\n  strategy: string;\r\n  fromCache: boolean;\r\n  degraded: boolean;\r\n  executionTime: number;\r\n}\r\n\r\nexport interface FallbackMetrics {\r\n  providerId: string;\r\n  totalFallbacks: number;\r\n  strategyUsage: Record<string, number>;\r\n  cacheHits: number;\r\n  cacheMisses: number;\r\n  averageExecutionTime: number;\r\n  lastFallbackTime?: Date;\r\n}\r\n\r\n/**\r\n * AI-specific fallback service for graceful service degradation\r\n */\r\n@Injectable()\r\nexport class AIFallbackService {\r\n  private readonly logger = new Logger(AIFallbackService.name);\r\n  private readonly configs = new Map<string, FallbackConfig>();\r\n  private readonly cache = new Map<string, { data: any; timestamp: number; ttl: number }>();\r\n  private readonly metrics = new Map<string, FallbackMetrics>();\r\n\r\n  /**\r\n   * Register fallback configuration for an AI provider\r\n   */\r\n  registerProvider(\r\n    providerId: string,\r\n    providerType: string,\r\n    config: FallbackConfig\r\n  ): void {\r\n    this.configs.set(providerId, config);\r\n    \r\n    // Initialize metrics\r\n    this.metrics.set(providerId, {\r\n      providerId,\r\n      totalFallbacks: 0,\r\n      strategyUsage: {},\r\n      cacheHits: 0,\r\n      cacheMisses: 0,\r\n      averageExecutionTime: 0,\r\n    });\r\n\r\n    this.logger.log(\r\n      `Registered fallback configuration for ${providerType} provider ${providerId} with ${config.fallbackStrategies.length} strategies`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Execute fallback logic when primary operation fails\r\n   */\r\n  async executeFallback<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    originalError: Error,\r\n    context: Partial<FallbackContext> = {}\r\n  ): Promise<T> {\r\n    const config = this.configs.get(providerId);\r\n    if (!config) {\r\n      throw new Error(`Fallback configuration not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    if (!config.enableFallback) {\r\n      throw originalError;\r\n    }\r\n\r\n    const fallbackContext: FallbackContext = {\r\n      providerId,\r\n      operationType,\r\n      originalError,\r\n      attempt: context.attempt || 1,\r\n      requestData: context.requestData,\r\n      metadata: context.metadata,\r\n    };\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      // Try cache first if enabled\r\n      if (config.cacheEnabled) {\r\n        const cachedResult = this.getCachedResult<T>(providerId, operationType, fallbackContext);\r\n        if (cachedResult !== null) {\r\n          this.updateMetrics(providerId, 'cache', Date.now() - startTime, true);\r\n          return cachedResult;\r\n        }\r\n      }\r\n\r\n      // Execute fallback strategies in priority order\r\n      const sortedStrategies = [...config.fallbackStrategies].sort((a, b) => a.priority - b.priority);\r\n      \r\n      for (const strategy of sortedStrategies) {\r\n        if (strategy.condition(originalError, fallbackContext)) {\r\n          this.logger.warn(\r\n            `Executing fallback strategy '${strategy.name}' for provider ${providerId} due to: ${originalError.message}`\r\n          );\r\n\r\n          try {\r\n            const result = await this.executeStrategy<T>(strategy, fallbackContext);\r\n            \r\n            // Cache the result if caching is enabled\r\n            if (config.cacheEnabled && result !== null && result !== undefined) {\r\n              this.cacheResult(providerId, operationType, fallbackContext, result, config.cacheTtl);\r\n            }\r\n\r\n            this.updateMetrics(providerId, strategy.name, Date.now() - startTime, false);\r\n            return result;\r\n          } catch (strategyError) {\r\n            this.logger.warn(\r\n              `Fallback strategy '${strategy.name}' failed for provider ${providerId}: ${strategyError.message}`\r\n            );\r\n            continue;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If no strategy worked, check degradation mode\r\n      if (config.degradationMode === 'cached_only') {\r\n        const staleResult = this.getStaleResult<T>(providerId, operationType, fallbackContext);\r\n        if (staleResult !== null) {\r\n          this.logger.warn(`Using stale cached result for provider ${providerId}`);\r\n          this.updateMetrics(providerId, 'stale_cache', Date.now() - startTime, true);\r\n          return staleResult;\r\n        }\r\n      }\r\n\r\n      // All fallback strategies failed\r\n      throw originalError;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `All fallback strategies failed for provider ${providerId}`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute fallback with detailed result information\r\n   */\r\n  async executeFallbackWithDetails<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    originalError: Error,\r\n    context: Partial<FallbackContext> = {}\r\n  ): Promise<FallbackResult<T>> {\r\n    const config = this.configs.get(providerId);\r\n    if (!config) {\r\n      throw new Error(`Fallback configuration not registered for provider: ${providerId}`);\r\n    }\r\n\r\n    const startTime = Date.now();\r\n    let fromCache = false;\r\n    let strategy = 'none';\r\n    let degraded = true;\r\n\r\n    try {\r\n      const result = await this.executeFallback<T>(providerId, operationType, originalError, context);\r\n      \r\n      // Determine which strategy was used based on metrics\r\n      const metrics = this.metrics.get(providerId);\r\n      if (metrics) {\r\n        const lastStrategy = Object.keys(metrics.strategyUsage).reduce((a, b) => \r\n          metrics.strategyUsage[a] > metrics.strategyUsage[b] ? a : b\r\n        );\r\n        strategy = lastStrategy || 'unknown';\r\n        fromCache = strategy === 'cache' || strategy === 'stale_cache';\r\n      }\r\n\r\n      return {\r\n        result,\r\n        strategy,\r\n        fromCache,\r\n        degraded,\r\n        executionTime: Date.now() - startTime,\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add a cached response for future fallback use\r\n   */\r\n  cacheResponse<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    requestData: any,\r\n    response: T,\r\n    ttl?: number\r\n  ): void {\r\n    const config = this.configs.get(providerId);\r\n    if (!config || !config.cacheEnabled) {\r\n      return;\r\n    }\r\n\r\n    const cacheKey = this.generateCacheKey(providerId, operationType, requestData);\r\n    const cacheTtl = ttl || config.cacheTtl;\r\n    \r\n    // Check cache size limit\r\n    if (this.cache.size >= config.maxCacheSize) {\r\n      this.evictOldestCacheEntry();\r\n    }\r\n\r\n    this.cache.set(cacheKey, {\r\n      data: response,\r\n      timestamp: Date.now(),\r\n      ttl: cacheTtl,\r\n    });\r\n\r\n    this.logger.debug(`Cached response for ${providerId}:${operationType}`);\r\n  }\r\n\r\n  /**\r\n   * Clear cache for a specific provider\r\n   */\r\n  clearProviderCache(providerId: string): number {\r\n    let clearedCount = 0;\r\n    \r\n    for (const [key] of this.cache) {\r\n      if (key.startsWith(`${providerId}:`)) {\r\n        this.cache.delete(key);\r\n        clearedCount++;\r\n      }\r\n    }\r\n\r\n    if (clearedCount > 0) {\r\n      this.logger.log(`Cleared ${clearedCount} cache entries for provider ${providerId}`);\r\n    }\r\n\r\n    return clearedCount;\r\n  }\r\n\r\n  /**\r\n   * Clear all cache entries\r\n   */\r\n  clearAllCache(): number {\r\n    const totalEntries = this.cache.size;\r\n    this.cache.clear();\r\n    \r\n    if (totalEntries > 0) {\r\n      this.logger.log(`Cleared ${totalEntries} cache entries`);\r\n    }\r\n\r\n    return totalEntries;\r\n  }\r\n\r\n  /**\r\n   * Get fallback metrics for a specific provider\r\n   */\r\n  getProviderMetrics(providerId: string): FallbackMetrics | null {\r\n    return this.metrics.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get metrics for all registered providers\r\n   */\r\n  getAllProviderMetrics(): FallbackMetrics[] {\r\n    return Array.from(this.metrics.values());\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for a specific provider\r\n   */\r\n  resetProviderMetrics(providerId: string): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      this.metrics.set(providerId, {\r\n        providerId,\r\n        totalFallbacks: 0,\r\n        strategyUsage: {},\r\n        cacheHits: 0,\r\n        cacheMisses: 0,\r\n        averageExecutionTime: 0,\r\n      });\r\n      this.logger.log(`Reset fallback metrics for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset metrics for all providers\r\n   */\r\n  resetAllMetrics(): void {\r\n    for (const [providerId] of this.metrics) {\r\n      this.resetProviderMetrics(providerId);\r\n    }\r\n    this.logger.log('Reset fallback metrics for all providers');\r\n  }\r\n\r\n  /**\r\n   * Update provider configuration\r\n   */\r\n  updateProviderConfig(providerId: string, config: Partial<FallbackConfig>): void {\r\n    const existingConfig = this.configs.get(providerId);\r\n    if (existingConfig) {\r\n      const updatedConfig = { ...existingConfig, ...config };\r\n      this.configs.set(providerId, updatedConfig);\r\n      \r\n      this.logger.log(`Updated fallback configuration for provider ${providerId}`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a provider's fallback configuration\r\n   */\r\n  unregisterProvider(providerId: string): void {\r\n    this.clearProviderCache(providerId);\r\n    this.configs.delete(providerId);\r\n    this.metrics.delete(providerId);\r\n    this.logger.log(`Unregistered fallback configuration for provider ${providerId}`);\r\n  }\r\n\r\n  /**\r\n   * Get provider configuration\r\n   */\r\n  getProviderConfig(providerId: string): FallbackConfig | null {\r\n    return this.configs.get(providerId) || null;\r\n  }\r\n\r\n  /**\r\n   * Get cache statistics\r\n   */\r\n  getCacheStats(): { size: number; hitRate: number; totalHits: number; totalMisses: number } {\r\n    let totalHits = 0;\r\n    let totalMisses = 0;\r\n\r\n    for (const metrics of this.metrics.values()) {\r\n      totalHits += metrics.cacheHits;\r\n      totalMisses += metrics.cacheMisses;\r\n    }\r\n\r\n    const hitRate = totalHits + totalMisses > 0 ? totalHits / (totalHits + totalMisses) : 0;\r\n\r\n    return {\r\n      size: this.cache.size,\r\n      hitRate,\r\n      totalHits,\r\n      totalMisses,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Execute a fallback strategy\r\n   */\r\n  private async executeStrategy<T>(\r\n    strategy: FallbackStrategy,\r\n    context: FallbackContext\r\n  ): Promise<T> {\r\n    if (strategy.timeout) {\r\n      return Promise.race([\r\n        Promise.resolve(strategy.handler(context)),\r\n        new Promise<never>((_, reject) => {\r\n          setTimeout(() => {\r\n            reject(new Error(`Fallback strategy '${strategy.name}' timed out after ${strategy.timeout}ms`));\r\n          }, strategy.timeout);\r\n        }),\r\n      ]);\r\n    }\r\n\r\n    return Promise.resolve(strategy.handler(context));\r\n  }\r\n\r\n  /**\r\n   * Get cached result if available and not expired\r\n   */\r\n  private getCachedResult<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    context: FallbackContext\r\n  ): T | null {\r\n    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);\r\n    const cached = this.cache.get(cacheKey);\r\n\r\n    if (cached && Date.now() - cached.timestamp < cached.ttl) {\r\n      this.logger.debug(`Cache hit for ${providerId}:${operationType}`);\r\n      return cached.data;\r\n    }\r\n\r\n    if (cached) {\r\n      // Remove expired entry\r\n      this.cache.delete(cacheKey);\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Get stale cached result (expired but still available)\r\n   */\r\n  private getStaleResult<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    context: FallbackContext\r\n  ): T | null {\r\n    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);\r\n    const cached = this.cache.get(cacheKey);\r\n\r\n    if (cached) {\r\n      this.logger.debug(`Using stale cache for ${providerId}:${operationType}`);\r\n      return cached.data;\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * Cache a result\r\n   */\r\n  private cacheResult<T>(\r\n    providerId: string,\r\n    operationType: string,\r\n    context: FallbackContext,\r\n    result: T,\r\n    ttl: number\r\n  ): void {\r\n    const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);\r\n    \r\n    this.cache.set(cacheKey, {\r\n      data: result,\r\n      timestamp: Date.now(),\r\n      ttl,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Generate cache key\r\n   */\r\n  private generateCacheKey(providerId: string, operationType: string, requestData?: any): string {\r\n    const dataHash = requestData ? JSON.stringify(requestData) : '';\r\n    return `${providerId}:${operationType}:${Buffer.from(dataHash).toString('base64')}`;\r\n  }\r\n\r\n  /**\r\n   * Evict oldest cache entry when cache is full\r\n   */\r\n  private evictOldestCacheEntry(): void {\r\n    let oldestKey: string | null = null;\r\n    let oldestTimestamp = Date.now();\r\n\r\n    for (const [key, entry] of this.cache) {\r\n      if (entry.timestamp < oldestTimestamp) {\r\n        oldestTimestamp = entry.timestamp;\r\n        oldestKey = key;\r\n      }\r\n    }\r\n\r\n    if (oldestKey) {\r\n      this.cache.delete(oldestKey);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update fallback metrics\r\n   */\r\n  private updateMetrics(\r\n    providerId: string,\r\n    strategy: string,\r\n    executionTime: number,\r\n    fromCache: boolean\r\n  ): void {\r\n    const metrics = this.metrics.get(providerId);\r\n    if (metrics) {\r\n      metrics.totalFallbacks++;\r\n      metrics.strategyUsage[strategy] = (metrics.strategyUsage[strategy] || 0) + 1;\r\n      \r\n      if (fromCache) {\r\n        metrics.cacheHits++;\r\n      } else {\r\n        metrics.cacheMisses++;\r\n      }\r\n      \r\n      metrics.averageExecutionTime = \r\n        (metrics.averageExecutionTime * (metrics.totalFallbacks - 1) + executionTime) / \r\n        metrics.totalFallbacks;\r\n      \r\n      metrics.lastFallbackTime = new Date();\r\n      \r\n      this.metrics.set(providerId, metrics);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create predefined fallback configurations\r\n   */\r\n  static createDefaultConfig(): FallbackConfig {\r\n    return {\r\n      enableFallback: true,\r\n      fallbackStrategies: [\r\n        {\r\n          name: 'cached_response',\r\n          priority: 1,\r\n          condition: () => true,\r\n          handler: () => null, // Will be handled by cache logic\r\n        },\r\n        {\r\n          name: 'default_response',\r\n          priority: 10,\r\n          condition: () => true,\r\n          handler: () => ({ message: 'Service temporarily unavailable', status: 'fallback' }),\r\n        },\r\n      ],\r\n      cacheEnabled: true,\r\n      cacheTtl: 300000, // 5 minutes\r\n      maxCacheSize: 1000,\r\n      degradationMode: 'graceful',\r\n    };\r\n  }\r\n\r\n  static createMinimalConfig(): FallbackConfig {\r\n    return {\r\n      enableFallback: true,\r\n      fallbackStrategies: [\r\n        {\r\n          name: 'minimal_response',\r\n          priority: 1,\r\n          condition: () => true,\r\n          handler: () => ({ error: 'Service unavailable' }),\r\n        },\r\n      ],\r\n      cacheEnabled: false,\r\n      cacheTtl: 0,\r\n      maxCacheSize: 0,\r\n      degradationMode: 'minimal',\r\n    };\r\n  }\r\n\r\n  static createCachedOnlyConfig(cacheTtl: number = 600000): FallbackConfig {\r\n    return {\r\n      enableFallback: true,\r\n      fallbackStrategies: [],\r\n      cacheEnabled: true,\r\n      cacheTtl,\r\n      maxCacheSize: 500,\r\n      degradationMode: 'cached_only',\r\n    };\r\n  }\r\n}"], "version": 3}