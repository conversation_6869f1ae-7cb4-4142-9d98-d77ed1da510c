46477da51b1bb3251c8adbd94281f021
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var AIFallbackService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIFallbackService = void 0;
const common_1 = require("@nestjs/common");
/**
 * AI-specific fallback service for graceful service degradation
 */
let AIFallbackService = AIFallbackService_1 = class AIFallbackService {
    constructor() {
        this.logger = new common_1.Logger(AIFallbackService_1.name);
        this.configs = new Map();
        this.cache = new Map();
        this.metrics = new Map();
    }
    /**
     * Register fallback configuration for an AI provider
     */
    registerProvider(providerId, providerType, config) {
        this.configs.set(providerId, config);
        // Initialize metrics
        this.metrics.set(providerId, {
            providerId,
            totalFallbacks: 0,
            strategyUsage: {},
            cacheHits: 0,
            cacheMisses: 0,
            averageExecutionTime: 0,
        });
        this.logger.log(`Registered fallback configuration for ${providerType} provider ${providerId} with ${config.fallbackStrategies.length} strategies`);
    }
    /**
     * Execute fallback logic when primary operation fails
     */
    async executeFallback(providerId, operationType, originalError, context = {}) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
        }
        if (!config.enableFallback) {
            throw originalError;
        }
        const fallbackContext = {
            providerId,
            operationType,
            originalError,
            attempt: context.attempt || 1,
            requestData: context.requestData,
            metadata: context.metadata,
        };
        const startTime = Date.now();
        try {
            // Try cache first if enabled
            if (config.cacheEnabled) {
                const cachedResult = this.getCachedResult(providerId, operationType, fallbackContext);
                if (cachedResult !== null) {
                    this.updateMetrics(providerId, 'cache', Date.now() - startTime, true);
                    return cachedResult;
                }
            }
            // Execute fallback strategies in priority order
            const sortedStrategies = [...config.fallbackStrategies].sort((a, b) => a.priority - b.priority);
            for (const strategy of sortedStrategies) {
                if (strategy.condition(originalError, fallbackContext)) {
                    this.logger.warn(`Executing fallback strategy '${strategy.name}' for provider ${providerId} due to: ${originalError.message}`);
                    try {
                        const result = await this.executeStrategy(strategy, fallbackContext);
                        // Cache the result if caching is enabled
                        if (config.cacheEnabled && result !== null && result !== undefined) {
                            this.cacheResult(providerId, operationType, fallbackContext, result, config.cacheTtl);
                        }
                        this.updateMetrics(providerId, strategy.name, Date.now() - startTime, false);
                        return result;
                    }
                    catch (strategyError) {
                        this.logger.warn(`Fallback strategy '${strategy.name}' failed for provider ${providerId}: ${strategyError.message}`);
                        continue;
                    }
                }
            }
            // If no strategy worked, check degradation mode
            if (config.degradationMode === 'cached_only') {
                const staleResult = this.getStaleResult(providerId, operationType, fallbackContext);
                if (staleResult !== null) {
                    this.logger.warn(`Using stale cached result for provider ${providerId}`);
                    this.updateMetrics(providerId, 'stale_cache', Date.now() - startTime, true);
                    return staleResult;
                }
            }
            // All fallback strategies failed
            throw originalError;
        }
        catch (error) {
            this.logger.error(`All fallback strategies failed for provider ${providerId}`, error);
            throw error;
        }
    }
    /**
     * Execute fallback with detailed result information
     */
    async executeFallbackWithDetails(providerId, operationType, originalError, context = {}) {
        const config = this.configs.get(providerId);
        if (!config) {
            throw new Error(`Fallback configuration not registered for provider: ${providerId}`);
        }
        const startTime = Date.now();
        let fromCache = false;
        let strategy = 'none';
        let degraded = true;
        try {
            const result = await this.executeFallback(providerId, operationType, originalError, context);
            // Determine which strategy was used based on metrics
            const metrics = this.metrics.get(providerId);
            if (metrics) {
                const lastStrategy = Object.keys(metrics.strategyUsage).reduce((a, b) => metrics.strategyUsage[a] > metrics.strategyUsage[b] ? a : b);
                strategy = lastStrategy || 'unknown';
                fromCache = strategy === 'cache' || strategy === 'stale_cache';
            }
            return {
                result,
                strategy,
                fromCache,
                degraded,
                executionTime: Date.now() - startTime,
            };
        }
        catch (error) {
            throw error;
        }
    }
    /**
     * Add a cached response for future fallback use
     */
    cacheResponse(providerId, operationType, requestData, response, ttl) {
        const config = this.configs.get(providerId);
        if (!config || !config.cacheEnabled) {
            return;
        }
        const cacheKey = this.generateCacheKey(providerId, operationType, requestData);
        const cacheTtl = ttl || config.cacheTtl;
        // Check cache size limit and evict if necessary
        while (this.cache.size >= config.maxCacheSize) {
            this.evictOldestCacheEntry();
        }
        this.cache.set(cacheKey, {
            data: response,
            timestamp: Date.now(),
            ttl: cacheTtl,
        });
        this.logger.debug(`Cached response for ${providerId}:${operationType}`);
    }
    /**
     * Clear cache for a specific provider
     */
    clearProviderCache(providerId) {
        let clearedCount = 0;
        for (const [key] of this.cache) {
            if (key.startsWith(`${providerId}:`)) {
                this.cache.delete(key);
                clearedCount++;
            }
        }
        if (clearedCount > 0) {
            this.logger.log(`Cleared ${clearedCount} cache entries for provider ${providerId}`);
        }
        return clearedCount;
    }
    /**
     * Clear all cache entries
     */
    clearAllCache() {
        const totalEntries = this.cache.size;
        this.cache.clear();
        if (totalEntries > 0) {
            this.logger.log(`Cleared ${totalEntries} cache entries`);
        }
        return totalEntries;
    }
    /**
     * Get fallback metrics for a specific provider
     */
    getProviderMetrics(providerId) {
        return this.metrics.get(providerId) || null;
    }
    /**
     * Get metrics for all registered providers
     */
    getAllProviderMetrics() {
        return Array.from(this.metrics.values());
    }
    /**
     * Reset metrics for a specific provider
     */
    resetProviderMetrics(providerId) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            this.metrics.set(providerId, {
                providerId,
                totalFallbacks: 0,
                strategyUsage: {},
                cacheHits: 0,
                cacheMisses: 0,
                averageExecutionTime: 0,
            });
            this.logger.log(`Reset fallback metrics for provider ${providerId}`);
        }
    }
    /**
     * Reset metrics for all providers
     */
    resetAllMetrics() {
        for (const [providerId] of this.metrics) {
            this.resetProviderMetrics(providerId);
        }
        this.logger.log('Reset fallback metrics for all providers');
    }
    /**
     * Update provider configuration
     */
    updateProviderConfig(providerId, config) {
        const existingConfig = this.configs.get(providerId);
        if (existingConfig) {
            const updatedConfig = { ...existingConfig, ...config };
            this.configs.set(providerId, updatedConfig);
            this.logger.log(`Updated fallback configuration for provider ${providerId}`);
        }
    }
    /**
     * Remove a provider's fallback configuration
     */
    unregisterProvider(providerId) {
        this.clearProviderCache(providerId);
        this.configs.delete(providerId);
        this.metrics.delete(providerId);
        this.logger.log(`Unregistered fallback configuration for provider ${providerId}`);
    }
    /**
     * Get provider configuration
     */
    getProviderConfig(providerId) {
        return this.configs.get(providerId) || null;
    }
    /**
     * Get cache statistics
     */
    getCacheStats() {
        let totalHits = 0;
        let totalMisses = 0;
        for (const metrics of this.metrics.values()) {
            totalHits += metrics.cacheHits;
            totalMisses += metrics.cacheMisses;
        }
        const hitRate = totalHits + totalMisses > 0 ? totalHits / (totalHits + totalMisses) : 0;
        return {
            size: this.cache.size,
            hitRate,
            totalHits,
            totalMisses,
        };
    }
    /**
     * Execute a fallback strategy
     */
    async executeStrategy(strategy, context) {
        if (strategy.timeout) {
            return Promise.race([
                Promise.resolve(strategy.handler(context)),
                new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error(`Fallback strategy '${strategy.name}' timed out after ${strategy.timeout}ms`));
                    }, strategy.timeout);
                }),
            ]);
        }
        return Promise.resolve(strategy.handler(context));
    }
    /**
     * Get cached result if available and not expired
     */
    getCachedResult(providerId, operationType, context) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
            this.logger.debug(`Cache hit for ${providerId}:${operationType}`);
            return cached.data;
        }
        const config = this.configs.get(providerId);
        if (cached && config?.degradationMode !== 'cached_only') {
            // Remove expired entry only if not in cached_only mode
            this.cache.delete(cacheKey);
        }
        return null;
    }
    /**
     * Get stale cached result (expired but still available)
     */
    getStaleResult(providerId, operationType, context) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        const cached = this.cache.get(cacheKey);
        if (cached) {
            this.logger.debug(`Using stale cache for ${providerId}:${operationType}`);
            return cached.data;
        }
        return null;
    }
    /**
     * Cache a result
     */
    cacheResult(providerId, operationType, context, result, ttl) {
        const cacheKey = this.generateCacheKey(providerId, operationType, context.requestData);
        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            ttl,
        });
    }
    /**
     * Generate cache key
     */
    generateCacheKey(providerId, operationType, requestData) {
        const dataHash = requestData ? JSON.stringify(requestData) : '';
        return `${providerId}:${operationType}:${Buffer.from(dataHash).toString('base64')}`;
    }
    /**
     * Evict oldest cache entry when cache is full
     */
    evictOldestCacheEntry() {
        let oldestKey = null;
        let oldestTimestamp = Date.now();
        for (const [key, entry] of this.cache) {
            if (entry.timestamp < oldestTimestamp) {
                oldestTimestamp = entry.timestamp;
                oldestKey = key;
            }
        }
        if (oldestKey) {
            this.cache.delete(oldestKey);
        }
    }
    /**
     * Update fallback metrics
     */
    updateMetrics(providerId, strategy, executionTime, fromCache) {
        const metrics = this.metrics.get(providerId);
        if (metrics) {
            metrics.totalFallbacks++;
            metrics.strategyUsage[strategy] = (metrics.strategyUsage[strategy] || 0) + 1;
            if (fromCache) {
                metrics.cacheHits++;
            }
            else {
                metrics.cacheMisses++;
            }
            metrics.averageExecutionTime =
                (metrics.averageExecutionTime * (metrics.totalFallbacks - 1) + executionTime) /
                    metrics.totalFallbacks;
            metrics.lastFallbackTime = new Date();
            this.metrics.set(providerId, metrics);
        }
    }
    /**
     * Create predefined fallback configurations
     */
    static createDefaultConfig() {
        return {
            enableFallback: true,
            fallbackStrategies: [
                {
                    name: 'cached_response',
                    priority: 1,
                    condition: () => true,
                    handler: () => null, // Will be handled by cache logic
                },
                {
                    name: 'default_response',
                    priority: 10,
                    condition: () => true,
                    handler: () => ({ message: 'Service temporarily unavailable', status: 'fallback' }),
                },
            ],
            cacheEnabled: true,
            cacheTtl: 300000, // 5 minutes
            maxCacheSize: 1000,
            degradationMode: 'graceful',
        };
    }
    static createMinimalConfig() {
        return {
            enableFallback: true,
            fallbackStrategies: [
                {
                    name: 'minimal_response',
                    priority: 1,
                    condition: () => true,
                    handler: () => ({ error: 'Service unavailable' }),
                },
            ],
            cacheEnabled: false,
            cacheTtl: 0,
            maxCacheSize: 0,
            degradationMode: 'minimal',
        };
    }
    static createCachedOnlyConfig(cacheTtl = 600000) {
        return {
            enableFallback: true,
            fallbackStrategies: [],
            cacheEnabled: true,
            cacheTtl,
            maxCacheSize: 500,
            degradationMode: 'cached_only',
        };
    }
};
exports.AIFallbackService = AIFallbackService;
exports.AIFallbackService = AIFallbackService = AIFallbackService_1 = __decorate([
    (0, common_1.Injectable)()
], AIFallbackService);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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