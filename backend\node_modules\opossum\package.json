{"name": "opossum", "version": "9.0.0", "author": "Red Hat, Inc.", "license": "Apache-2.0", "type": "commonjs", "scripts": {"prepare": "husky install", "prebuild": "npm run pretest", "build": "./test/browser/generate-index.sh && NODE_OPTIONS=--openssl-legacy-provider npm run build:browser && npm run build:docs", "build:browser": "webpack --config=config/webpack.config.js", "build:docs": "npm run build:docs:html && npm run build:docs:markdown", "build:docs:html": "documentation build index.js -f html -o docs --config documentation.yml", "build:docs:markdown": "documentation build index.js -f md -o docs/opossum.md", "pretest": "npm run lint", "test": "nyc tape test/*.js | faucet", "test:headless": "node test/browser/webpack-test.js", "test:browser": "opener http://localhost:9007/test/browser/index.html && serve . -p 9007", "test:all": "npm run build && npm run test && npm run test:headless", "lint": "eslint --ignore-path .gitignore .", "clean": "rm -rf node_modules dist/*.js test/browser/webpack-test.js", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git://github.com/nodeshift/opossum.git"}, "files": ["index.js", "lib", "dist", "doc/opossum.md"], "bugs": {"url": "https://github.com/nodeshift/opossum/issues"}, "homepage": "https://nodeshift.dev/opossum", "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "babel-loader": "^9.2.1", "buffer": "^6.0.3", "documentation": "^14.0.3", "eslint": "^8.51.0", "eslint-config-semistandard": "^17.0.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.0", "eslint-plugin-standard": "^5.0.0", "faucet": "^0.0.3", "husky": "^9.1.7", "nyc": "~17.0.0", "opener": "1.5.2", "serve": "^14.2.0", "stream-browserify": "^3.0.0", "tape": "~5.9.0", "util": "^0.12.5", "webpack": "~5.98.0", "webpack-cli": "~5.1.4"}, "description": "A fail-fast circuit breaker for promises and callbacks", "keywords": ["circuit breaker", "circuit-breaker", "fail-fast", "circuit", "breaker", "hystrix", "rate-limiting"], "engines": {"node": "^24 || ^22 || ^20"}, "support": true}