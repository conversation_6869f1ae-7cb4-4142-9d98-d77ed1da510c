import { Test, TestingModule } from '@nestjs/testing';
import { AIFallbackService, FallbackConfig, FallbackStrategy } from '../fallback.service';

describe('AIFallbackService', () => {
  let service: AIFallbackService;

  const mockStrategy1: FallbackStrategy = {
    name: 'strategy1',
    priority: 1,
    condition: (error) => error.message.includes('timeout'),
    handler: () => ({ result: 'fallback1' }),
  };

  const mockStrategy2: FallbackStrategy = {
    name: 'strategy2',
    priority: 2,
    condition: () => true,
    handler: () => ({ result: 'fallback2' }),
  };

  const defaultConfig: FallbackConfig = {
    enableFallback: true,
    fallbackStrategies: [mockStrategy1, mockStrategy2],
    cacheEnabled: true,
    cacheTtl: 5000,
    maxCacheSize: 100,
    degradationMode: 'graceful',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AIFallbackService],
    }).compile();

    service = module.get<AIFallbackService>(AIFallbackService);
  });

  afterEach(() => {
    // Clean up all registered providers and cache
    service.clearAllCache();
    service.resetAllMetrics();
  });

  describe('registerProvider', () => {
    it('should register a new provider with fallback configuration', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const config = service.getProviderConfig(providerId);
      expect(config).toEqual(defaultConfig);
    });

    it('should initialize metrics for registered provider', () => {
      const providerId = 'test-provider';
      const providerType = 'openai';

      service.registerProvider(providerId, providerType, defaultConfig);

      const metrics = service.getProviderMetrics(providerId);
      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe(providerId);
      expect(metrics!.totalFallbacks).toBe(0);
      expect(metrics!.strategyUsage).toEqual({});
    });
  });

  describe('executeFallback', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should throw original error when fallback is disabled', async () => {
      const disabledConfig: FallbackConfig = {
        ...defaultConfig,
        enableFallback: false,
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', disabledConfig);

      const originalError = new Error('original error');

      await expect(
        service.executeFallback('test-provider', 'test-operation', originalError)
      ).rejects.toThrow('original error');
    });

    it('should execute fallback strategy based on condition', async () => {
      const timeoutError = new Error('timeout occurred');

      const result = await service.executeFallback('test-provider', 'test-operation', timeoutError);

      expect(result).toEqual({ result: 'fallback1' });
      
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalFallbacks).toBe(1);
      expect(metrics!.strategyUsage['strategy1']).toBe(1);
    });

    it('should execute fallback strategy in priority order', async () => {
      const genericError = new Error('generic error');

      const result = await service.executeFallback('test-provider', 'test-operation', genericError);

      // Should use strategy2 because strategy1 only matches timeout errors
      expect(result).toEqual({ result: 'fallback2' });
    });

    it('should fall back to next strategy if first one fails', async () => {
      const failingStrategy: FallbackStrategy = {
        name: 'failing-strategy',
        priority: 1,
        condition: () => true,
        handler: () => { throw new Error('strategy failed'); },
      };

      const configWithFailingStrategy: FallbackConfig = {
        ...defaultConfig,
        fallbackStrategies: [failingStrategy, mockStrategy2],
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', configWithFailingStrategy);

      const originalError = new Error('original error');

      const result = await service.executeFallback('test-provider', 'test-operation', originalError);

      expect(result).toEqual({ result: 'fallback2' });
    });

    it('should throw original error if all strategies fail', async () => {
      const failingStrategy1: FallbackStrategy = {
        name: 'failing-strategy1',
        priority: 1,
        condition: () => true,
        handler: () => { throw new Error('strategy1 failed'); },
      };

      const failingStrategy2: FallbackStrategy = {
        name: 'failing-strategy2',
        priority: 2,
        condition: () => true,
        handler: () => { throw new Error('strategy2 failed'); },
      };

      const configWithFailingStrategies: FallbackConfig = {
        ...defaultConfig,
        fallbackStrategies: [failingStrategy1, failingStrategy2],
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', configWithFailingStrategies);

      const originalError = new Error('original error');

      await expect(
        service.executeFallback('test-provider', 'test-operation', originalError)
      ).rejects.toThrow('original error');
    });

    it('should throw error for unregistered provider', async () => {
      const originalError = new Error('original error');

      await expect(
        service.executeFallback('unknown-provider', 'test-operation', originalError)
      ).rejects.toThrow('Fallback configuration not registered for provider: unknown-provider');
    });

    it('should handle async strategy handlers', async () => {
      const asyncStrategy: FallbackStrategy = {
        name: 'async-strategy',
        priority: 1,
        condition: () => true,
        handler: async () => {
          await new Promise(resolve => setTimeout(resolve, 10));
          return { result: 'async-fallback' };
        },
      };

      const configWithAsyncStrategy: FallbackConfig = {
        ...defaultConfig,
        fallbackStrategies: [asyncStrategy],
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', configWithAsyncStrategy);

      const originalError = new Error('original error');

      const result = await service.executeFallback('test-provider', 'test-operation', originalError);

      expect(result).toEqual({ result: 'async-fallback' });
    });

    it('should handle strategy timeout', async () => {
      const slowStrategy: FallbackStrategy = {
        name: 'slow-strategy',
        priority: 1,
        condition: () => true,
        timeout: 50,
        handler: async () => {
          await new Promise(resolve => setTimeout(resolve, 100));
          return { result: 'slow-fallback' };
        },
      };

      const configWithSlowStrategy: FallbackConfig = {
        ...defaultConfig,
        fallbackStrategies: [slowStrategy, mockStrategy2],
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', configWithSlowStrategy);

      const originalError = new Error('original error');

      const result = await service.executeFallback('test-provider', 'test-operation', originalError);

      // Should fall back to strategy2 after strategy1 times out
      expect(result).toEqual({ result: 'fallback2' });
    });
  });

  describe('executeFallbackWithDetails', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return detailed fallback information', async () => {
      const originalError = new Error('timeout occurred');

      const result = await service.executeFallbackWithDetails('test-provider', 'test-operation', originalError);

      expect(result.result).toEqual({ result: 'fallback1' });
      expect(result.strategy).toBe('strategy1');
      expect(result.fromCache).toBe(false);
      expect(result.degraded).toBe(true);
      expect(result.executionTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('caching', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should cache response when caching is enabled', () => {
      const response = { result: 'cached-response' };
      
      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);

      // Verify cache stats
      const stats = service.getCacheStats();
      expect(stats.size).toBe(1);
    });

    it('should return cached result when available', async () => {
      const response = { result: 'cached-response' };
      
      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);

      const originalError = new Error('original error');
      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
        requestData: { input: 'test' },
      });

      expect(result).toEqual(response);
      
      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.cacheHits).toBe(1);
    });

    it('should not return expired cached result', async () => {
      const shortTtlConfig: FallbackConfig = {
        ...defaultConfig,
        cacheTtl: 10, // Very short TTL
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', shortTtlConfig);

      const response = { result: 'cached-response' };
      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);

      // Wait for cache to expire
      await new Promise(resolve => setTimeout(resolve, 20));

      const originalError = new Error('original error');
      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
        requestData: { input: 'test' },
      });

      // Should use fallback strategy2, not cached result (strategy1 only matches timeout)
      expect(result).toEqual({ result: 'fallback2' });
    });

    it('should evict oldest entry when cache is full', () => {
      const smallCacheConfig: FallbackConfig = {
        ...defaultConfig,
        maxCacheSize: 2,
      };

      service.unregisterProvider('test-provider');
      service.registerProvider('test-provider', 'openai', smallCacheConfig);

      // Fill cache to capacity
      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
      service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
      
      // Add one more to trigger eviction
      service.cacheResponse('test-provider', 'op3', { input: '3' }, { result: '3' });

      const stats = service.getCacheStats();
      expect(stats.size).toBe(2); // Should still be at max capacity
    });

    it('should clear provider cache', () => {
      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
      service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });

      const clearedCount = service.clearProviderCache('test-provider');
      expect(clearedCount).toBe(2);

      const stats = service.getCacheStats();
      expect(stats.size).toBe(0);
    });

    it('should clear all cache', () => {
      service.registerProvider('provider2', 'bedrock', defaultConfig);
      
      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
      service.cacheResponse('provider2', 'op2', { input: '2' }, { result: '2' });

      const clearedCount = service.clearAllCache();
      expect(clearedCount).toBe(2);

      const stats = service.getCacheStats();
      expect(stats.size).toBe(0);
    });
  });

  describe('getProviderMetrics', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return metrics for registered provider', () => {
      const metrics = service.getProviderMetrics('test-provider');

      expect(metrics).toBeDefined();
      expect(metrics!.providerId).toBe('test-provider');
    });

    it('should return null for unregistered provider', () => {
      const metrics = service.getProviderMetrics('unknown-provider');
      expect(metrics).toBeNull();
    });

    it('should update metrics after fallback execution', async () => {
      const originalError = new Error('timeout occurred');

      await service.executeFallback('test-provider', 'test-operation', originalError);

      const metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalFallbacks).toBe(1);
      expect(metrics!.strategyUsage['strategy1']).toBe(1);
      expect(metrics!.averageExecutionTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getAllProviderMetrics', () => {
    it('should return empty array when no providers registered', () => {
      const metrics = service.getAllProviderMetrics();
      expect(metrics).toEqual([]);
    });

    it('should return metrics for all registered providers', () => {
      service.registerProvider('provider1', 'openai', defaultConfig);
      service.registerProvider('provider2', 'bedrock', defaultConfig);

      const metrics = service.getAllProviderMetrics();
      expect(metrics).toHaveLength(2);
      expect(metrics.map(m => m.providerId)).toContain('provider1');
      expect(metrics.map(m => m.providerId)).toContain('provider2');
    });
  });

  describe('resetProviderMetrics', () => {
    beforeEach(async () => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
      
      // Generate some metrics
      const originalError = new Error('timeout occurred');
      await service.executeFallback('test-provider', 'test-operation', originalError);
    });

    it('should reset metrics for specific provider', () => {
      // Verify metrics exist
      let metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalFallbacks).toBe(1);

      // Reset metrics
      service.resetProviderMetrics('test-provider');

      // Verify metrics are reset
      metrics = service.getProviderMetrics('test-provider');
      expect(metrics!.totalFallbacks).toBe(0);
      expect(metrics!.strategyUsage).toEqual({});
    });
  });

  describe('updateProviderConfig', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should update provider configuration', () => {
      const newConfig = { cacheTtl: 10000, maxCacheSize: 200 };
      
      service.updateProviderConfig('test-provider', newConfig);

      const config = service.getProviderConfig('test-provider');
      expect(config!.cacheTtl).toBe(10000);
      expect(config!.maxCacheSize).toBe(200);
      expect(config!.enableFallback).toBe(true); // Should keep existing values
    });
  });

  describe('unregisterProvider', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should remove provider configuration and metrics', () => {
      expect(service.getProviderConfig('test-provider')).toBeDefined();
      expect(service.getProviderMetrics('test-provider')).toBeDefined();

      service.unregisterProvider('test-provider');

      expect(service.getProviderConfig('test-provider')).toBeNull();
      expect(service.getProviderMetrics('test-provider')).toBeNull();
    });

    it('should clear provider cache when unregistering', () => {
      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
      
      expect(service.getCacheStats().size).toBe(1);

      service.unregisterProvider('test-provider');

      expect(service.getCacheStats().size).toBe(0);
    });
  });

  describe('getCacheStats', () => {
    beforeEach(() => {
      service.registerProvider('test-provider', 'openai', defaultConfig);
    });

    it('should return cache statistics', async () => {
      // Add some cache entries and generate hits/misses
      service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
      
      const originalError = new Error('original error');
      
      // Generate cache hit
      await service.executeFallback('test-provider', 'op1', originalError, {
        requestData: { input: '1' },
      });
      
      // Generate cache miss
      await service.executeFallback('test-provider', 'op2', originalError, {
        requestData: { input: '2' },
      });

      const stats = service.getCacheStats();
      expect(stats.size).toBeGreaterThan(0);
      expect(stats.totalHits).toBe(1);
      expect(stats.totalMisses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });
  });

  describe('static factory methods', () => {
    it('should create default fallback config', () => {
      const config = AIFallbackService.createDefaultConfig();

      expect(config.enableFallback).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.fallbackStrategies).toHaveLength(2);
      expect(config.degradationMode).toBe('graceful');
    });

    it('should create minimal fallback config', () => {
      const config = AIFallbackService.createMinimalConfig();

      expect(config.enableFallback).toBe(true);
      expect(config.cacheEnabled).toBe(false);
      expect(config.fallbackStrategies).toHaveLength(1);
      expect(config.degradationMode).toBe('minimal');
    });

    it('should create cached-only fallback config', () => {
      const config = AIFallbackService.createCachedOnlyConfig(300000);

      expect(config.enableFallback).toBe(true);
      expect(config.cacheEnabled).toBe(true);
      expect(config.cacheTtl).toBe(300000);
      expect(config.fallbackStrategies).toHaveLength(0);
      expect(config.degradationMode).toBe('cached_only');
    });
  });

  describe('degradation modes', () => {
    it('should use stale cache in cached_only mode when no strategies work', async () => {
      const cachedOnlyConfig: FallbackConfig = {
        enableFallback: true,
        fallbackStrategies: [], // No strategies
        cacheEnabled: true,
        cacheTtl: 10, // Very short TTL to make it stale
        maxCacheSize: 100,
        degradationMode: 'cached_only',
      };

      service.registerProvider('test-provider', 'openai', cachedOnlyConfig);

      // Cache a response
      const response = { result: 'stale-response' };
      service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);

      // Wait for cache to become stale
      await new Promise(resolve => setTimeout(resolve, 20));

      const originalError = new Error('original error');
      const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
        requestData: { input: 'test' },
      });

      expect(result).toEqual(response);
    });
  });
});