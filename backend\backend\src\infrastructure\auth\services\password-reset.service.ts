import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { PasswordService } from './password.service';
import { User } from '../../../modules/user-management/domain/entities/user.entity';
import { AuditService } from '../../logging/audit/audit.service';
import * as crypto from 'crypto';

// Type definitions for better type safety
interface PasswordResetInitiateRequest {
  email: string;
  ipAddress?: string;
  userAgent?: string;
}

interface PasswordResetInitiateResponse {
  message: string;
  resetToken?: string;
}

interface TokenVerificationResult {
  valid: boolean;
  userId?: string;
}

interface PasswordResetResponse {
  success: boolean;
  message: string;
}

interface AuthConfig {
  passwordReset: {
    tokenExpiryMinutes: number;
    rateLimitMinutes: number;
  };
}

interface AuditEventData {
  email?: string;
  ipAddress?: string;
  userAgent?: string;
  status?: string;
  tokenExpiry?: Date;
}

/**
 * Password reset service that handles password reset functionality
 * Provides secure password reset with token-based verification
 */
@Injectable()
export class PasswordResetService {
  private readonly logger = new Logger(PasswordResetService.name);
  private readonly RESET_TOKEN_BYTES = 32;
  private readonly SUCCESS_MESSAGE = 'If an account with that email exists, a password reset link has been sent.';

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly passwordService: PasswordService,
    private readonly configService: ConfigService,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Initiate password reset process
   */
  async initiatePasswordReset({
    email,
    ipAddress,
    userAgent,
  }: PasswordResetInitiateRequest): Promise<PasswordResetInitiateResponse> {
    const normalizedEmail = email.toLowerCase().trim();
    
    try {
      this.logger.debug('Initiating password reset', { email: normalizedEmail, ipAddress });

      const user = await this.findUserByEmail(normalizedEmail);
      
      if (!user) {
        await this.logInvalidEmailAttempt(normalizedEmail, ipAddress, userAgent);
        return { message: this.SUCCESS_MESSAGE };
      }

      if (user.status !== 'active') {
        await this.logInactiveAccountAttempt(user.id, user.status, ipAddress, userAgent);
        return { message: this.SUCCESS_MESSAGE };
      }

      if (await this.isRateLimited(user)) {
        await this.logRateLimitedAttempt(user.id, ipAddress, userAgent);
        return { message: this.SUCCESS_MESSAGE };
      }

      const { resetToken, tokenExpiry } = await this.generateAndStoreResetToken(user);

      await this.auditService.logUserAction(
        user.id,
        'password_reset_initiated',
        'authentication',
        undefined,
        { tokenExpiry },
        ipAddress,
        userAgent,
      );

      this.logger.log('Password reset initiated successfully', {
        userId: user.id,
        email: user.email,
        ipAddress,
      });

      return {
        message: this.SUCCESS_MESSAGE,
        ...(this.isDevelopmentMode() && { resetToken }),
      };
    } catch (error) {
      this.logger.error('Error initiating password reset', {
        email: normalizedEmail,
        error: (error as Error).message,
        ipAddress,
      });
      return { message: this.SUCCESS_MESSAGE };
    }
  }

  /**
   * Verify password reset token
   */
  async verifyResetToken(token: string): Promise<TokenVerificationResult> {
    try {
      this.logger.debug('Verifying password reset token');

      const hashedToken = await this.hashToken(token);
      const user = await this.userRepository.findOne({
        where: { passwordResetToken: hashedToken },
      });

      if (!user) {
        this.logger.warn('Invalid password reset token');
        return { valid: false };
      }

      if (this.isTokenExpired(user.passwordResetExpires)) {
        this.logger.warn('Expired password reset token', {
          userId: user.id,
          expiry: user.passwordResetExpires,
        });
        await this.clearResetToken(user);
        return { valid: false };
      }

      this.logger.debug('Password reset token verified successfully', { userId: user.id });
      return { valid: true, userId: user.id };
    } catch (error) {
      this.logger.error('Error verifying password reset token', {
        error: (error as Error).message,
      });
      return { valid: false };
    }
  }

  /**
   * Reset password using token
   */
  async resetPassword(
    token: string,
    newPassword: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<PasswordResetResponse> {
    try {
      this.logger.debug('Resetting password with token');

      const tokenVerification = await this.verifyResetToken(token);
      if (!tokenVerification.valid || !tokenVerification.userId) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      const user = await this.userRepository.findOneBy({ id: tokenVerification.userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      this.validateNewPassword(newPassword);
      await this.updateUserPassword(user, newPassword);

      await this.auditService.logUserAction(
        user.id,
        'password_reset_completed',
        'authentication',
        undefined,
        {},
        ipAddress,
        userAgent,
      );

      this.logger.log('Password reset completed successfully', { userId: user.id, ipAddress });

      return {
        success: true,
        message: 'Password has been reset successfully',
      };
    } catch (error) {
      this.logger.error('Error resetting password', {
        error: (error as Error).message,
        ipAddress,
      });

      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException('Failed to reset password');
    }
  }

  // Private helper methods
  private async findUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({ where: { email } });
  }

  private async logInvalidEmailAttempt(
    email: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    this.logger.warn('Password reset requested for non-existent email', { email, ipAddress });
    await this.auditService.logSystemEvent(
      'password_reset_invalid_email',
      'authentication',
      undefined,
      { email, ipAddress, userAgent } as AuditEventData,
    );
  }

  private async logInactiveAccountAttempt(
    userId: string,
    status: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    this.logger.warn('Password reset requested for inactive account', {
      userId,
      status,
      ipAddress,
    });
    await this.auditService.logUserAction(
      userId,
      'password_reset_inactive_account',
      'authentication',
      undefined,
      { status },
      ipAddress,
      userAgent,
    );
  }

  private async logRateLimitedAttempt(
    userId: string,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<void> {
    this.logger.warn('Password reset rate limited', { userId, ipAddress });
    await this.auditService.logUserAction(
      userId,
      'password_reset_rate_limited',
      'authentication',
      undefined,
      {},
      ipAddress,
      userAgent,
    );
  }

  private async generateAndStoreResetToken(user: User): Promise<{
    resetToken: string;
    tokenExpiry: Date;
  }> {
    const resetToken = this.generateResetToken();
    const tokenExpiry = this.getTokenExpiry();

    user.passwordResetToken = await this.hashToken(resetToken);
    user.passwordResetExpires = tokenExpiry;

    await this.userRepository.save(user);

    return { resetToken, tokenExpiry };
  }

  private validateNewPassword(password: string): void {
    const passwordValidation = this.passwordService.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      throw new BadRequestException({
        message: 'Password does not meet security requirements',
        errors: passwordValidation.errors,
      });
    }
  }

  private async updateUserPassword(user: User, newPassword: string): Promise<void> {
    const newPasswordHash = await this.passwordService.hashPassword(newPassword);

    Object.assign(user, {
      passwordHash: newPasswordHash,
      passwordChangedAt: new Date(),
      passwordResetToken: undefined,
      passwordResetExpires: undefined,
      failedLoginAttempts: 0,
      lockedUntil: undefined,
    });

    await this.userRepository.save(user);
  }

  private async clearResetToken(user: User): Promise<void> {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await this.userRepository.save(user);
  }

  private generateResetToken(): string {
    return crypto.randomBytes(this.RESET_TOKEN_BYTES).toString('hex');
  }

  private async hashToken(token: string): Promise<string> {
    return this.passwordService.hashPassword(token);
  }

  private getTokenExpiry(): Date {
    const authConfig = this.configService.get<AuthConfig>('auth');
    const expiryMinutes = authConfig?.passwordReset.tokenExpiryMinutes ?? 60;
    
    const expiry = new Date();
    expiry.setMinutes(expiry.getMinutes() + expiryMinutes);
    
    return expiry;
  }

  private isTokenExpired(expiryDate?: Date | null): boolean {
    return !expiryDate || new Date() > expiryDate;
  }

  private async isRateLimited(user: User): Promise<boolean> {
    if (!user.passwordResetExpires) {
      return false;
    }

    const authConfig = this.configService.get<AuthConfig>('auth');
    const rateLimitMinutes = authConfig?.passwordReset.rateLimitMinutes ?? 15;
    const rateLimitMs = rateLimitMinutes * 60 * 1000;

    const timeSinceLastRequest = Date.now() - user.passwordResetExpires.getTime();
    return timeSinceLastRequest < rateLimitMs;
  }

  private isDevelopmentMode(): boolean {
    return this.configService.get('NODE_ENV') === 'development';
  }
}