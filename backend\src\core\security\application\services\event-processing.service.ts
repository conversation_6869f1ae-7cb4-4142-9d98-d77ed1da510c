import { Injectable } from '@nestjs/common';
import { SecurityEvent } from '../../domain/entities/event/security-event.entity';
import { NormalizedEvent } from '../../domain/entities/event/normalized-event.entity';
import { EnrichedEvent } from '../../domain/entities/event/enriched-event.entity';
import { CorrelatedEvent } from '../../domain/entities/event/correlated-event.entity';
import { EventProcessingStatus } from '../../domain/enums/event-processing-status.enum';
import { ThreatSeverity } from '../../domain/enums/threat-severity.enum';
import { SecurityEventRepository } from '../../domain/interfaces/repositories/security-event.repository.interface';
import { ThreatAssessmentService } from '../../domain/interfaces/services/threat-assessment.service.interface';
import { SecurityOrchestratorService } from '../../domain/interfaces/services/security-orchestrator.service.interface';
import { DomainEventPublisher } from '../../../../shared-kernel/domain/domain-event-publisher';
import { Logger } from '../../../../shared-kernel/infrastructure/logger';

/**
 * Event Processing Configuration
 */
export interface EventProcessingConfig {
  /** Enable normalization */
  enableNormalization: boolean;
  /** Enable enrichment */
  enableEnrichment: boolean;
  /** Enable correlation */
  enableCorrelation: boolean;
  /** Enable threat assessment */
  enableThreatAssessment: boolean;
  /** Processing timeout in milliseconds */
  timeoutMs: number;
  /** Maximum retry attempts */
  maxRetries: number;
  /** Batch size for bulk processing */
  batchSize: number;
  /** Quality threshold for processing */
  qualityThreshold: number;
}

/**
 * Processing Result
 */
export interface ProcessingResult {
  /** Processing success */
  success: boolean;
  /** Event ID */
  eventId: string;
  /** Final status */
  finalStatus: EventProcessingStatus;
  /** Processing stages completed */
  stagesCompleted: string[];
  /** Total processing duration */
  duration: number;
  /** Generated artifacts */
  artifacts: {
    normalizedEvent?: NormalizedEvent;
    enrichedEvent?: EnrichedEvent;
    correlatedEvents?: CorrelatedEvent[];
    threatAssessment?: any;
  };
  /** Processing errors */
  errors: Array<{
    stage: string;
    error: string;
    retryable: boolean;
  }>;
  /** Quality metrics */
  qualityMetrics: {
    normalizationScore?: number;
    enrichmentScore?: number;
    correlationScore?: number;
    overallScore: number;
  };
}

/**
 * Batch Processing Result
 */
export interface BatchProcessingResult {
  /** Total events processed */
  totalEvents: number;
  /** Successfully processed */
  successfulEvents: number;
  /** Failed events */
  failedEvents: number;
  /** Individual results */
  results: ProcessingResult[];
  /** Batch statistics */
  statistics: {
    averageProcessingTime: number;
    throughput: number;
    errorRate: number;
    qualityDistribution: Record<string, number>;
  };
  /** Correlation results */
  correlationResults: CorrelatedEvent[];
}

/**
 * Event Processing Application Service
 * 
 * Orchestrates the complete event processing pipeline from raw events
 * to enriched, correlated events with threat assessment.
 * 
 * Key responsibilities:
 * - Event processing workflow orchestration
 * - Quality assurance and validation
 * - Error handling and retry logic
 * - Performance monitoring and optimization
 * - Batch processing coordination
 */
@Injectable()
export class EventProcessingService {
  private readonly logger = new Logger(EventProcessingService.name);
  private readonly defaultConfig: EventProcessingConfig = {
    enableNormalization: true,
    enableEnrichment: true,
    enableCorrelation: true,
    enableThreatAssessment: true,
    timeoutMs: 30000,
    maxRetries: 3,
    batchSize: 100,
    qualityThreshold: 50,
  };

  constructor(
    private readonly eventRepository: SecurityEventRepository,
    private readonly threatAssessmentService: ThreatAssessmentService,
    private readonly orchestratorService: SecurityOrchestratorService,
    private readonly eventPublisher: DomainEventPublisher
  ) {}

  /**
   * Process a single security event
   */
  async processEvent(
    event: SecurityEvent,
    config?: Partial<EventProcessingConfig>
  ): Promise<ProcessingResult> {
    const processingConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    const result: ProcessingResult = {
      success: false,
      eventId: event.id.toString(),
      finalStatus: EventProcessingStatus.FAILED,
      stagesCompleted: [],
      duration: 0,
      artifacts: {},
      errors: [],
      qualityMetrics: { overallScore: 0 },
    };

    try {
      this.logger.info('Starting event processing', {
        eventId: event.id.toString(),
        sourceType: event.metadata.source.type,
        config: processingConfig,
      });

      // Update event status to normalizing (start of processing)
      event.updateStatus(EventProcessingStatus.NORMALIZING);
      await this.eventRepository.save(event);

      // Use orchestrator service for processing
      const orchestratorResult = await this.orchestratorService.processEvent(
        event,
        {
          enableNormalization: processingConfig.enableNormalization,
          enableEnrichment: processingConfig.enableEnrichment,
          enableCorrelation: processingConfig.enableCorrelation,
          enableThreatAssessment: processingConfig.enableThreatAssessment,
          timeoutMs: processingConfig.timeoutMs,
          maxRetries: processingConfig.maxRetries,
          priority: this.calculateProcessingPriority(event),
        }
      );

      // Map orchestrator result to our result format
      result.success = orchestratorResult.success;
      result.finalStatus = orchestratorResult.finalStatus;
      result.stagesCompleted = orchestratorResult.stagesCompleted;
      result.artifacts = orchestratorResult.artifacts;
      result.errors = orchestratorResult.errors;
      result.qualityMetrics = orchestratorResult.qualityMetrics;

      // Update event with final status
      event.updateStatus(result.finalStatus);
      await this.eventRepository.save(event);

      // Publish domain events
      await this.eventPublisher.publishAll(event.getDomainEvents());
      event.clearDomainEvents();

      this.logger.info('Event processing completed', {
        eventId: event.id.toString(),
        success: result.success,
        finalStatus: result.finalStatus,
        duration: result.duration,
        qualityScore: result.qualityMetrics.overallScore,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error('Event processing failed', {
        eventId: event.id.toString(),
        error: errorMessage,
        stack: errorStack,
      });

      result.errors.push({
        stage: 'orchestration',
        error: errorMessage,
        retryable: this.isRetryableError(error),
      });

      // Update event status to failed
      event.updateStatus(EventProcessingStatus.FAILED);
      await this.eventRepository.save(event);
    } finally {
      result.duration = Date.now() - startTime;
    }

    return result;
  }

  /**
   * Process multiple events in batch
   */
  async processBatch(
    events: SecurityEvent[],
    config?: Partial<EventProcessingConfig>
  ): Promise<BatchProcessingResult> {
    const processingConfig = { ...this.defaultConfig, ...config };
    const startTime = Date.now();
    
    this.logger.info('Starting batch processing', {
      eventCount: events.length,
      batchSize: processingConfig.batchSize,
      config: processingConfig,
    });

    const results: ProcessingResult[] = [];
    const correlationResults: CorrelatedEvent[] = [];
    let successfulEvents = 0;
    let failedEvents = 0;

    // Process events in batches
    for (let i = 0; i < events.length; i += processingConfig.batchSize) {
      const batch = events.slice(i, i + processingConfig.batchSize);
      
      try {
        // Use orchestrator for batch processing
        const batchResult = await this.orchestratorService.processBatch({
          events: batch,
          config: {
            enableNormalization: processingConfig.enableNormalization,
            enableEnrichment: processingConfig.enableEnrichment,
            enableCorrelation: processingConfig.enableCorrelation,
            enableThreatAssessment: processingConfig.enableThreatAssessment,
            timeoutMs: processingConfig.timeoutMs,
            maxRetries: processingConfig.maxRetries,
            priority: 'normal',
          },
          options: {
            parallel: true,
            batchSize: processingConfig.batchSize,
            enableCorrelation: processingConfig.enableCorrelation,
            correlationWindow: 60, // 60 minutes
          },
        });

        // Process individual results
        for (const [eventId, orchestratorResult] of batchResult.results) {
          const result: ProcessingResult = {
            success: orchestratorResult.success,
            eventId,
            finalStatus: orchestratorResult.finalStatus,
            stagesCompleted: orchestratorResult.stagesCompleted,
            duration: orchestratorResult.totalDuration,
            artifacts: {
              normalizedEvent: orchestratorResult.artifacts.normalizedEvent,
              enrichedEvent: orchestratorResult.artifacts.enrichedEvent,
              correlatedEvents: orchestratorResult.artifacts.correlatedEvents?.map(event => ({
                // Map SecurityEvent to CorrelatedEvent structure
                primaryEventId: (event as any).id,
                relatedEventIds: [],
                allEventIds: [(event as any).id],
                correlationAnalysis: {
                  type: 'temporal',
                  confidence: 0.8,
                  score: 80,
                  indicators: [],
                  patterns: [],
                  metadata: {}
                },
                correlationMetadata: {
                  engineVersion: '1.0.0',
                  algorithmsUsed: ['temporal'],
                  processingDuration: 0,
                  correlatedAt: new Date(),
                  dataSources: [],
                  rulesApplied: [],
                  performanceMetrics: {
                    eventsProcessed: 1,
                    correlationsFound: 1,
                    falsePositives: 0,
                    processingRate: 1
                  }
                },
                confidence: 'HIGH' as any,
                score: 80,
                errors: []
              } as any)) || [],
              threatAssessment: orchestratorResult.artifacts.threats,
            },
            errors: orchestratorResult.errors.map(error => ({
              stage: error.stage,
              error: error.message,
              retryable: error.retryable,
            })),
            qualityMetrics: orchestratorResult.qualityMetrics,
          };

          results.push(result);

          if (result.success) {
            successfulEvents++;
          } else {
            failedEvents++;
          }
        }

        // Add correlation results (convert CorrelationResult to CorrelatedEvent)
        if (batchResult.correlationResults) {
          const convertedResults = batchResult.correlationResults.map(result => ({
            // Convert CorrelationResult to CorrelatedEvent structure using available properties
            primaryEventId: (result as any).eventIds?.[0] || 'unknown',
            relatedEventIds: (result as any).eventIds?.slice(1) || [],
            allEventIds: (result as any).eventIds || [],
            correlationAnalysis: {
              type: (result as any).type || 'temporal',
              confidence: (result as any).confidence || 0.8,
              score: (result as any).score || 80,
              indicators: [],
              patterns: [],
              metadata: (result as any).commonAttributes || {}
            },
            correlationMetadata: {
              engineVersion: '1.0.0',
              algorithmsUsed: ['temporal'],
              processingDuration: 0,
              correlatedAt: new Date(),
              dataSources: [],
              rulesApplied: [],
              performanceMetrics: {
                eventsProcessed: 1,
                correlationsFound: 1,
                falsePositives: 0,
                processingRate: 1
              }
            },
            confidence: 'HIGH' as any,
            score: (result as any).score || 80,
            errors: []
          } as any));
          correlationResults.push(...convertedResults);
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.logger.error('Batch processing failed', {
          batchIndex: Math.floor(i / processingConfig.batchSize),
          batchSize: batch.length,
          error: errorMessage,
        });

        // Mark all events in batch as failed
        for (const event of batch) {
          results.push({
            success: false,
            eventId: (event as any).id.toString(),
            finalStatus: EventProcessingStatus.FAILED,
            stagesCompleted: [],
            duration: 0,
            artifacts: {},
            errors: [{
              stage: 'batch_processing',
              error: error.message,
              retryable: this.isRetryableError(error),
            }],
            qualityMetrics: { overallScore: 0 },
          });
          failedEvents++;
        }
      }
    }

    const totalDuration = Date.now() - startTime;
    const statistics = this.calculateBatchStatistics(results, totalDuration);

    this.logger.info('Batch processing completed', {
      totalEvents: events.length,
      successfulEvents,
      failedEvents,
      correlationCount: correlationResults.length,
      duration: totalDuration,
      throughput: statistics.throughput,
    });

    return {
      totalEvents: events.length,
      successfulEvents,
      failedEvents,
      results,
      statistics,
      correlationResults,
    };
  }

  /**
   * Reprocess a failed event
   */
  async reprocessEvent(
    eventId: string,
    config?: Partial<EventProcessingConfig>
  ): Promise<ProcessingResult> {
    const event = await this.eventRepository.findById(eventId);
    if (!event) {
      throw new Error(`Event not found: ${eventId}`);
    }

    this.logger.info('Reprocessing event', {
      eventId,
      currentStatus: event.status,
    });

    // Reset event status to reprocessing
    event.updateStatus(EventProcessingStatus.REPROCESSING);
    await this.eventRepository.save(event);

    return this.processEvent(event, config);
  }

  /**
   * Get processing statistics
   */
  async getProcessingStatistics(
    timeRange?: { from: Date; to: Date }
  ): Promise<{
    totalEvents: number;
    successfulEvents: number;
    failedEvents: number;
    averageProcessingTime: number;
    throughput: number;
    errorRate: number;
    qualityMetrics: {
      averageNormalizationScore: number;
      averageEnrichmentScore: number;
      averageCorrelationScore: number;
      averageOverallScore: number;
    };
    statusDistribution: Record<EventProcessingStatus, number>;
  }> {
    // This would typically query the repository for statistics
    // For now, return mock data structure
    return {
      totalEvents: 0,
      successfulEvents: 0,
      failedEvents: 0,
      averageProcessingTime: 0,
      throughput: 0,
      errorRate: 0,
      qualityMetrics: {
        averageNormalizationScore: 0,
        averageEnrichmentScore: 0,
        averageCorrelationScore: 0,
        averageOverallScore: 0,
      },
      statusDistribution: {} as Record<EventProcessingStatus, number>,
    };
  }

  /**
   * Get processing health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      processingQueue: {
        pending: number;
        processing: number;
        failed: number;
      };
      performance: {
        averageProcessingTime: number;
        throughput: number;
        errorRate: number;
      };
      resources: {
        cpu: number;
        memory: number;
        storage: number;
      };
    };
  }> {
    // Get health status from orchestrator
    const orchestratorHealth = await this.orchestratorService.getHealthStatus();
    
    return {
      status: orchestratorHealth.status,
      details: {
        processingQueue: {
          pending: orchestratorHealth.performance.queueDepth,
          processing: 0, // Would be calculated from active processing
          failed: 0, // Would be calculated from failed events
        },
        performance: {
          averageProcessingTime: orchestratorHealth.performance.latency,
          throughput: orchestratorHealth.performance.throughput,
          errorRate: orchestratorHealth.performance.errorRate,
        },
        resources: {
          cpu: orchestratorHealth.resources.cpu,
          memory: orchestratorHealth.resources.memory,
          storage: orchestratorHealth.resources.storage,
        },
      },
    };
  }

  /**
   * Calculate processing priority based on event characteristics
   */
  private calculateProcessingPriority(event: SecurityEvent): 'low' | 'normal' | 'high' | 'critical' {
    // High priority for critical events
    if (event.rawData.severity === 'critical' || event.rawData.priority === 'high') {
      return 'critical';
    }

    // High priority for security-related sources
    const highPrioritySources = ['edr', 'ids_ips', 'deception', 'threat_intelligence'];
    if (highPrioritySources.includes(event.metadata.source.type.toLowerCase())) {
      return 'high';
    }

    // Medium priority for authentication and network events
    const mediumPriorityCategories = ['authentication', 'network', 'malware'];
    if (mediumPriorityCategories.some(cat => 
      event.rawData.category?.toLowerCase().includes(cat) ||
      event.rawData.event_type?.toLowerCase().includes(cat)
    )) {
      return 'normal';
    }

    return 'low';
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    const retryableErrors = [
      'timeout',
      'network',
      'service_unavailable',
      'rate_limit',
      'temporary_failure',
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError)
    );
  }

  /**
   * Calculate batch processing statistics
   */
  private calculateBatchStatistics(
    results: ProcessingResult[],
    totalDuration: number
  ): {
    averageProcessingTime: number;
    throughput: number;
    errorRate: number;
    qualityDistribution: Record<string, number>;
  } {
    const totalEvents = results.length;
    const successfulEvents = results.filter(r => r.success).length;
    const totalProcessingTime = results.reduce((sum, r) => sum + r.duration, 0);

    const qualityScores = results
      .filter(r => r.success)
      .map(r => r.qualityMetrics.overallScore);

    const qualityDistribution = {
      excellent: qualityScores.filter(s => s >= 90).length,
      good: qualityScores.filter(s => s >= 70 && s < 90).length,
      fair: qualityScores.filter(s => s >= 50 && s < 70).length,
      poor: qualityScores.filter(s => s < 50).length,
    };

    return {
      averageProcessingTime: totalEvents > 0 ? totalProcessingTime / totalEvents : 0,
      throughput: totalDuration > 0 ? (totalEvents / totalDuration) * 1000 : 0, // events per second
      errorRate: totalEvents > 0 ? ((totalEvents - successfulEvents) / totalEvents) * 100 : 0,
      qualityDistribution,
    };
  }

  /**
   * Optimize processing configuration based on performance metrics
   */
  async optimizeConfiguration(): Promise<EventProcessingConfig> {
    const health = await this.getHealthStatus();
    const stats = await this.getProcessingStatistics();

    const optimizedConfig = { ...this.defaultConfig };

    // Adjust batch size based on throughput
    if (health.details.performance.throughput < 10) {
      optimizedConfig.batchSize = Math.max(50, optimizedConfig.batchSize - 25);
    } else if (health.details.performance.throughput > 50) {
      optimizedConfig.batchSize = Math.min(200, optimizedConfig.batchSize + 25);
    }

    // Adjust timeout based on average processing time
    if (health.details.performance.averageProcessingTime > 20000) {
      optimizedConfig.timeoutMs = Math.min(60000, optimizedConfig.timeoutMs + 10000);
    }

    // Adjust quality threshold based on error rate
    if (health.details.performance.errorRate > 10) {
      optimizedConfig.qualityThreshold = Math.max(30, optimizedConfig.qualityThreshold - 10);
    }

    this.logger.info('Configuration optimized', {
      originalConfig: this.defaultConfig,
      optimizedConfig,
      performanceMetrics: health.details.performance,
    });

    return optimizedConfig;
  }
}
