"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const tslib_1 = require("tslib");
const common_1 = require("@nestjs/common");
const redis_constants_1 = require("./redis.constants");
const utils_1 = require("../utils");
const errors_1 = require("../errors");
/**
 * Manager for redis connections.
 */
let RedisService = class RedisService {
    constructor(clients) {
        this.clients = clients;
    }
    /**
     * Retrieves a redis connection by namespace.
     * However, if the query does not find a connection, it returns ClientNotFoundError: No Connection found error.
     *
     * @param namespace - The namespace
     * @returns A redis connection
     */
    getOrThrow(namespace = redis_constants_1.DEFAULT_REDIS) {
        const client = this.clients.get(namespace);
        if (!client)
            throw new errors_1.ClientNotFoundError((0, utils_1.parseNamespace)(namespace));
        return client;
    }
    /**
     * Retrieves a redis connection by namespace, if the query does not find a connection, it returns `null`;
     *
     * @param namespace - The namespace
     * @returns A redis connection or nil
     */
    getOrNil(namespace = redis_constants_1.DEFAULT_REDIS) {
        const client = this.clients.get(namespace);
        if (!client)
            return null;
        return client;
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__param(0, (0, common_1.Inject)(redis_constants_1.REDIS_CLIENTS)),
    tslib_1.__metadata("design:paramtypes", [Object])
], RedisService);
