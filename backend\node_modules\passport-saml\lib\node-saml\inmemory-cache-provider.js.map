{"version": 3, "file": "inmemory-cache-provider.js", "sourceRoot": "", "sources": ["../../src/node-saml/inmemory-cache-provider.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAWH,MAAa,aAAa;IAIxB,YAAY,OAAsC;;QAChD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,OAAO;YACV,qBAAqB,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,qBAAqB,mCAAI,QAAQ,EAAE,WAAW;SAC/E,CAAC;QAEF,wBAAwB;QACxB,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBACnB,IACE,KAAK;oBACL,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EACtF;oBACA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;iBACvB;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAEvC,wGAAwG;QACxG,eAAe,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CAAC,GAAW,EAAE,KAAa;QACxC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG;gBACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE;gBAC/B,KAAK,EAAE,KAAK;aACb,CAAC;YACF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,GAAW;QACxB,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;SAClC;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,WAAW,CAAC,GAAW;QAC3B,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC3B,OAAO,GAAG,CAAC;SACZ;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;CACF;AAzED,sCAyEC", "sourcesContent": ["/**\n * Simple in memory cache provider.  To be used to store state of requests that needs\n * to be validated/checked when a response is received.\n *\n * This is the default implementation of a cache provider used by Passport-SAML.  For\n * multiple server instances/load balanced scenarios (I.e. the SAML request could have\n * been generated from a different server/process handling the SAML response) this\n * implementation will NOT be sufficient.\n *\n * The caller should provide their own implementation for a cache provider as defined\n * in the config options for Passport-SAML.\n * @param options\n * @constructor\n */\n\nexport interface CacheItem {\n  value: string;\n  createdAt: number;\n}\n\ninterface CacheProviderOptions {\n  keyExpirationPeriodMs: number;\n}\n\nexport class CacheProvider {\n  cacheKeys: Record<string, CacheItem>;\n  options: CacheProviderOptions;\n\n  constructor(options: Partial<CacheProviderOptions>) {\n    this.cacheKeys = {};\n\n    this.options = {\n      ...options,\n      keyExpirationPeriodMs: options?.keyExpirationPeriodMs ?? 28800000, // 8 hours,\n    };\n\n    // Expire old cache keys\n    const expirationTimer = setInterval(() => {\n      const nowMs = new Date().getTime();\n      const keys = Object.keys(this.cacheKeys);\n      keys.forEach((key) => {\n        if (\n          nowMs >=\n          new Date(this.cacheKeys[key].createdAt).getTime() + this.options.keyExpirationPeriodMs\n        ) {\n          this.removeAsync(key);\n        }\n      });\n    }, this.options.keyExpirationPeriodMs);\n\n    // we only want this to run if the process is still open; it shouldn't hold the process open (issue #68)\n    expirationTimer.unref();\n  }\n\n  /**\n   * Store an item in the cache, using the specified key and value.\n   * Internally will keep track of the time the item was added to the cache\n   * @param id\n   * @param value\n   */\n  async saveAsync(key: string, value: string): Promise<CacheItem | null> {\n    if (!this.cacheKeys[key]) {\n      this.cacheKeys[key] = {\n        createdAt: new Date().getTime(),\n        value: value,\n      };\n      return this.cacheKeys[key];\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Returns the value of the specified key in the cache\n   * @param id\n   * @returns {boolean}\n   */\n  async getAsync(key: string): Promise<string | null> {\n    if (this.cacheKeys[key]) {\n      return this.cacheKeys[key].value;\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Removes an item from the cache if it exists\n   * @param key\n   */\n  async removeAsync(key: string): Promise<string | null> {\n    if (this.cacheKeys[key]) {\n      delete this.cacheKeys[key];\n      return key;\n    } else {\n      return null;\n    }\n  }\n}\n"]}