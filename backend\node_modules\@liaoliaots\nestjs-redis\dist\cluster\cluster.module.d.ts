import { DynamicModule, OnApplicationShutdown } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { ClusterModuleOptions, ClusterModuleAsyncOptions } from './interfaces';
export declare class ClusterModule implements OnApplicationShutdown {
    private moduleRef;
    constructor(moduleRef: ModuleRef);
    /**
     * Registers the module synchronously.
     *
     * @param options - The module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRoot(options: ClusterModuleOptions, isGlobal?: boolean): DynamicModule;
    /**
     * Registers the module asynchronously.
     *
     * @param options - The async module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRootAsync(options: ClusterModuleAsyncOptions, isGlobal?: boolean): DynamicModule;
    onApplicationShutdown(): Promise<void>;
}
