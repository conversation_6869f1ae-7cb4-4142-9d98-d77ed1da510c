import { BaseAggregateRoot } from '../../../../shared-kernel/domain/base-aggregate-root';
import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { EventMetadata } from '../../value-objects/event-metadata/event-metadata.value-object';
import { EventProcessingStatus, EventProcessingStatusUtils } from '../../enums/event-processing-status.enum';
import { SecurityEventCreatedEvent } from '../../events/security-event-created.event';
import { SecurityEventStatusChangedEvent } from '../../events/security-event-status-changed.event';

/**
 * Security Event Properties
 */
export interface SecurityEventProps {
  /** Event metadata (timestamp, source, etc.) */
  metadata: EventMetadata;
  /** Current processing status */
  status: EventProcessingStatus;
  /** Raw event data as received */
  rawData: Record<string, any>;
  /** Event title/summary */
  title: string;
  /** Detailed event description */
  description?: string;
  /** Event category */
  category?: string;
  /** Event subcategory */
  subcategory?: string;
  /** Processing error message if failed */
  errorMessage?: string;
  /** Number of processing attempts */
  processingAttempts: number;
  /** When processing was last attempted */
  lastProcessedAt?: Date;
  /** Processing duration in milliseconds */
  processingDuration?: number;
  /** Event tags for classification */
  tags: string[];
  /** Custom attributes */
  attributes: Record<string, any>;
}

/**
 * Security Event Entity
 * 
 * Represents a security event in the system with full lifecycle tracking.
 * Serves as the aggregate root for event processing workflows.
 * 
 * Key responsibilities:
 * - Event lifecycle management
 * - Status transitions and validation
 * - Processing attempt tracking
 * - Domain event publishing
 * - Business rule enforcement
 * 
 * Business Rules:
 * - Events can only transition to valid next statuses
 * - Failed events can be retried with limits
 * - Terminal statuses cannot be changed
 * - Processing attempts are tracked and limited
 */
export class SecurityEvent extends BaseAggregateRoot<SecurityEventProps> {
  private static readonly MAX_PROCESSING_ATTEMPTS = 3;
  private static readonly MAX_TITLE_LENGTH = 255;
  private static readonly MAX_DESCRIPTION_LENGTH = 2000;

  constructor(props: SecurityEventProps, id?: UniqueEntityId) {
    super(props, id);
  }

  protected validate(): void {
    if (!this.props.metadata) {
      throw new Error('Security event must have metadata');
    }

    if (!this.props.status) {
      throw new Error('Security event must have a status');
    }

    if (!Object.values(EventProcessingStatus).includes(this.props.status)) {
      throw new Error(`Invalid event processing status: ${this.props.status}`);
    }

    if (!this.props.rawData || Object.keys(this.props.rawData).length === 0) {
      throw new Error('Security event must have raw data');
    }

    if (!this.props.title || this.props.title.trim().length === 0) {
      throw new Error('Security event must have a title');
    }

    if (this.props.title.length > SecurityEvent.MAX_TITLE_LENGTH) {
      throw new Error(`Event title cannot exceed ${SecurityEvent.MAX_TITLE_LENGTH} characters`);
    }

    if (this.props.description && this.props.description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {
      throw new Error(`Event description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);
    }

    if (this.props.processingAttempts < 0) {
      throw new Error('Processing attempts cannot be negative');
    }

    if (this.props.processingAttempts > SecurityEvent.MAX_PROCESSING_ATTEMPTS) {
      throw new Error(`Processing attempts cannot exceed ${SecurityEvent.MAX_PROCESSING_ATTEMPTS}`);
    }

    if (this.props.processingDuration !== undefined && this.props.processingDuration < 0) {
      throw new Error('Processing duration cannot be negative');
    }
  }

  /**
   * Create a new security event
   */
  static create(
    metadata: EventMetadata,
    rawData: Record<string, any>,
    title: string,
    options?: {
      description?: string;
      category?: string;
      subcategory?: string;
      tags?: string[];
      attributes?: Record<string, any>;
    }
  ): SecurityEvent {
    const props: SecurityEventProps = {
      metadata,
      status: EventProcessingStatus.RAW,
      rawData,
      title: title.trim(),
      description: options?.description?.trim(),
      category: options?.category?.trim(),
      subcategory: options?.subcategory?.trim(),
      processingAttempts: 0,
      tags: options?.tags || [],
      attributes: options?.attributes || {},
    };

    const event = new SecurityEvent(props);
    
    // Publish domain event
    event.addDomainEvent(new SecurityEventCreatedEvent(
      event.id,
      {
        eventId: event.id.toString(),
        title: event.props.title,
        category: event.props.category,
        sourceType: event.props.metadata.source.type,
        sourceIdentifier: event.props.metadata.source.identifier,
        timestamp: event.props.metadata.timestamp.toISOString(),
      }
    ));

    return event;
  }

  /**
   * Get event metadata
   */
  get metadata(): EventMetadata {
    return this.props.metadata;
  }

  /**
   * Get current status
   */
  get status(): EventProcessingStatus {
    return this.props.status;
  }

  /**
   * Get raw event data
   */
  get rawData(): Record<string, any> {
    return { ...this.props.rawData };
  }

  /**
   * Get event title
   */
  get title(): string {
    return this.props.title;
  }

  /**
   * Get event description
   */
  get description(): string | undefined {
    return this.props.description;
  }

  /**
   * Get event category
   */
  get category(): string | undefined {
    return this.props.category;
  }

  /**
   * Get event subcategory
   */
  get subcategory(): string | undefined {
    return this.props.subcategory;
  }

  /**
   * Update event status
   */
  updateStatus(newStatus: EventProcessingStatus): void {
    // Validate status transition
    if (!EventProcessingStatusUtils.isValidTransition(this.props.status, newStatus)) {
      throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);
    }

    const oldStatus = this.props.status;
    this.props.status = newStatus;
    this.props.lastProcessedAt = new Date();

    // Increment processing attempts for certain statuses
    if (newStatus === EventProcessingStatus.NORMALIZING ||
        newStatus === EventProcessingStatus.ENRICHING ||
        newStatus === EventProcessingStatus.CORRELATING ||
        newStatus === EventProcessingStatus.ANALYZING ||
        newStatus === EventProcessingStatus.REPROCESSING) {
      this.props.processingAttempts++;
    }

    // Publish domain event for status change
    this.addDomainEvent(new SecurityEventStatusChangedEvent(
      this.id,
      {
        eventId: this.id.toString(),
        oldStatus,
        newStatus,
        timestamp: new Date().toISOString(),
        processingAttempts: this.props.processingAttempts,
      }
    ));

    this.validate();
  }

  /**
   * Get error message
   */
  get errorMessage(): string | undefined {
    return this.props.errorMessage;
  }

  /**
   * Get processing attempts count
   */
  get processingAttempts(): number {
    return this.props.processingAttempts;
  }

  /**
   * Get last processed timestamp
   */
  get lastProcessedAt(): Date | undefined {
    return this.props.lastProcessedAt;
  }

  /**
   * Get processing duration
   */
  get processingDuration(): number | undefined {
    return this.props.processingDuration;
  }

  /**
   * Get event tags
   */
  get tags(): string[] {
    return [...this.props.tags];
  }

  /**
   * Get event attributes
   */
  get attributes(): Record<string, any> {
    return { ...this.props.attributes };
  }

  /**
   * Check if event is in terminal status
   */
  isTerminal(): boolean {
    const terminalStatuses = [
      EventProcessingStatus.RESOLVED,
      EventProcessingStatus.ARCHIVED,
      EventProcessingStatus.FAILED,
      EventProcessingStatus.DISCARDED,
      EventProcessingStatus.SKIPPED,
      EventProcessingStatus.TIMEOUT,
    ];
    return terminalStatuses.includes(this.props.status);
  }

  /**
   * Check if event is in progress
   */
  isInProgress(): boolean {
    const inProgressStatuses = [
      EventProcessingStatus.NORMALIZING,
      EventProcessingStatus.ENRICHING,
      EventProcessingStatus.CORRELATING,
      EventProcessingStatus.ANALYZING,
      EventProcessingStatus.INVESTIGATING,
      EventProcessingStatus.REPROCESSING,
    ];
    return inProgressStatuses.includes(this.props.status);
  }

  /**
   * Check if event requires attention
   */
  requiresAttention(): boolean {
    const attentionStatuses = [
      EventProcessingStatus.PENDING_REVIEW,
      EventProcessingStatus.INVESTIGATING,
      EventProcessingStatus.FAILED,
      EventProcessingStatus.ON_HOLD,
      EventProcessingStatus.TIMEOUT,
    ];
    return attentionStatuses.includes(this.props.status);
  }

  /**
   * Check if event can be retried
   */
  canRetry(): boolean {
    return this.props.status === EventProcessingStatus.FAILED &&
           this.props.processingAttempts < SecurityEvent.MAX_PROCESSING_ATTEMPTS;
  }

  /**
   * Check if event has specific tag
   */
  hasTag(tag: string): boolean {
    return this.props.tags.includes(tag);
  }

  /**
   * Get attribute value
   */
  getAttribute<T = any>(key: string): T | undefined {
    return this.props.attributes[key] as T;
  }

  /**
   * Change event status
   */
  changeStatus(
    newStatus: EventProcessingStatus,
    options?: {
      errorMessage?: string;
      processingDuration?: number;
    }
  ): void {
    if (this.isTerminal()) {
      throw new Error(`Cannot change status from terminal state: ${this.props.status}`);
    }

    if (!this.isValidStatusTransition(newStatus)) {
      throw new Error(`Invalid status transition from ${this.props.status} to ${newStatus}`);
    }

    const oldStatus = this.props.status;
    this.props.status = newStatus;
    this.props.lastProcessedAt = new Date();

    if (options?.errorMessage) {
      this.props.errorMessage = options.errorMessage;
    }

    if (options?.processingDuration !== undefined) {
      this.props.processingDuration = options.processingDuration;
    }

    // Increment processing attempts for certain statuses
    if (this.shouldIncrementAttempts(newStatus)) {
      this.props.processingAttempts++;
    }

    // Publish status change event
    this.addDomainEvent(new SecurityEventStatusChangedEvent(
      this.id,
      {
        eventId: this.id.toString(),
        oldStatus,
        newStatus,
        processingAttempts: this.props.processingAttempts,
        errorMessage: options?.errorMessage,
        timestamp: new Date().toISOString(),
      }
    ));
  }

  private isValidStatusTransition(newStatus: EventProcessingStatus): boolean {
    const validTransitions: Record<EventProcessingStatus, EventProcessingStatus[]> = {
      [EventProcessingStatus.RAW]: [
        EventProcessingStatus.NORMALIZING,
        EventProcessingStatus.SKIPPED,
        EventProcessingStatus.FAILED,
      ],
      [EventProcessingStatus.NORMALIZING]: [
        EventProcessingStatus.NORMALIZED,
        EventProcessingStatus.FAILED,
        EventProcessingStatus.TIMEOUT,
      ],
      [EventProcessingStatus.NORMALIZED]: [
        EventProcessingStatus.ENRICHING,
        EventProcessingStatus.SKIPPED,
        EventProcessingStatus.FAILED,
      ],
      [EventProcessingStatus.ENRICHING]: [
        EventProcessingStatus.ENRICHED,
        EventProcessingStatus.FAILED,
        EventProcessingStatus.TIMEOUT,
      ],
      [EventProcessingStatus.ENRICHED]: [
        EventProcessingStatus.CORRELATING,
        EventProcessingStatus.ANALYZING,
        EventProcessingStatus.FAILED,
      ],
      [EventProcessingStatus.CORRELATING]: [
        EventProcessingStatus.CORRELATED,
        EventProcessingStatus.FAILED,
        EventProcessingStatus.TIMEOUT,
      ],
      [EventProcessingStatus.CORRELATED]: [
        EventProcessingStatus.ANALYZING,
        EventProcessingStatus.FAILED,
      ],
      [EventProcessingStatus.ANALYZING]: [
        EventProcessingStatus.ANALYZED,
        EventProcessingStatus.FAILED,
        EventProcessingStatus.TIMEOUT,
      ],
      [EventProcessingStatus.ANALYZED]: [
        EventProcessingStatus.PENDING_REVIEW,
        EventProcessingStatus.RESOLVED,
        EventProcessingStatus.DISCARDED,
      ],
      [EventProcessingStatus.PENDING_REVIEW]: [
        EventProcessingStatus.INVESTIGATING,
        EventProcessingStatus.DISCARDED,
        EventProcessingStatus.ON_HOLD,
      ],
      [EventProcessingStatus.INVESTIGATING]: [
        EventProcessingStatus.RESOLVED,
        EventProcessingStatus.ON_HOLD,
        EventProcessingStatus.PENDING_REVIEW,
      ],
      [EventProcessingStatus.ON_HOLD]: [
        EventProcessingStatus.INVESTIGATING,
        EventProcessingStatus.PENDING_REVIEW,
        EventProcessingStatus.DISCARDED,
      ],
      [EventProcessingStatus.FAILED]: [
        EventProcessingStatus.REPROCESSING,
        EventProcessingStatus.DISCARDED,
      ],
      [EventProcessingStatus.TIMEOUT]: [
        EventProcessingStatus.REPROCESSING,
        EventProcessingStatus.DISCARDED,
      ],
      [EventProcessingStatus.REPROCESSING]: [
        EventProcessingStatus.NORMALIZED,
        EventProcessingStatus.FAILED,
      ],
      [EventProcessingStatus.RESOLVED]: [
        EventProcessingStatus.ARCHIVED,
        EventProcessingStatus.REPROCESSING,
      ],
      // Terminal statuses have no valid transitions
      [EventProcessingStatus.ARCHIVED]: [],
      [EventProcessingStatus.DISCARDED]: [],
      [EventProcessingStatus.SKIPPED]: [],
    };

    return validTransitions[this.props.status]?.includes(newStatus) || false;
  }

  private shouldIncrementAttempts(status: EventProcessingStatus): boolean {
    return [
      EventProcessingStatus.NORMALIZING,
      EventProcessingStatus.ENRICHING,
      EventProcessingStatus.CORRELATING,
      EventProcessingStatus.ANALYZING,
      EventProcessingStatus.REPROCESSING,
    ].includes(status);
  }

  /**
   * Add tags to the event
   */
  addTags(tags: string[]): void {
    const newTags = tags.filter(tag => !this.props.tags.includes(tag));
    this.props.tags.push(...newTags);
  }

  /**
   * Remove tags from the event
   */
  removeTags(tags: string[]): void {
    this.props.tags = this.props.tags.filter(tag => !tags.includes(tag));
  }

  /**
   * Set attribute value
   */
  setAttribute(key: string, value: any): void {
    this.props.attributes[key] = value;
  }

  /**
   * Remove attribute
   */
  removeAttribute(key: string): void {
    delete this.props.attributes[key];
  }

  /**
   * Update event description
   */
  updateDescription(description: string): void {
    if (description.length > SecurityEvent.MAX_DESCRIPTION_LENGTH) {
      throw new Error(`Description cannot exceed ${SecurityEvent.MAX_DESCRIPTION_LENGTH} characters`);
    }
    this.props.description = description.trim();
  }

  /**
   * Mark event as failed
   */
  markAsFailed(errorMessage: string, processingDuration?: number): void {
    this.changeStatus(EventProcessingStatus.FAILED, {
      errorMessage,
      processingDuration,
    });
  }

  /**
   * Mark event as resolved
   */
  markAsResolved(processingDuration?: number): void {
    this.changeStatus(EventProcessingStatus.RESOLVED, {
      processingDuration,
    });
  }

  /**
   * Get event age in milliseconds
   */
  getAge(): number {
    return this.props.metadata.timestamp.getAge();
  }

  /**
   * Get processing summary
   */
  getProcessingSummary(): {
    status: string;
    attempts: number;
    maxAttempts: number;
    canRetry: boolean;
    isTerminal: boolean;
    isInProgress: boolean;
    requiresAttention: boolean;
    lastProcessedAt?: string;
    processingDuration?: number;
    errorMessage?: string;
  } {
    return {
      status: this.props.status,
      attempts: this.props.processingAttempts,
      maxAttempts: SecurityEvent.MAX_PROCESSING_ATTEMPTS,
      canRetry: this.canRetry(),
      isTerminal: this.isTerminal(),
      isInProgress: this.isInProgress(),
      requiresAttention: this.requiresAttention(),
      lastProcessedAt: this.props.lastProcessedAt?.toISOString(),
      processingDuration: this.props.processingDuration,
      errorMessage: this.props.errorMessage,
    };
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): Record<string, any> {
    return {
      id: this.id.toString(),
      metadata: this.props.metadata.toJSON(),
      status: this.props.status,
      title: this.props.title,
      description: this.props.description,
      category: this.props.category,
      subcategory: this.props.subcategory,
      tags: this.props.tags,
      attributes: this.props.attributes,
      processingSummary: this.getProcessingSummary(),
      age: this.getAge(),
      createdAt: this.createdAt?.toISOString(),
      updatedAt: this.updatedAt?.toISOString(),
    };
  }
}
