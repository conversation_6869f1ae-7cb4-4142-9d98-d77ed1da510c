2bb5f1bb3e3a27d318cca79a492f29f2
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const fallback_service_1 = require("../fallback.service");
describe('AIFallbackService', () => {
    let service;
    const mockStrategy1 = {
        name: 'strategy1',
        priority: 1,
        condition: (error) => error.message.includes('timeout'),
        handler: () => ({ result: 'fallback1' }),
    };
    const mockStrategy2 = {
        name: 'strategy2',
        priority: 2,
        condition: () => true,
        handler: () => ({ result: 'fallback2' }),
    };
    const defaultConfig = {
        enableFallback: true,
        fallbackStrategies: [mockStrategy1, mockStrategy2],
        cacheEnabled: true,
        cacheTtl: 5000,
        maxCacheSize: 100,
        degradationMode: 'graceful',
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [fallback_service_1.AIFallbackService],
        }).compile();
        service = module.get(fallback_service_1.AIFallbackService);
    });
    afterEach(() => {
        // Clean up all registered providers and cache
        service.clearAllCache();
        service.resetAllMetrics();
    });
    describe('registerProvider', () => {
        it('should register a new provider with fallback configuration', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const config = service.getProviderConfig(providerId);
            expect(config).toEqual(defaultConfig);
        });
        it('should initialize metrics for registered provider', () => {
            const providerId = 'test-provider';
            const providerType = 'openai';
            service.registerProvider(providerId, providerType, defaultConfig);
            const metrics = service.getProviderMetrics(providerId);
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe(providerId);
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('executeFallback', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should throw original error when fallback is disabled', async () => {
            const disabledConfig = {
                ...defaultConfig,
                enableFallback: false,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', disabledConfig);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should execute fallback strategy based on condition', async () => {
            const timeoutError = new Error('timeout occurred');
            const result = await service.executeFallback('test-provider', 'test-operation', timeoutError);
            expect(result).toEqual({ result: 'fallback1' });
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
        });
        it('should execute fallback strategy in priority order', async () => {
            const genericError = new Error('generic error');
            const result = await service.executeFallback('test-provider', 'test-operation', genericError);
            // Should use strategy2 because strategy1 only matches timeout errors
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should fall back to next strategy if first one fails', async () => {
            const failingStrategy = {
                name: 'failing-strategy',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy failed'); },
            };
            const configWithFailingStrategy = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should throw original error if all strategies fail', async () => {
            const failingStrategy1 = {
                name: 'failing-strategy1',
                priority: 1,
                condition: () => true,
                handler: () => { throw new Error('strategy1 failed'); },
            };
            const failingStrategy2 = {
                name: 'failing-strategy2',
                priority: 2,
                condition: () => true,
                handler: () => { throw new Error('strategy2 failed'); },
            };
            const configWithFailingStrategies = {
                ...defaultConfig,
                fallbackStrategies: [failingStrategy1, failingStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithFailingStrategies);
            const originalError = new Error('original error');
            await expect(service.executeFallback('test-provider', 'test-operation', originalError)).rejects.toThrow('original error');
        });
        it('should throw error for unregistered provider', async () => {
            const originalError = new Error('original error');
            await expect(service.executeFallback('unknown-provider', 'test-operation', originalError)).rejects.toThrow('Fallback configuration not registered for provider: unknown-provider');
        });
        it('should handle async strategy handlers', async () => {
            const asyncStrategy = {
                name: 'async-strategy',
                priority: 1,
                condition: () => true,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 10));
                    return { result: 'async-fallback' };
                },
            };
            const configWithAsyncStrategy = {
                ...defaultConfig,
                fallbackStrategies: [asyncStrategy],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithAsyncStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            expect(result).toEqual({ result: 'async-fallback' });
        });
        it('should handle strategy timeout', async () => {
            const slowStrategy = {
                name: 'slow-strategy',
                priority: 1,
                condition: () => true,
                timeout: 50,
                handler: async () => {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    return { result: 'slow-fallback' };
                },
            };
            const configWithSlowStrategy = {
                ...defaultConfig,
                fallbackStrategies: [slowStrategy, mockStrategy2],
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', configWithSlowStrategy);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError);
            // Should fall back to strategy2 after strategy1 times out
            expect(result).toEqual({ result: 'fallback2' });
        });
    });
    describe('executeFallbackWithDetails', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return detailed fallback information', async () => {
            const originalError = new Error('timeout occurred');
            const result = await service.executeFallbackWithDetails('test-provider', 'test-operation', originalError);
            expect(result.result).toEqual({ result: 'fallback1' });
            expect(result.strategy).toBe('strategy1');
            expect(result.fromCache).toBe(false);
            expect(result.degraded).toBe(true);
            expect(result.executionTime).toBeGreaterThanOrEqual(0);
        });
    });
    describe('caching', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should cache response when caching is enabled', () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Verify cache stats
            const stats = service.getCacheStats();
            expect(stats.size).toBe(1);
        });
        it('should return cached result when available', async () => {
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.cacheHits).toBe(1);
        });
        it('should not return expired cached result', async () => {
            const shortTtlConfig = {
                ...defaultConfig,
                cacheTtl: 10, // Very short TTL
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', shortTtlConfig);
            const response = { result: 'cached-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to expire
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            // Should use fallback strategy2, not cached result (strategy1 only matches timeout)
            expect(result).toEqual({ result: 'fallback2' });
        });
        it('should evict oldest entry when cache is full', () => {
            const smallCacheConfig = {
                ...defaultConfig,
                maxCacheSize: 2,
            };
            service.unregisterProvider('test-provider');
            service.registerProvider('test-provider', 'openai', smallCacheConfig);
            // Fill cache to capacity
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            // Add one more to trigger eviction
            service.cacheResponse('test-provider', 'op3', { input: '3' }, { result: '3' });
            const stats = service.getCacheStats();
            expect(stats.size).toBe(2); // Should still be at max capacity
        });
        it('should clear provider cache', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('test-provider', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearProviderCache('test-provider');
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
        it('should clear all cache', () => {
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            service.cacheResponse('provider2', 'op2', { input: '2' }, { result: '2' });
            const clearedCount = service.clearAllCache();
            expect(clearedCount).toBe(2);
            const stats = service.getCacheStats();
            expect(stats.size).toBe(0);
        });
    });
    describe('getProviderMetrics', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return metrics for registered provider', () => {
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics).toBeDefined();
            expect(metrics.providerId).toBe('test-provider');
        });
        it('should return null for unregistered provider', () => {
            const metrics = service.getProviderMetrics('unknown-provider');
            expect(metrics).toBeNull();
        });
        it('should update metrics after fallback execution', async () => {
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
            const metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            expect(metrics.strategyUsage['strategy1']).toBe(1);
            expect(metrics.averageExecutionTime).toBeGreaterThanOrEqual(0);
        });
    });
    describe('getAllProviderMetrics', () => {
        it('should return empty array when no providers registered', () => {
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toEqual([]);
        });
        it('should return metrics for all registered providers', () => {
            service.registerProvider('provider1', 'openai', defaultConfig);
            service.registerProvider('provider2', 'bedrock', defaultConfig);
            const metrics = service.getAllProviderMetrics();
            expect(metrics).toHaveLength(2);
            expect(metrics.map(m => m.providerId)).toContain('provider1');
            expect(metrics.map(m => m.providerId)).toContain('provider2');
        });
    });
    describe('resetProviderMetrics', () => {
        beforeEach(async () => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
            // Generate some metrics
            const originalError = new Error('timeout occurred');
            await service.executeFallback('test-provider', 'test-operation', originalError);
        });
        it('should reset metrics for specific provider', () => {
            // Verify metrics exist
            let metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(1);
            // Reset metrics
            service.resetProviderMetrics('test-provider');
            // Verify metrics are reset
            metrics = service.getProviderMetrics('test-provider');
            expect(metrics.totalFallbacks).toBe(0);
            expect(metrics.strategyUsage).toEqual({});
        });
    });
    describe('updateProviderConfig', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should update provider configuration', () => {
            const newConfig = { cacheTtl: 10000, maxCacheSize: 200 };
            service.updateProviderConfig('test-provider', newConfig);
            const config = service.getProviderConfig('test-provider');
            expect(config.cacheTtl).toBe(10000);
            expect(config.maxCacheSize).toBe(200);
            expect(config.enableFallback).toBe(true); // Should keep existing values
        });
    });
    describe('unregisterProvider', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should remove provider configuration and metrics', () => {
            expect(service.getProviderConfig('test-provider')).toBeDefined();
            expect(service.getProviderMetrics('test-provider')).toBeDefined();
            service.unregisterProvider('test-provider');
            expect(service.getProviderConfig('test-provider')).toBeNull();
            expect(service.getProviderMetrics('test-provider')).toBeNull();
        });
        it('should clear provider cache when unregistering', () => {
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            expect(service.getCacheStats().size).toBe(1);
            service.unregisterProvider('test-provider');
            expect(service.getCacheStats().size).toBe(0);
        });
    });
    describe('getCacheStats', () => {
        beforeEach(() => {
            service.registerProvider('test-provider', 'openai', defaultConfig);
        });
        it('should return cache statistics', async () => {
            // Add some cache entries and generate hits/misses
            service.cacheResponse('test-provider', 'op1', { input: '1' }, { result: '1' });
            const originalError = new Error('original error');
            // Generate cache hit
            await service.executeFallback('test-provider', 'op1', originalError, {
                requestData: { input: '1' },
            });
            // Generate cache miss
            await service.executeFallback('test-provider', 'op2', originalError, {
                requestData: { input: '2' },
            });
            const stats = service.getCacheStats();
            expect(stats.size).toBeGreaterThan(0);
            expect(stats.totalHits).toBe(1);
            expect(stats.totalMisses).toBe(1);
            expect(stats.hitRate).toBe(0.5);
        });
    });
    describe('static factory methods', () => {
        it('should create default fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createDefaultConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.fallbackStrategies).toHaveLength(2);
            expect(config.degradationMode).toBe('graceful');
        });
        it('should create minimal fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createMinimalConfig();
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(false);
            expect(config.fallbackStrategies).toHaveLength(1);
            expect(config.degradationMode).toBe('minimal');
        });
        it('should create cached-only fallback config', () => {
            const config = fallback_service_1.AIFallbackService.createCachedOnlyConfig(300000);
            expect(config.enableFallback).toBe(true);
            expect(config.cacheEnabled).toBe(true);
            expect(config.cacheTtl).toBe(300000);
            expect(config.fallbackStrategies).toHaveLength(0);
            expect(config.degradationMode).toBe('cached_only');
        });
    });
    describe('degradation modes', () => {
        it('should use stale cache in cached_only mode when no strategies work', async () => {
            const cachedOnlyConfig = {
                enableFallback: true,
                fallbackStrategies: [], // No strategies
                cacheEnabled: true,
                cacheTtl: 10, // Very short TTL to make it stale
                maxCacheSize: 100,
                degradationMode: 'cached_only',
            };
            service.registerProvider('test-provider', 'openai', cachedOnlyConfig);
            // Cache a response
            const response = { result: 'stale-response' };
            service.cacheResponse('test-provider', 'test-operation', { input: 'test' }, response);
            // Wait for cache to become stale
            await new Promise(resolve => setTimeout(resolve, 20));
            const originalError = new Error('original error');
            const result = await service.executeFallback('test-provider', 'test-operation', originalError, {
                requestData: { input: 'test' },
            });
            expect(result).toEqual(response);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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