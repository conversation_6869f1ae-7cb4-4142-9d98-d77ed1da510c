{"version": 3, "file": "algorithms.js", "sourceRoot": "", "sources": ["../../src/node-saml/algorithms.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,SAAgB,mBAAmB,CAAC,SAAkB;IACpD,QAAQ,SAAS,EAAE;QACjB,KAAK,QAAQ;YACX,OAAO,mDAAmD,CAAC;QAC7D,KAAK,QAAQ;YACX,OAAO,mDAAmD,CAAC;QAC7D,KAAK,MAAM,CAAC;QACZ;YACE,OAAO,4CAA4C,CAAC;KACvD;AACH,CAAC;AAVD,kDAUC;AAED,SAAgB,kBAAkB,CAAC,SAAkB;IACnD,QAAQ,SAAS,EAAE;QACjB,KAAK,QAAQ;YACX,OAAO,yCAAyC,CAAC;QACnD,KAAK,QAAQ;YACX,OAAO,yCAAyC,CAAC;QACnD,KAAK,MAAM,CAAC;QACZ;YACE,OAAO,wCAAwC,CAAC;KACnD;AACH,CAAC;AAVD,gDAUC;AAED,SAAgB,SAAS,CAAC,SAAkB;IAC1C,QAAQ,SAAS,EAAE;QACjB,KAAK,QAAQ;YACX,OAAO,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,QAAQ;YACX,OAAO,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,MAAM,CAAC;QACZ;YACE,OAAO,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;KACxC;AACH,CAAC;AAVD,8BAUC", "sourcesContent": ["import * as crypto from \"crypto\";\n\nexport function getSigningAlgorithm(shortName?: string): string {\n  switch (shortName) {\n    case \"sha256\":\n      return \"http://www.w3.org/2001/04/xmldsig-more#rsa-sha256\";\n    case \"sha512\":\n      return \"http://www.w3.org/2001/04/xmldsig-more#rsa-sha512\";\n    case \"sha1\":\n    default:\n      return \"http://www.w3.org/2000/09/xmldsig#rsa-sha1\";\n  }\n}\n\nexport function getDigestAlgorithm(shortName?: string): string {\n  switch (shortName) {\n    case \"sha256\":\n      return \"http://www.w3.org/2001/04/xmlenc#sha256\";\n    case \"sha512\":\n      return \"http://www.w3.org/2001/04/xmlenc#sha512\";\n    case \"sha1\":\n    default:\n      return \"http://www.w3.org/2000/09/xmldsig#sha1\";\n  }\n}\n\nexport function getSigner(shortName?: string): crypto.Signer {\n  switch (shortName) {\n    case \"sha256\":\n      return crypto.createSign(\"RSA-SHA256\");\n    case \"sha512\":\n      return crypto.createSign(\"RSA-SHA512\");\n    case \"sha1\":\n    default:\n      return crypto.createSign(\"RSA-SHA1\");\n  }\n}\n"]}