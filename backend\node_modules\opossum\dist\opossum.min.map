{"version": 3, "file": "opossum.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAwB,eAAID,IAE5BD,EAAqB,eAAIC,GAC1B,CATD,CASGK,MAAM,KACT,O,sBCaA,IAOIC,EAPAC,EAAuB,iBAAZC,QAAuBA,QAAU,KAC5CC,EAAeF,GAAwB,mBAAZA,EAAEG,MAC7BH,EAAEG,MACF,SAAsBC,EAAQC,EAAUC,GACxC,OAAOC,SAASC,UAAUL,MAAMM,KAAKL,EAAQC,EAAUC,EACzD,EAIAP,EADEC,GAA0B,mBAAdA,EAAEU,QACCV,EAAEU,QACVC,OAAOC,sBACC,SAAwBR,GACvC,OAAOO,OAAOE,oBAAoBT,GAC/BU,OAAOH,OAAOC,sBAAsBR,GACzC,EAEiB,SAAwBA,GACvC,OAAOO,OAAOE,oBAAoBT,EACpC,EAOF,IAAIW,EAAcC,OAAOC,OAAS,SAAqBC,GACrD,OAAOA,GAAUA,CACnB,EAEA,SAASC,IACPA,EAAaC,KAAKX,KAAKY,KACzB,CACA1B,EAAOD,QAAUyB,EACjBxB,EAAOD,QAAQ4B,KAwYf,SAAcC,EAASC,GACrB,OAAO,IAAIC,SAAQ,SAAUC,EAASC,GACpC,SAASC,EAAcC,GACrBN,EAAQO,eAAeN,EAAMO,GAC7BJ,EAAOE,EACT,CAEA,SAASE,IAC+B,mBAA3BR,EAAQO,gBACjBP,EAAQO,eAAe,QAASF,GAElCF,EAAQ,GAAGM,MAAMvB,KAAKwB,WACxB,CAEAC,EAA+BX,EAASC,EAAMO,EAAU,CAAET,MAAM,IACnD,UAATE,GAMR,SAAuCD,EAASY,GACpB,mBAAfZ,EAAQa,IACjBF,EAA+BX,EAAS,QAASY,EAPO,CAAEb,MAAM,GASpE,CATMe,CAA8Bd,EAASK,EAE3C,GACF,EAxZAT,EAAaA,aAAeA,EAE5BA,EAAaX,UAAU8B,aAAUC,EACjCpB,EAAaX,UAAUgC,aAAe,EACtCrB,EAAaX,UAAUiC,mBAAgBF,EAIvC,IAAIG,EAAsB,GAE1B,SAASC,EAAcC,GACrB,GAAwB,mBAAbA,EACT,MAAM,IAAIC,UAAU,0EAA4ED,EAEpG,CAoCA,SAASE,EAAiBC,GACxB,YAA2BR,IAAvBQ,EAAKN,cACAtB,EAAauB,oBACfK,EAAKN,aACd,CAkDA,SAASO,EAAa5C,EAAQ6C,EAAML,EAAUM,GAC5C,IAAIC,EACAC,EACAC,EA1HsBC,EAgJ1B,GApBAX,EAAcC,QAGCL,KADfa,EAAShD,EAAOkC,UAEdc,EAAShD,EAAOkC,QAAU3B,OAAO4C,OAAO,MACxCnD,EAAOoC,aAAe,SAIKD,IAAvBa,EAAOI,cACTpD,EAAOqD,KAAK,cAAeR,EACfL,EAASA,SAAWA,EAASA,SAAWA,GAIpDQ,EAAShD,EAAOkC,SAElBe,EAAWD,EAAOH,SAGHV,IAAbc,EAEFA,EAAWD,EAAOH,GAAQL,IACxBxC,EAAOoC,kBAeT,GAbwB,mBAAba,EAETA,EAAWD,EAAOH,GAChBC,EAAU,CAACN,EAAUS,GAAY,CAACA,EAAUT,GAErCM,EACTG,EAASK,QAAQd,GAEjBS,EAASM,KAAKf,IAIhBO,EAAIL,EAAiB1C,IACb,GAAKiD,EAASO,OAAST,IAAME,EAASQ,OAAQ,CACpDR,EAASQ,QAAS,EAGlB,IAAIC,EAAI,IAAIC,MAAM,+CACEV,EAASO,OAAS,IAAMI,OAAOf,GADjC,qEAIlBa,EAAEtC,KAAO,8BACTsC,EAAEvC,QAAUnB,EACZ0D,EAAEb,KAAOA,EACTa,EAAEG,MAAQZ,EAASO,OA7KGN,EA8KHQ,EA7KnBI,SAAWA,QAAQC,MAAMD,QAAQC,KAAKb,EA8KxC,CAGF,OAAOlD,CACT,CAaA,SAASgE,IACP,IAAK/C,KAAKgD,MAGR,OAFAhD,KAAKjB,OAAO0B,eAAeT,KAAK4B,KAAM5B,KAAKiD,QAC3CjD,KAAKgD,OAAQ,EACY,IAArBpC,UAAU2B,OACLvC,KAAKuB,SAASnC,KAAKY,KAAKjB,QAC1BiB,KAAKuB,SAASzC,MAAMkB,KAAKjB,OAAQ6B,UAE5C,CAEA,SAASsC,EAAUnE,EAAQ6C,EAAML,GAC/B,IAAI4B,EAAQ,CAAEH,OAAO,EAAOC,YAAQ/B,EAAWnC,OAAQA,EAAQ6C,KAAMA,EAAML,SAAUA,GACjF6B,EAAUL,EAAYM,KAAKF,GAG/B,OAFAC,EAAQ7B,SAAWA,EACnB4B,EAAMF,OAASG,EACRA,CACT,CAyHA,SAASE,EAAWvE,EAAQ6C,EAAM2B,GAChC,IAAIxB,EAAShD,EAAOkC,QAEpB,QAAeC,IAAXa,EACF,MAAO,GAET,IAAIyB,EAAazB,EAAOH,GACxB,YAAmBV,IAAfsC,EACK,GAEiB,mBAAfA,EACFD,EAAS,CAACC,EAAWjC,UAAYiC,GAAc,CAACA,GAElDD,EAsDT,SAAyBE,GAEvB,IADA,IAAIC,EAAM,IAAIC,MAAMF,EAAIlB,QACfqB,EAAI,EAAGA,EAAIF,EAAInB,SAAUqB,EAChCF,EAAIE,GAAKH,EAAIG,GAAGrC,UAAYkC,EAAIG,GAElC,OAAOF,CACT,CA3DIG,CAAgBL,GAAcM,EAAWN,EAAYA,EAAWjB,OACpE,CAmBA,SAASwB,EAAcnC,GACrB,IAAIG,EAAS/B,KAAKiB,QAElB,QAAeC,IAAXa,EAAsB,CACxB,IAAIyB,EAAazB,EAAOH,GAExB,GAA0B,mBAAf4B,EACT,OAAO,EACF,QAAmBtC,IAAfsC,EACT,OAAOA,EAAWjB,MAEtB,CAEA,OAAO,CACT,CAMA,SAASuB,EAAWL,EAAKO,GAEvB,IADA,IAAIC,EAAO,IAAIN,MAAMK,GACZJ,EAAI,EAAGA,EAAII,IAAKJ,EACvBK,EAAKL,GAAKH,EAAIG,GAChB,OAAOK,CACT,CA2CA,SAASpD,EAA+BX,EAASC,EAAMoB,EAAU2C,GAC/D,GAA0B,mBAAfhE,EAAQa,GACbmD,EAAMjE,KACRC,EAAQD,KAAKE,EAAMoB,GAEnBrB,EAAQa,GAAGZ,EAAMoB,OAEd,IAAwC,mBAA7BrB,EAAQiE,iBAYxB,MAAM,IAAI3C,UAAU,6EAA+EtB,GATnGA,EAAQiE,iBAAiBhE,GAAM,SAASiE,EAAaC,GAG/CH,EAAMjE,MACRC,EAAQoE,oBAAoBnE,EAAMiE,GAEpC7C,EAAS8C,EACX,GAGF,CACF,CAraA/E,OAAOiF,eAAezE,EAAc,sBAAuB,CACzD0E,YAAY,EACZC,IAAK,WACH,OAAOpD,CACT,EACAqD,IAAK,SAASL,GACZ,GAAmB,iBAARA,GAAoBA,EAAM,GAAK3E,EAAY2E,GACpD,MAAM,IAAIM,WAAW,kGAAoGN,EAAM,KAEjIhD,EAAsBgD,CACxB,IAGFvE,EAAaC,KAAO,gBAEGmB,IAAjBlB,KAAKiB,SACLjB,KAAKiB,UAAY3B,OAAOsF,eAAe5E,MAAMiB,UAC/CjB,KAAKiB,QAAU3B,OAAO4C,OAAO,MAC7BlC,KAAKmB,aAAe,GAGtBnB,KAAKoB,cAAgBpB,KAAKoB,oBAAiBF,CAC7C,EAIApB,EAAaX,UAAU0F,gBAAkB,SAAyBb,GAChE,GAAiB,iBAANA,GAAkBA,EAAI,GAAKtE,EAAYsE,GAChD,MAAM,IAAIW,WAAW,gFAAkFX,EAAI,KAG7G,OADAhE,KAAKoB,cAAgB4C,EACdhE,IACT,EAQAF,EAAaX,UAAU2F,gBAAkB,WACvC,OAAOrD,EAAiBzB,KAC1B,EAEAF,EAAaX,UAAUiD,KAAO,SAAcR,GAE1C,IADA,IAAI3C,EAAO,GACF2E,EAAI,EAAGA,EAAIhD,UAAU2B,OAAQqB,IAAK3E,EAAKqD,KAAK1B,UAAUgD,IAC/D,IAAImB,EAAoB,UAATnD,EAEXG,EAAS/B,KAAKiB,QAClB,QAAeC,IAAXa,EACFgD,EAAWA,QAA4B7D,IAAjBa,EAAOiD,WAC1B,IAAKD,EACR,OAAO,EAGT,GAAIA,EAAS,CACX,IAAIE,EAGJ,GAFIhG,EAAKsD,OAAS,IAChB0C,EAAKhG,EAAK,IACRgG,aAAcvC,MAGhB,MAAMuC,EAGR,IAAIzE,EAAM,IAAIkC,MAAM,oBAAsBuC,EAAK,KAAOA,EAAGC,QAAU,IAAM,KAEzE,MADA1E,EAAI2E,QAAUF,EACRzE,CACR,CAEA,IAAIM,EAAUiB,EAAOH,GAErB,QAAgBV,IAAZJ,EACF,OAAO,EAET,GAAuB,mBAAZA,EACTjC,EAAaiC,EAASd,KAAMf,OAE5B,KAAImG,EAAMtE,EAAQyB,OACd8C,EAAYvB,EAAWhD,EAASsE,GACpC,IAASxB,EAAI,EAAGA,EAAIwB,IAAOxB,EACzB/E,EAAawG,EAAUzB,GAAI5D,KAAMf,EAHX,CAM1B,OAAO,CACT,EAgEAa,EAAaX,UAAUmG,YAAc,SAAqB1D,EAAML,GAC9D,OAAOI,EAAa3B,KAAM4B,EAAML,GAAU,EAC5C,EAEAzB,EAAaX,UAAU4B,GAAKjB,EAAaX,UAAUmG,YAEnDxF,EAAaX,UAAUoG,gBACnB,SAAyB3D,EAAML,GAC7B,OAAOI,EAAa3B,KAAM4B,EAAML,GAAU,EAC5C,EAoBJzB,EAAaX,UAAUc,KAAO,SAAc2B,EAAML,GAGhD,OAFAD,EAAcC,GACdvB,KAAKe,GAAGa,EAAMsB,EAAUlD,KAAM4B,EAAML,IAC7BvB,IACT,EAEAF,EAAaX,UAAUqG,oBACnB,SAA6B5D,EAAML,GAGjC,OAFAD,EAAcC,GACdvB,KAAKuF,gBAAgB3D,EAAMsB,EAAUlD,KAAM4B,EAAML,IAC1CvB,IACT,EAGJF,EAAaX,UAAUsB,eACnB,SAAwBmB,EAAML,GAC5B,IAAIkE,EAAM1D,EAAQ2D,EAAU9B,EAAG+B,EAK/B,GAHArE,EAAcC,QAGCL,KADfa,EAAS/B,KAAKiB,SAEZ,OAAOjB,KAGT,QAAakB,KADbuE,EAAO1D,EAAOH,IAEZ,OAAO5B,KAET,GAAIyF,IAASlE,GAAYkE,EAAKlE,WAAaA,EACb,MAAtBvB,KAAKmB,aACTnB,KAAKiB,QAAU3B,OAAO4C,OAAO,cAEtBH,EAAOH,GACVG,EAAOtB,gBACTT,KAAKoC,KAAK,iBAAkBR,EAAM6D,EAAKlE,UAAYA,SAElD,GAAoB,mBAATkE,EAAqB,CAGrC,IAFAC,GAAY,EAEP9B,EAAI6B,EAAKlD,OAAS,EAAGqB,GAAK,EAAGA,IAChC,GAAI6B,EAAK7B,KAAOrC,GAAYkE,EAAK7B,GAAGrC,WAAaA,EAAU,CACzDoE,EAAmBF,EAAK7B,GAAGrC,SAC3BmE,EAAW9B,EACX,KACF,CAGF,GAAI8B,EAAW,EACb,OAAO1F,KAEQ,IAAb0F,EACFD,EAAKG,QAiIf,SAAmBH,EAAMI,GACvB,KAAOA,EAAQ,EAAIJ,EAAKlD,OAAQsD,IAC9BJ,EAAKI,GAASJ,EAAKI,EAAQ,GAC7BJ,EAAKK,KACP,CAnIUC,CAAUN,EAAMC,GAGE,IAAhBD,EAAKlD,SACPR,EAAOH,GAAQ6D,EAAK,SAEQvE,IAA1Ba,EAAOtB,gBACTT,KAAKoC,KAAK,iBAAkBR,EAAM+D,GAAoBpE,EAC1D,CAEA,OAAOvB,IACT,EAEJF,EAAaX,UAAU6G,IAAMlG,EAAaX,UAAUsB,eAEpDX,EAAaX,UAAU8G,mBACnB,SAA4BrE,GAC1B,IAAIyD,EAAWtD,EAAQ6B,EAGvB,QAAe1C,KADfa,EAAS/B,KAAKiB,SAEZ,OAAOjB,KAGT,QAA8BkB,IAA1Ba,EAAOtB,eAUT,OATyB,IAArBG,UAAU2B,QACZvC,KAAKiB,QAAU3B,OAAO4C,OAAO,MAC7BlC,KAAKmB,aAAe,QACMD,IAAjBa,EAAOH,KACY,MAAtB5B,KAAKmB,aACTnB,KAAKiB,QAAU3B,OAAO4C,OAAO,aAEtBH,EAAOH,IAEX5B,KAIT,GAAyB,IAArBY,UAAU2B,OAAc,CAC1B,IACI2D,EADAC,EAAO7G,OAAO6G,KAAKpE,GAEvB,IAAK6B,EAAI,EAAGA,EAAIuC,EAAK5D,SAAUqB,EAEjB,oBADZsC,EAAMC,EAAKvC,KAEX5D,KAAKiG,mBAAmBC,GAK1B,OAHAlG,KAAKiG,mBAAmB,kBACxBjG,KAAKiB,QAAU3B,OAAO4C,OAAO,MAC7BlC,KAAKmB,aAAe,EACbnB,IACT,CAIA,GAAyB,mBAFzBqF,EAAYtD,EAAOH,IAGjB5B,KAAKS,eAAemB,EAAMyD,QACrB,QAAkBnE,IAAdmE,EAET,IAAKzB,EAAIyB,EAAU9C,OAAS,EAAGqB,GAAK,EAAGA,IACrC5D,KAAKS,eAAemB,EAAMyD,EAAUzB,IAIxC,OAAO5D,IACT,EAmBJF,EAAaX,UAAUkG,UAAY,SAAmBzD,GACpD,OAAO0B,EAAWtD,KAAM4B,GAAM,EAChC,EAEA9B,EAAaX,UAAUiH,aAAe,SAAsBxE,GAC1D,OAAO0B,EAAWtD,KAAM4B,GAAM,EAChC,EAEA9B,EAAaiE,cAAgB,SAAS7D,EAAS0B,GAC7C,MAAqC,mBAA1B1B,EAAQ6D,cACV7D,EAAQ6D,cAAcnC,GAEtBmC,EAAc3E,KAAKc,EAAS0B,EAEvC,EAEA9B,EAAaX,UAAU4E,cAAgBA,EAiBvCjE,EAAaX,UAAUkH,WAAa,WAClC,OAAOrG,KAAKmB,aAAe,EAAIzC,EAAesB,KAAKiB,SAAW,EAChE,C,2BCvaA3C,EAAOD,QAEP,SAAoBuE,GAClB,IAAM0D,EAAY,GACdC,EAAU3D,EAER4D,EAAM,CACVC,KAAAA,EACAC,QAAAA,EACAC,KAsCF,WACE,QAAIJ,EAAU,IACPE,MAAU,CACnB,GAjCA,OALAnH,OAAOiF,eAAeiC,EAAK,QAAS,CAClC/B,IAAK,SAAAmC,GAAC,OAAIL,CAAO,EACjB/B,YAAY,IAGPgC,EAEP,SAASC,EAAMI,GACb,OAAIN,EAAU,KACVA,EACKnG,QAAQC,QAAQqG,IAElB,IAAItG,SAAQ,SAACC,EAASC,GAC3BgG,EAAUhE,MAAK,SAAAsE,KACXL,EACFlG,EAAQqG,EACV,IACIG,GACFC,YAAW,SAAAF,GACTN,EAAUV,QACV,IAAMpF,EAAM,IAAIkC,MAAM,mBAADjD,OAAoBoH,EAAO,OAChDrG,EAAIuG,KAAO,YACXzG,EAAOE,EACT,GAAGqG,EAEP,GACF,CAEA,SAASH,IACPH,IACID,EAAU/D,OAAS,GACrB+D,EAAUV,OAAVU,EAEJ,CAMF,C,6BCrDa,SAAAU,EAAAC,GAAA,gBAAAA,GAAA,GAAAtD,MAAAuD,QAAAD,GAAA,OAAAE,EAAAF,EAAA,CAAAG,CAAAH,IAAA,SAAAA,GAAA,uBAAAI,QAAA,MAAAJ,EAAAI,OAAAC,WAAA,MAAAL,EAAA,qBAAAtD,MAAA4D,KAAAN,EAAA,CAAAO,CAAAP,IAAA,SAAAA,EAAAQ,GAAA,GAAAR,EAAA,qBAAAA,EAAA,OAAAE,EAAAF,EAAAQ,GAAA,IAAAC,EAAA,GAAAC,SAAAvI,KAAA6H,GAAAtG,MAAA,uBAAA+G,GAAAT,EAAAW,cAAAF,EAAAT,EAAAW,YAAAzH,MAAA,QAAAuH,GAAA,QAAAA,EAAA/D,MAAA4D,KAAAN,GAAA,cAAAS,GAAA,2CAAAf,KAAAe,GAAAP,EAAAF,EAAAQ,QAAA,GAAAI,CAAAZ,IAAA,qBAAAzF,UAAA,wIAAAsG,EAAA,UAAAX,EAAAF,EAAAQ,IAAA,MAAAA,GAAAA,EAAAR,EAAA1E,UAAAkF,EAAAR,EAAA1E,QAAA,QAAAwF,EAAA,EAAA/D,EAAAL,MAAA8D,GAAAM,EAAAN,EAAAM,IAAA/D,EAAA+D,GAAAd,EAAAc,GAAA,OAAA/D,CAAA,UAAAgE,EAAAC,GAAA,OAAAD,EAAA,mBAAAX,QAAA,iBAAAA,OAAAC,SAAA,SAAAW,GAAA,cAAAA,CAAA,WAAAA,GAAA,OAAAA,GAAA,mBAAAZ,QAAAY,EAAAL,cAAAP,QAAAY,IAAAZ,OAAAlI,UAAA,gBAAA8I,CAAA,EAAAD,EAAAC,EAAA,UAAAC,EAAAH,EAAAd,GAAA,QAAAS,EAAA,EAAAA,EAAAT,EAAA1E,OAAAmF,IAAA,KAAAO,EAAAhB,EAAAS,GAAAO,EAAAzD,WAAAyD,EAAAzD,aAAA,EAAAyD,EAAAE,cAAA,YAAAF,IAAAA,EAAAG,UAAA,GAAA9I,OAAAiF,eAAAwD,EAAAM,EAAAJ,EAAA/B,KAAA+B,EAAA,WAAAI,EAAAX,GAAA,IAAA9D,EAAA,SAAA8D,GAAA,aAAAM,EAAAN,KAAAA,EAAA,OAAAA,EAAA,IAAAK,EAAAL,EAAAL,OAAAiB,aAAA,YAAAP,EAAA,KAAAnE,EAAAmE,EAAA3I,KAAAsI,EAAAT,UAAA,aAAAe,EAAApE,GAAA,OAAAA,EAAA,UAAApC,UAAA,uDAAAmB,OAAA+E,EAAA,CAAAa,CAAAb,GAAA,gBAAAM,EAAApE,GAAAA,EAAAA,EAAA,YAAA4E,IAAA,QAAAd,GAAAe,QAAAtJ,UAAAuJ,QAAAtJ,KAAAR,QAAA+J,UAAAF,QAAA,0BAAAf,GAAA,QAAAc,EAAA,mBAAAd,CAAA,cAAAkB,EAAAlB,GAAA,OAAAkB,EAAAtJ,OAAAuJ,eAAAvJ,OAAAsF,eAAAvB,OAAA,SAAAqE,GAAA,OAAAA,EAAAoB,WAAAxJ,OAAAsF,eAAA8C,EAAA,EAAAkB,EAAAlB,EAAA,UAAAqB,EAAArB,EAAAK,GAAA,OAAAgB,EAAAzJ,OAAAuJ,eAAAvJ,OAAAuJ,eAAAxF,OAAA,SAAAqE,EAAAK,GAAA,OAAAL,EAAAoB,UAAAf,EAAAL,CAAA,EAAAqB,EAAArB,EAAAK,EAAA,CAEb,IAAMjI,EAAekJ,EAAQ,GACvBC,EAASD,EAAQ,KACjBE,EAAYF,EAAQ,KACpBG,EAAcH,EAAQ,KAEtBI,EAAQ/B,OAAO,SACfgC,EAAOhC,OAAO,QACdiC,EAASjC,OAAO,UAChBkC,EAAYlC,OAAO,aACnBmC,EAAgBnC,OAAO,iBACvBoC,EAAWpC,OAAO,YAClBqC,EAAoBrC,OAAO,YAC3BsC,EAAStC,OAAO,UAChBuC,EAAOvC,OAAO,QACdwC,EAAQxC,OAAO,SACfyC,EAAUzC,OAAO,WACjB0C,EAAa1C,OAAO,cACpB2C,EAAmB3C,OAAO,oBAC1B4C,EAAY5C,OAAO,aACnB6C,EAAgB7C,OAAO,iBACvB8C,EAAiB9C,OAAO,kBACxB+C,EAAgB/C,OAAO,iBA+HvBgD,EAAc,SAAAC,GAyBlB,SAAAD,EAAaE,GAAsB,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAdC,EAAOtK,UAAA2B,OAAA,QAAArB,IAAAN,UAAA,GAAAA,UAAA,GAAG,CAAC,EAyB9B,GAxMS,SAAA6G,EAAAzD,GAAA,KAAAyD,aAAAzD,GAAA,UAAAxC,UAAA,qCA+KsB2J,CAAA,KAAAd,IAC/BY,EAhLS,SAAAvD,EAAAO,EAAAF,GAAA,OAAAE,EAAAW,EAAAX,GAAA,SAAAP,EAAAK,GAAA,GAAAA,IAAA,UAAAC,EAAAD,IAAA,mBAAAA,GAAA,OAAAA,EAAA,YAAAA,EAAA,UAAAvG,UAAA,4EAAAuG,GAAA,YAAAA,EAAA,UAAAqD,eAAA,oEAAArD,CAAA,CAAAsD,CAAA3D,EAAA,CAAA4D,CAAA5D,EAAAc,IAAA5J,QAAA+J,UAAAV,EAAAF,GAAA,GAAAa,EAAAlB,GAAAE,aAAAK,EAAAnJ,MAAA4I,EAAAK,GAAA,CAgLTwD,CAAA,KAAAlB,IACKa,QAAUA,EACfD,EAAKC,QAAQrE,QAAyB,QAAlB2D,EAAGU,EAAQrE,eAAO,IAAA2D,EAAAA,EAAI,IAC1CS,EAAKC,QAAQM,aAAmC,QAAvBf,EAAGS,EAAQM,oBAAY,IAAAf,EAAAA,EAAI,IACpDQ,EAAKC,QAAQO,yBACqB,QADGf,EACnCQ,EAAQO,gCAAwB,IAAAf,EAAAA,EAAI,GACtCO,EAAKC,QAAQQ,oBAAiD,QAA9Bf,EAAGO,EAAQQ,2BAAmB,IAAAf,EAAAA,EAAI,IAClEM,EAAKC,QAAQS,oBAAiD,QAA9Bf,EAAGM,EAAQS,2BAAmB,IAAAf,EAAAA,EAAI,GAClEK,EAAKC,QAAQU,2BAC2B,IAAtCV,EAAQU,0BACVX,EAAKC,QAAQW,SAAWlM,OAAOmM,UAAUZ,EAAQW,UAC7CX,EAAQW,SACRlM,OAAOoM,iBACXd,EAAKC,QAAQc,YAAcd,EAAQc,aAAgB,SAAApF,GAAC,OAAI,CAAK,EAC7DqE,EAAKC,QAAQe,SAA2B,QAAnBpB,EAAGK,EAAQe,gBAAQ,IAAApB,EAAAA,EAAI,EAC5CI,EAAKC,QAAQgB,YAAiC,QAAtBpB,EAAGI,EAAQgB,mBAAW,IAAApB,EAAAA,EAC3C,mBAAAqB,EAAAvL,UAAA2B,OAAItD,EAAI,IAAA0E,MAAAwI,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJnN,EAAImN,GAAAxL,UAAAwL,GAAA,OAAKC,KAAKC,UAAUrN,EAAK,EACpCgM,EAAKC,QAAQqB,iBAA8C,IAA5BrB,EAAQqB,gBACvCtB,EAAKC,QAAQsB,uBAAyBtB,EAAQsB,uBAC9CvB,EAAKC,QAAQuB,WAAavB,EAAQuB,SAClCxB,EAAKC,QAAQwB,YAAiC,QAAtB3B,EAAGG,EAAQwB,mBAAW,IAAA3B,EAAAA,EAAIE,EAAKC,QAAQrE,QAC/DoE,EAAKC,QAAQyB,iBAAyC,QAAvB3B,EAAAE,EAAQyB,uBAAe,IAAA3B,OAAA,EAAvBA,EAAyB4B,QAAO,SAAA3E,GAAC,MAAI,CAAC,QAAS,UAAW,WAAW4E,SAAS5E,EAAE,MAAK,GAGhHgD,EAAKC,QAAQ4B,MACf,QAAoC5L,IAAhC+J,EAAKC,QAAQ6B,eACf9B,EAAKC,QAAQ6B,eAAiB,IAAI5D,EAAY+B,EAAQ8B,gBACjD,GAA2C,WAAvChF,EAAOiD,EAAKC,QAAQ6B,kBAC5B9B,EAAKC,QAAQ6B,eAAetI,MAC5BwG,EAAKC,QAAQ6B,eAAerI,MAC5BuG,EAAKC,QAAQ6B,eAAeE,MAE7B,MAAM,IAAIzL,UACR,oFAYN,GAPIyJ,EAAKC,QAAQuB,WACfxB,EAAKC,QAAQgC,cAAgB,IAAI/D,EAAY+B,EAAQiC,eAGvDlC,EAAKmC,UAAY,IAAIlE,EAAU+B,EAAKC,QAAQW,WAGvCtB,EACH,MAAM,IAAI/I,UACR,sFAQJ,GAJI0J,EAAQmC,2BAA6BnC,EAAQoC,kBAC/CpC,EAAQoC,gBAAkB,IAAIC,iBAG5BrC,EAAQoC,iBAA4D,mBAAlCpC,EAAQoC,gBAAgBE,MAC5D,MAAM,IAAIhM,UACR,qDA2CJ,GAvCAyJ,EAAKjB,GAAoBrK,OAAOmM,UAAUZ,EAAQuC,iBAC9CvC,EAAQuC,gBACR,EACJxC,EAAKlB,IAAsC,IAAxBmB,EAAQwC,YAGvBzC,EAAKC,QAAQyC,OAEX1C,EAAKC,QAAQyC,kBAAkB1E,EACjCgC,EAAKtB,GAAUsB,EAAKC,QAAQyC,OAE5B1C,EAAKtB,GAAU,IAAIV,EAAO,CAAE2E,MAAO3C,EAAKC,QAAQyC,SAGlD1C,EAAKtB,GAAU,IAAIV,EAAOgC,EAAKC,SAGjCD,EAAK7B,GAASE,EAEV4B,EAAQ/H,OACV8H,EAAKnB,IAAqC,IAA1BoB,EAAQ/H,MAAM0K,QAC9B5C,EAAKlB,GAAcmB,EAAQ/H,MAAM2K,QAAU7C,EAAKlB,GAEhDkB,EAAK3B,IAAmC,IAAzB4B,EAAQ/H,MAAM4K,OAE7B9C,EAAK1B,GAAa0B,EAAKzB,GAAiB0B,EAAQ/H,MAAM6K,WAAY,EAGlE/C,EAAK5B,IAAS4B,EAAK3B,KAAY2B,EAAK1B,GACpC0B,EAAKxB,GAAYyB,EAAQ/H,MAAM8K,WAAY,IAE3ChD,EAAKzB,IAAiB,EACtByB,EAAKnB,IAA+B,IAApBoB,EAAQ2C,SAG1B5C,EAAKvB,GAAqB,KAC1BuB,EAAKrB,GAAQsB,EAAQ/K,MAAQoK,EAAOpK,MAAQ+N,IAC5CjD,EAAKpB,GAASqB,EAAQiD,OAASlD,EAAKrB,GAEhCqB,EAAKlB,GAAa,CACpB,IAAMqE,EAAQnD,EAAKd,GAAkBrD,YACnC,SAAAF,GAAC,OAAKqE,EAAKlB,IAAc,CAAK,GAC9BkB,EAAKC,QAAQQ,qBAEY,mBAAhB0C,EAAMC,OACfD,EAAMC,OAEV,CAGEpD,EAAKV,OADe,mBAAXA,EACK,SAAA3D,GAAC,OAAIxG,QAAQC,QAAQkK,EAAO,EACvBA,EAEjBW,EAAQoD,aAAazL,QAAQmC,MA1QjB,kFA4QhB,IAsBsBuJ,EAtBhBC,EAAY,SAAAC,GAAQ,OACxB,SAACC,EAAQC,GAAO,OAAK1D,EAAKtB,GAAQ6E,UAAUC,EAAUE,EAAQ,GAuChE,SAASC,EAAWL,GAClBA,EAAQnF,GAASG,EACjBgF,EAAQ/E,IAAiB,EACzB+E,EAAQM,gCASRN,EAAQnM,KAAK,WAAYmM,EAAQrD,QAAQM,aAC3C,CA4BC,OA9EDP,EAAKlK,GAAG,UAAWyN,EAAU,cAC7BvD,EAAKlK,GAAG,UAAWyN,EAAU,aAC7BvD,EAAKlK,GAAG,WAAYyN,EAAU,cAC9BvD,EAAKlK,GAAG,UAAWyN,EAAU,aAC7BvD,EAAKlK,GAAG,OAAQyN,EAAU,UAC1BvD,EAAKlK,GAAG,SAAUyN,EAAU,YAC5BvD,EAAKlK,GAAG,WAAYyN,EAAU,cAC9BvD,EAAKlK,GAAG,YAAayN,EAAU,gBAC/BvD,EAAKlK,GAAG,mBAAoByN,EAAU,sBACtCvD,EAAKlK,GAAG,oBAAqByN,EAAU,wBACvCvD,EAAKlK,GAAG,QAAQ,SAAA6F,GAAC,OAAIqE,EAAKtB,GAAQmF,MAAM,IACxC7D,EAAKlK,GAAG,SAAS,SAAA6F,GAAC,OAAIqE,EAAKtB,GAAQoF,OAAO,IAC1C9D,EAAKlK,GAAG,kBAAmByN,EAAU,wBAwCrCvD,EAAKlK,GAAG,SAjCcwN,EAiCKtD,GAhCjBb,GAAiB4E,KAAKC,MACvB,SAAArI,GACL,IAAMwH,EAAQG,EAAQrE,GAAiBpD,YAAW,WAChD8H,EAAUL,EACZ,GAAGA,EAAQrD,QAAQM,cACQ,mBAAhB4C,EAAMC,OACfD,EAAMC,OAEV,IAyBFpD,EAAKlK,GAAG,WAAW,SAAA6F,GACbqE,EAAK+C,UACP/C,EAAK8D,OAET,IAGI9D,EAAKxB,IACPwB,EAAK7B,GAASK,EACdwB,EAAKgD,YACIhD,EAAK3B,GACd2B,EAAK8D,QACI9D,EAAK5B,QAGyBnI,IAAnC+J,EAAKC,QAAQ/H,MAAM+L,aACpBF,KAAKC,MAAQhE,EAAKC,QAAQ/H,MAAM+L,YACjCjE,EAAKC,QAAQM,aACboD,EAAS3D,GAETA,EAAK6D,OAEE7D,EAAK1B,KAEd0B,EAAK7B,GAASG,GACf0B,CACH,CAEA,OAxXW,SAAAvD,EAAAK,GAAA,sBAAAA,GAAA,OAAAA,EAAA,UAAAvG,UAAA,sDAAAkG,EAAAvI,UAAAG,OAAA4C,OAAA6F,GAAAA,EAAA5I,UAAA,CAAAyI,YAAA,CAAA/H,MAAA6H,EAAAU,UAAA,EAAAD,cAAA,KAAA7I,OAAAiF,eAAAmD,EAAA,aAAAU,UAAA,IAAAL,GAAAgB,EAAArB,EAAAK,EAAA,CAwXXoH,CAAA9E,EAAAC,GAxXWvC,EAwXXsC,EAxXWpD,EAwXX,EAAAf,IAAA,gCAAArG,MAKA,WAEIG,KAAKkL,QAAQmC,0BACXrN,KAAKkL,QAAQoC,iBACbtN,KAAKkL,QAAQoC,gBAAgB8B,OAAOC,UAEtCrP,KAAKkL,QAAQoC,gBAAkB,IAAIC,gBAEvC,GAEA,CAAArH,IAAA,QAAArG,MAKA,WACMG,KAAKoJ,KAAWE,IACdtJ,KAAKkK,IACPoF,aAAatP,KAAKkK,IAEpBlK,KAAKoJ,GAASE,EACdtJ,KAAKwJ,IAAiB,EACtBxJ,KAAK6O,gCAKL7O,KAAKoC,KAAK,SAEd,GAEA,CAAA8D,IAAA,OAAArG,MASA,WACMG,KAAKoJ,KAAWC,IAClBrJ,KAAKoJ,GAASC,EACdrJ,KAAKwJ,IAAiB,EAMtBxJ,KAAKoC,KAAK,QAEd,GAEA,CAAA8D,IAAA,WAAArG,MAKA,WAKEG,KAAKoC,KAAK,YAEVpC,KAAKuP,UACLvP,KAAKiG,qBACDjG,KAAKkK,IACPoF,aAAatP,KAAKkK,IAEhBlK,KAAKmK,IACPmF,aAAatP,KAAKmK,IAEpBnK,KAAK2N,OAAOM,WACZjO,KAAKoJ,GAASK,EAGdzJ,KAAKwP,YACP,GAEA,CAAAtJ,IAAA,aAAAzB,IAIA,WACE,OAAOzE,KAAKoJ,KAAWK,CACzB,GAEA,CAAAvD,IAAA,OAAAzB,IAIA,WACE,OAAOzE,KAAK4J,EACd,GAEA,CAAA1D,IAAA,QAAAzB,IAIA,WACE,OAAOzE,KAAK6J,EACd,GAEA,CAAA3D,IAAA,eAAAzB,IAIA,WACE,OAAOzE,KAAKwJ,EACd,GAEA,CAAAtD,IAAA,SAAAzB,IAIA,WACE,OAAOzE,KAAKoJ,KAAWE,CACzB,GAEA,CAAApD,IAAA,SAAAzB,IAIA,WACE,OAAOzE,KAAKoJ,KAAWC,CACzB,GAEA,CAAAnD,IAAA,WAAAzB,IAIA,WACE,OAAOzE,KAAKoJ,KAAWG,CACzB,GAEA,CAAArD,IAAA,SAAAzB,IAIA,WACE,OAAOzE,KAAK2J,EACd,GAEA,CAAAzD,IAAA,QAAAzB,IAKA,WACE,OAAOzE,KAAK2J,GAAQiE,KACtB,GAAC,CAAA1H,IAAA,SAAArG,MAED,WACE,MAAO,CACLsD,MAAO,CACLhD,KAAMH,KAAKG,KACX0N,QAAS7N,KAAK6N,QACdE,OAAQ/N,KAAK+N,OACbe,KAAM9O,KAAKyP,OACXzB,SAAUhO,KAAKgO,SACfF,OAAQ9N,KAAK8N,OACbG,SAAUjO,KAAK0P,WACfR,YAAalP,KAAKoK,IAEpBuD,OAAQ3N,KAAK2N,OAAOC,MAExB,GAEA,CAAA1H,IAAA,UAAAzB,IAIA,WACE,OAAOzE,KAAK8J,EACd,GAEA,CAAA5D,IAAA,SAAAzB,IAIA,WACE,OAAOzE,KAAK+J,EACd,GAEA,CAAA7D,IAAA,kBAAAzB,IAIA,WACE,OAAOzE,KAAKgK,EACd,GAEA,CAAA9D,IAAA,WAAArG,MASA,SAAU8P,GACR,IAAIC,EAAKD,EAKT,OAJIA,aAAgBtF,IAClBuF,EAAK,WAAH,OAAgBD,EAAKE,KAAI/Q,MAAT6Q,EAAI/O,UAAc,GAEtCZ,KAAK0J,GAAqBkG,EACnB5P,IACT,GAEA,CAAAkG,IAAA,OAAArG,MAsBA,WAAe,QAAAiQ,EAAAlP,UAAA2B,OAANtD,EAAI,IAAA0E,MAAAmM,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ9Q,EAAI8Q,GAAAnP,UAAAmP,GACX,OAAO/P,KAAKZ,KAAIN,MAATkB,KAAI,CAAMA,KAAKuK,QAAM9K,OAAKR,GACnC,GAEA,CAAAiH,IAAA,OAAArG,MAwBA,SAAMsF,GAAkB,IAAA6K,EAAA,KACtB,GAAIhQ,KAAK0P,WAAY,CACnB,IAAMlP,EAAMyP,EAAW,iCAAkC,aACzD,OAAO7P,QAAQE,OAAOE,EACxB,CAAC,QAAA0P,EAAAtP,UAAA2B,OAJe4N,EAAI,IAAAxM,MAAAuM,EAAA,EAAAA,EAAA,KAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,EAAA,GAAAxP,UAAAwP,GAMpB,IAAMnR,EAAOkR,EAAKxP,QAUlB,GAHAX,KAAKoC,KAAK,OAAQnD,IAGbe,KAAK8J,GAAU,CAClB,IAAM4E,EAAS1O,KAAKuK,OAAOzL,MAAMqG,EAASlG,GAC1C,MAA+B,mBAAhByP,EAAO2B,KAClB3B,EACAtO,QAAQC,QAAQqO,EACtB,CAGA,IA2DI7H,EA3DEyJ,EAAWtQ,KAAKkL,QAAQ4B,OAAS9M,KAAKkL,QAAQuB,SAAWzM,KAAKkL,QAAQgB,YAAYpN,MAAMkB,KAAMmQ,GAAQ,GAG5G,GAAInQ,KAAKkL,QAAQ4B,MAAO,CACtB,IAAMyD,EAASvQ,KAAKkL,QAAQ6B,eAAetI,IAAI6L,GAC/C,GAAIC,EAOF,OADAvQ,KAAKoC,KAAK,YACHmO,EAOTvQ,KAAKoC,KAAK,YACZ,CAIA,GAAIpC,KAAKkL,QAAQuB,SAAU,CACzB,IAAM+D,EAAaxQ,KAAKkL,QAAQgC,cAAczI,IAAI6L,GAElD,GAAIE,EAOF,OADAxQ,KAAKoC,KAAK,oBACHoO,EAOTxQ,KAAKoC,KAAK,oBACZ,CAEA,IAAKpC,KAAK+N,SAAW/N,KAAKyQ,aAAc,CAMtC,IAAMzL,EAAQiL,EAAW,kBAAmB,gBAI5C,OAFAjQ,KAAKoC,KAAK,SAAU4C,GAEb0L,EAAS1Q,KAAMgF,EAAO/F,IAC3BmB,QAAQE,OAAO0E,EACnB,CACAhF,KAAKwJ,IAAiB,EAGtB,IAAImH,GAAe,EAEbvR,EAAO,IAAIgB,SAAQ,SAACC,EAASC,GACjC,IAAMsQ,EAAmB5B,KAAKC,MAC9B,GAAIe,EAAK5C,UAAUzG,OAAQ,CACrBqJ,EAAK9E,QAAQrE,UACfA,EAAUC,YACR,WACE6J,GAAe,EACf,IAAM3L,EAAQiL,EAAW,mBAADxQ,OACHuQ,EAAK9E,QAAQrE,QAAO,MAAM,aAEzCgK,EAAU7B,KAAKC,MAAQ2B,EAC7BZ,EAAK5C,UAAU1G,UAOfsJ,EAAK5N,KAAK,UAAW4C,EAAO6L,EAAS5R,GACrC6R,EAAY9L,EAAOgL,EAAMnJ,EAAS5H,EAAM4R,EAASxQ,EAASC,GAC1DyQ,EAAcf,EAAMM,EAAU,WAE1BN,EAAK9E,QAAQoC,iBACf0C,EAAK9E,QAAQoC,gBAAgBE,OAEjC,GAAGwC,EAAK9E,QAAQrE,UAGpB,IACE,IAAM6H,EAASsB,EAAKzF,OAAOzL,MAAMqG,EAASlG,GACpC+R,EAAkC,mBAAhBtC,EAAO2B,KAC3B3B,EACAtO,QAAQC,QAAQqO,GAEpBsC,EAAQX,MAAK,SAAA3B,GACNiC,IACHrB,aAAazI,GAMbmJ,EAAK5N,KAAK,UAAWsM,EAASM,KAAKC,MAAQ2B,GAC3CG,EAAcf,EAAMM,EAAU,WAC9BN,EAAK5C,UAAU1G,UACfrG,EAAQqO,GACJsB,EAAK9E,QAAQ4B,OACfkD,EAAK9E,QAAQ6B,eAAerI,IAC1B4L,EACAU,EACAhB,EAAK9E,QAAQe,SAAW,EACpB+C,KAAKC,MAAQe,EAAK9E,QAAQe,SAC1B,GAIZ,IAAE,OACO,SAAAjH,GACL,IAAK2L,EAAc,CACjBX,EAAK5C,UAAU1G,UACf,IAAMuK,EAAiBjC,KAAKC,MAAQ2B,EACpCE,EACE9L,EAAOgL,EAAMnJ,EAAS5H,EAAMgS,EAAgB5Q,EAASC,GACvDyQ,EAAcf,EAAMM,EAAU,QAChC,CACF,GACJ,CAAE,MAAOtL,GACPgL,EAAK5C,UAAU1G,UACf,IAAMmK,EAAU7B,KAAKC,MAAQ2B,EAC7BE,EAAY9L,EAAOgL,EAAMnJ,EAAS5H,EAAM4R,EAASxQ,EAASC,GAC1DyQ,EAAcf,EAAMM,EAAU,QAChC,CACF,KAAO,CACL,IAAMO,EAAU7B,KAAKC,MAAQ2B,EACvBpQ,EAAMyP,EAAW,mBAAoB,cAO3CD,EAAK5N,KAAK,kBAAmB5B,EAAKqQ,GAClCC,EAAYtQ,EAAKwP,EAAMnJ,EAAS5H,EAAM4R,EAASxQ,EAASC,GACxDyQ,EAAcf,EAAMM,EACtB,CACF,IAaA,OAVItQ,KAAKkL,QAAQuB,UACfzM,KAAKkL,QAAQgC,cAAcxI,IACzB4L,EACAlR,EACAY,KAAKkL,QAAQwB,YAAc,EACvBsC,KAAKC,MAAQjP,KAAKkL,QAAQwB,YAC1B,GAIDtN,CACT,GAEA,CAAA8G,IAAA,aAAArG,MAIA,WACMG,KAAKkL,QAAQ4B,OACf9M,KAAKkL,QAAQ6B,eAAeE,QAG1BjN,KAAKkL,QAAQgC,eACflN,KAAKkL,QAAQgC,cAAcD,OAE/B,GAEA,CAAA/G,IAAA,cAAArG,MAoBA,SAAa8P,EAAMuB,GAAU,IAAAC,EAAA,KAE3B,GADAD,EAAWA,GAAY,IACH,mBAATvB,EACT,MAAM,IAAInO,UAAU,4CAEtB,GAAI5B,MAAMsR,GACR,MAAM,IAAI1P,UAAU,0CAGtB,IAAM4P,EAAQ,SAAAxK,GACZ+I,EAAK7Q,MAAMqS,GAAK,OAAO,SAAApJ,GAOrBoJ,EAAK/O,KAAK,oBAAqB2F,GAC/BoJ,EAAKrC,MACP,GACF,EAEMV,EAAQiD,YAAYD,EAAOF,GACN,mBAAhB9C,EAAMC,OACfD,EAAMC,QAGR+C,GACF,GAEA,CAAAlL,IAAA,SAAArG,MAMA,WACEG,KAAK8J,IAAW,EAChB9J,KAAK2N,OAAO2D,gCACd,GAEA,CAAApL,IAAA,UAAArG,MAKA,WACEG,KAAK8J,IAAW,EAChB9J,KAAK2N,OAAO4D,sCACd,GAEA,CAAArL,IAAA,YAAArG,MAMA,WACE,GAAIG,KAAKkL,QAAQoC,iBAAmBtN,KAAKkL,QAAQoC,gBAAgB8B,OAC/D,OAAOpP,KAAKkL,QAAQoC,gBAAgB8B,MAIxC,GAEA,CAAAlJ,IAAA,qBAAArG,MAOA,WACE,OAAOG,KAAKkL,QAAQoC,eACtB,IAz6BW5F,EAy6BV,EAAAxB,IAAA,aAAArG,MA5wBD,SAAmBmF,GACjB,QAASA,EAAMiF,EACjB,GAEA,CAAA/D,IAAA,YAAArG,MAUA,SAAkBqL,GAChB,OAAO,IAAIjC,EAAOiC,EACpB,IA7KWjE,GAAAiB,EAAAH,EAAA5I,UAAA8H,GAAAS,GAAAQ,EAAAH,EAAAL,GAAApI,OAAAiF,eAAAwD,EAAA,aAAAK,UAAA,IAAAL,EAAA,IAAAA,EAAAd,EAAAS,CA6KV,CAvBiB,CAAS5H,GAsxB7B,SAASgR,EAAa9L,EAAOuJ,EAAS1H,EAAS5H,EAAM4R,EAASxQ,EAASC,GAAQ,IAAAkR,EAG7E,GAFAlC,aAAazI,IAET2K,EAAAjD,EAAQrD,SAAQc,YAAWlN,MAAA0S,EAAA,CAACxM,GAAKvF,OAAAuH,EAAK/H,KAExCsP,EAAQnM,KAAK,UAAW4C,EAAO6L,OAC1B,EAkCT,SAAetC,EAAS/N,EAAKvB,EAAM4R,GAOjC,GADAtC,EAAQnM,KAAK,UAAW5B,EAAKqQ,EAAS5R,IAClCsP,EAAQT,OAAZ,CAGA,IAAMF,EAAQW,EAAQX,MACjBA,EAAM6D,MAAQlD,EAAQd,kBAAqBc,EAAQP,WACtCJ,EAAM8D,SAAW9D,EAAM6D,MAAQ,IACjClD,EAAQrD,QAAQO,0BAC9B8C,EAAQP,WACRO,EAAQO,MARgB,CAU5B,CAjDI6C,CAAKpD,EAASvJ,EAAO/F,EAAM4R,GAI3B,IAAMjB,EAAKc,EAASnC,EAASvJ,EAAO/F,GACpC,GAAI2Q,EAAI,OAAOvP,EAAQuP,EACzB,CAGAtP,EAAO0E,EACT,CAEA,SAAS0L,EAAUnC,EAAS/N,EAAKvB,GAC/B,GAAIsP,EAAQ7E,GACV,IACE,IAAMgF,EACNH,EAAQ7E,GACL5K,MAAMyP,EAAQ7E,GAAoB,GAAFjK,OAAAuH,EAAM/H,GAAI,CAAEuB,KAO/C,OADA+N,EAAQnM,KAAK,WAAYsM,EAAQlO,GAC7BkO,aAAkBtO,QAAgBsO,EAC/BtO,QAAQC,QAAQqO,EACzB,CAAE,MAAO3G,GACP,OAAO3H,QAAQE,OAAOyH,EACxB,CAEJ,CAqBA,SAASgJ,EAAexC,EAAS+B,EAAUsB,GASsB,IAAAC,EAA1DD,IAASrD,EAAQrD,QAAQyB,gBAAgBE,SAAS+E,IACxB,QAA7BC,EAAAtD,EAAQrD,QAAQgC,qBAAa,IAAA2E,GAA7BA,EAAA,OAAsCvB,EAE1C,CAEA,SAASL,EAAY6B,EAAK/K,GACxB,IAAM/B,EAAQ,IAAItC,MAAMoP,GAGxB,OAFA9M,EAAM+B,KAAOA,EACb/B,EAAMiF,IAAa,EACZjF,CACT,CAGA,IAAMkJ,EAAW,WAAH,MACZ,uCAAuC6D,QAAQ,SAAS,SAAAC,GACtD,IAAM/K,EAAoB,GAAhBgL,KAAKC,SAAgB,EAE/B,OADgB,MAANF,EAAY/K,EAAS,EAAJA,EAAU,GAC5BU,SAAS,GACpB,GAAE,EAEJrJ,EAAOD,QAAoBgM,C,grBCpgC3B,IAKMlB,EAAW,WAMf,O,EALA,SAAAA,EAAagJ,I,4FAAYhH,CAAA,KAAAhC,GACvBnJ,KAAK8M,MAAQ,IAAIsF,IACjBpS,KAAKmS,WAAaA,QAAAA,EAAcF,KAAAI,IAAA,EAAK,IAAK,CAC5C,G,EAEA,EAAAnM,IAAA,MAAArG,MAKA,SAAKqG,GACH,IAAMqK,EAASvQ,KAAK8M,MAAMrI,IAAIyB,GAC9B,GAAIqK,EAAQ,CACV,GAAIA,EAAO+B,UAAYtD,KAAKC,OAA8B,IAArBsB,EAAO+B,UAC1C,OAAO/B,EAAO1Q,MAEhBG,KAAK8M,MAAK,OAAQ5G,EACpB,CAEF,GAEA,CAAAA,IAAA,MAAArG,MAOA,SAAKqG,EAAKrG,EAAO0S,GAEXvS,KAAK8M,MAAM0F,OAASxS,KAAKmS,iBAAgCjR,IAAlBlB,KAAKyE,IAAIyB,IAClDlG,KAAK8M,MAAK,OAAQ9M,KAAK8M,MAAM3G,OAAOsM,OAAO5S,OAG7CG,KAAK8M,MAAMpI,IAAIwB,EAAK,CAClBoM,UAAWC,EACX1S,MAAAA,GAEJ,GAEA,CAAAqG,IAAA,SAAArG,MAKA,SAAQqG,GACNlG,KAAK8M,MAAK,OAAQ5G,EACpB,GAEA,CAAAA,IAAA,QAAArG,MAIA,WACEG,KAAK8M,MAAM4F,OACb,M,6EAAC,CAxDc,GA2DjBpU,EAAOD,QAAoB8K,C,6BC9D3B7K,EAAOD,QAAoB2K,EAAQ,I,6BCFtB,SAAAhB,EAAAC,GAAA,OAAAD,EAAA,mBAAAX,QAAA,iBAAAA,OAAAC,SAAA,SAAAW,GAAA,cAAAA,CAAA,WAAAA,GAAA,OAAAA,GAAA,mBAAAZ,QAAAY,EAAAL,cAAAP,QAAAY,IAAAZ,OAAAlI,UAAA,gBAAA8I,CAAA,EAAAD,EAAAC,EAAA,UAAA5I,EAAA0I,EAAAd,GAAA,IAAAS,EAAApI,OAAA6G,KAAA4B,GAAA,GAAAzI,OAAAC,sBAAA,KAAA0I,EAAA3I,OAAAC,sBAAAwI,GAAAd,IAAAgB,EAAAA,EAAA2E,QAAA,SAAA3F,GAAA,OAAA3H,OAAAqT,yBAAA5K,EAAAd,GAAAzC,UAAA,KAAAkD,EAAApF,KAAAxD,MAAA4I,EAAAO,EAAA,QAAAP,CAAA,UAAAkL,EAAA7K,GAAA,QAAAd,EAAA,EAAAA,EAAArG,UAAA2B,OAAA0E,IAAA,KAAAS,EAAA,MAAA9G,UAAAqG,GAAArG,UAAAqG,GAAA,GAAAA,EAAA,EAAA5H,EAAAC,OAAAoI,IAAA,GAAAmL,SAAA,SAAA5L,GAAA6L,EAAA/K,EAAAd,EAAAS,EAAAT,GAAA,IAAA3H,OAAAyT,0BAAAzT,OAAA0T,iBAAAjL,EAAAzI,OAAAyT,0BAAArL,IAAArI,EAAAC,OAAAoI,IAAAmL,SAAA,SAAA5L,GAAA3H,OAAAiF,eAAAwD,EAAAd,EAAA3H,OAAAqT,yBAAAjL,EAAAT,GAAA,WAAAc,CAAA,UAAA+K,EAAA/K,EAAAd,EAAAS,GAAA,OAAAT,EAAAoB,EAAApB,MAAAc,EAAAzI,OAAAiF,eAAAwD,EAAAd,EAAA,CAAApH,MAAA6H,EAAAlD,YAAA,EAAA2D,cAAA,EAAAC,UAAA,IAAAL,EAAAd,GAAAS,EAAAK,CAAA,UAAAG,EAAAH,EAAAd,GAAA,QAAAS,EAAA,EAAAA,EAAAT,EAAA1E,OAAAmF,IAAA,KAAAO,EAAAhB,EAAAS,GAAAO,EAAAzD,WAAAyD,EAAAzD,aAAA,EAAAyD,EAAAE,cAAA,YAAAF,IAAAA,EAAAG,UAAA,GAAA9I,OAAAiF,eAAAwD,EAAAM,EAAAJ,EAAA/B,KAAA+B,EAAA,WAAAI,EAAAX,GAAA,IAAA9D,EAAA,SAAA8D,GAAA,aAAAM,EAAAN,KAAAA,EAAA,OAAAA,EAAA,IAAAK,EAAAL,EAAAL,OAAAiB,aAAA,YAAAP,EAAA,KAAAnE,EAAAmE,EAAA3I,KAAAsI,EAAAT,UAAA,aAAAe,EAAApE,GAAA,OAAAA,EAAA,UAAApC,UAAA,uDAAAmB,OAAA+E,EAAA,CAAAa,CAAAb,GAAA,gBAAAM,EAAApE,GAAAA,EAAAA,EAAA,YAAA4E,IAAA,QAAAd,GAAAe,QAAAtJ,UAAAuJ,QAAAtJ,KAAAR,QAAA+J,UAAAF,QAAA,0BAAAf,GAAA,QAAAc,EAAA,mBAAAd,CAAA,cAAAkB,EAAAlB,GAAA,OAAAkB,EAAAtJ,OAAAuJ,eAAAvJ,OAAAsF,eAAAvB,OAAA,SAAAqE,GAAA,OAAAA,EAAAoB,WAAAxJ,OAAAsF,eAAA8C,EAAA,EAAAkB,EAAAlB,EAAA,UAAAqB,EAAArB,EAAAK,GAAA,OAAAgB,EAAAzJ,OAAAuJ,eAAAvJ,OAAAuJ,eAAAxF,OAAA,SAAAqE,EAAAK,GAAA,OAAAL,EAAAoB,UAAAf,EAAAL,CAAA,EAAAqB,EAAArB,EAAAK,EAAA,CAEb,IAAMkL,EAAS5L,OAAO,UAChB6L,EAAU7L,OAAO,WACjB8L,EAAU9L,OAAO,WACjB+L,EAAc/L,OAAO,eACrBgM,EAAkBhM,OAAO,mBACzBiM,EAAoBjM,OAAO,qBAC3BkM,EAAoBlM,OAAO,qBA0C3B4B,EAAM,SAAAqB,GACV,SAAArB,EAAaiC,GAAS,IAAAD,GAnDX,SAAAxD,EAAAzD,GAAA,KAAAyD,aAAAzD,GAAA,UAAAxC,UAAA,qCAmDW2J,CAAA,KAAAlC,IACpBgC,EApDS,SAAAvD,EAAAO,EAAAF,GAAA,OAAAE,EAAAW,EAAAX,GAAA,SAAAP,EAAAK,GAAA,GAAAA,IAAA,UAAAC,EAAAD,IAAA,mBAAAA,GAAA,OAAAA,EAAA,YAAAA,EAAA,UAAAvG,UAAA,4EAAAuG,GAAA,YAAAA,EAAA,UAAAqD,eAAA,oEAAArD,CAAA,CAAAsD,CAAA3D,EAAA,CAAA4D,CAAA5D,EAAAc,IAAA5J,QAAA+J,UAAAV,EAAAF,GAAA,GAAAa,EAAAlB,GAAAE,aAAAK,EAAAnJ,MAAA4I,EAAAK,GAAA,CAoDTwD,CAAA,KAAAtC,IAGKiK,GAAWhI,EAAQS,qBAAuB,GAC/CV,EAAKkI,GAAWjI,EAAQQ,qBAAuB,IAC/CT,EAAKgI,GAAU,IAAItP,MAAMsH,EAAKiI,IAC9BjI,EAAKmI,GAAe,CAAC,EAAK,IAAM,GAAK,IAAM,GAAK,IAAM,IAAM,KAAO,GACnEnI,EAAKsI,GAAqB,SAG1BtI,EAAKW,2BACiC,IAAtCV,EAAQU,0BAGRX,EAAKsB,iBAA8C,IAA5BrB,EAAQqB,gBAG/BtB,EAAKuB,uBAAyBtB,EAAQsB,uBACtCvB,EAAKuI,aAAeC,EAAWxI,EAAKgI,IAGpC,IAAK,IAAIrP,EAAI,EAAGA,EAAIqH,EAAKiI,GAAUtP,IAAKqH,EAAKgI,GAAQrP,GAAK8P,IAE1D,IAAMC,EAAiB1B,KAAK2B,MAAM3I,EAAKkI,GAAWlI,EAAKiI,IA+BtD,OA7BGjI,EAAKuB,uBAEPvB,EAAKqG,kCAGLrG,EAAKoI,GAAmBhC,YAAYpG,EAAKuI,aAAcG,GAEZ,mBAAhC1I,EAAKoI,GAAiBhF,OAC/BpD,EAAKoI,GAAiBhF,SAUtBpD,EAAKsB,kBACPtB,EAAKqI,GAAqBjC,aACxB,SAAAzK,GAAC,OAAIqE,EAAK7I,KAAK,WAAY6I,EAAK2C,MAAM,GACtC+F,GAC2C,mBAAlC1I,EAAKqI,GAAmBjF,OACjCpD,EAAKqI,GAAmBjF,SAIxBnD,EAAQ0C,QACV3C,EAAKgI,GAAQ,GAAEL,EAAAA,EAAA,GAAQc,KAAaxI,EAAQ0C,QAC7C3C,CACH,CAEA,OA7GW,SAAAvD,EAAAK,GAAA,sBAAAA,GAAA,OAAAA,EAAA,UAAAvG,UAAA,sDAAAkG,EAAAvI,UAAAG,OAAA4C,OAAA6F,GAAAA,EAAA5I,UAAA,CAAAyI,YAAA,CAAA/H,MAAA6H,EAAAU,UAAA,EAAAD,cAAA,KAAA7I,OAAAiF,eAAAmD,EAAA,aAAAU,UAAA,IAAAL,GAAAgB,EAAArB,EAAAK,EAAA,CA6GXoH,CAAAlG,EAAAqB,GA7GWvC,EA6GXkB,GA7GWhC,EA6GX,EAAAf,IAAA,QAAAzB,IAIA,WAAa,IAAAuL,EAAA,KACL6D,EAAS7T,KAAKiT,GAAQa,QAAO,SAACC,EAAKC,GACvC,OAAKA,GACL1U,OAAO6G,KAAK4N,GAAKlB,SAAQ,SAAA3M,GACX,iBAARA,GAAkC,gBAARA,IAC3B6N,EAAI7N,IAAQ8N,EAAI9N,IAAQ,EAE7B,IAEI8J,EAAKpE,2BACHoI,EAAIC,eACNF,EAAIE,aAAeF,EAAIE,aAAaxU,OAAOuU,EAAIC,eAG5CF,GAZYA,CAarB,GAAGL,KA6BH,OA3BI1T,KAAK4L,2BAEPiI,EAAOI,aAAaC,MAAK,SAACzM,EAAG0M,GAAC,OAAK1M,EAAI0M,CAAC,IAIpCN,EAAOI,aAAa1R,OACtBsR,EAAOO,YACJP,EACEI,aACAH,QAAO,SAACrM,EAAG0M,GAAC,OAAK1M,EAAI0M,CAAC,GAAE,GAAMN,EAAOI,aAAa1R,OAEvDsR,EAAOO,YAAc,EAIvBpU,KAAKoT,GAAaP,SAAQ,SAAAwB,GACxBR,EAAOS,YAAYD,GA2F3B,SAA8BA,EAAY5Q,GACxC,OAAmB,IAAf4Q,EACK5Q,EAAI,IAAM,EAGZA,EADKwO,KAAKsC,KAAKF,EAAa5Q,EAAIlB,QACtB,IAAM,CACzB,CAhGUiS,CAAoBH,EAAYR,EAAOI,aAC3C,MAEAJ,EAAOO,aAAe,EACtBpU,KAAKoT,GAAaP,SAAQ,SAAAwB,GACxBR,EAAOS,YAAYD,IAAe,CACpC,KAGKR,CACT,GAEA,CAAA3N,IAAA,SAAAzB,IAIA,WACE,OAAOzE,KAAKiT,GAAQtS,OACtB,GAAC,CAAAuF,IAAA,YAAArG,MAED,SAAW4O,EAAUgG,GACnBzU,KAAKiT,GAAQ,GAAGxE,KACC,cAAbA,GACa,aAAbA,GACa,aAAbA,GACFzO,KAAKiT,GAAQ,GAAGgB,aAAa3R,KAAKmS,GAAkB,EAExD,GAAC,CAAAvO,IAAA,OAAArG,MAED,WACEG,KAAKiT,GAAQ,GAAGyB,sBAAuB,CACzC,GAAC,CAAAxO,IAAA,QAAArG,MAED,WACEG,KAAKiT,GAAQ,GAAGyB,sBAAuB,CACzC,GAAC,CAAAxO,IAAA,WAAArG,MAED,WACEG,KAAKiG,0BAE+B/E,IAAhClB,KAAKwM,uBACPmI,cAAc3U,KAAKqT,IAEnBrT,KAAKuR,uCAEHvR,KAAKuM,iBACPoI,cAAc3U,KAAKsT,GAEvB,GAAC,CAAApN,IAAA,uCAAArG,MAED,WACMG,KAAKwM,wBACPxM,KAAKwM,uBAAuB/L,eAAeT,KAAKuT,GAC9CvT,KAAKwT,aAEX,GAAC,CAAAtN,IAAA,iCAAArG,MAED,WAEIG,KAAKwM,wBAEoB,IADzBxM,KAAKwM,uBAAuBzI,cAAc/D,KAAKuT,GAC7CvT,KAAKwT,eAEPxT,KAAKwM,uBAAuBzL,GAAGf,KAAKuT,GAClCvT,KAAKwT,aAEX,MAtNWtL,EAAAH,EAAA5I,UAAA8H,GAAA3H,OAAAiF,eAAAwD,EAAA,aAAAK,UAAA,IAAAL,EAAA,IAAAA,EAAAd,CAsNV,CApKS,CAxCS+B,EAAAA,GAAAA,cA+MfyK,EAAa,SAAAmB,GAAM,OAAI,SAAAhO,GAC3BgO,EAAO9O,MACP8O,EAAOvS,QAAQqR,IACjB,CAAC,EAEKA,EAAS,SAAA9M,GAAC,MAAK,CACnB8K,SAAU,EACVmD,UAAW,EACXC,UAAW,EACXC,QAAS,EACTtD,MAAO,EACPuD,SAAU,EACVC,UAAW,EACXC,YAAa,EACbC,kBAAmB,EACnBC,oBAAqB,EACrBC,oBAAqB,EACrBf,YAAa,CAAC,EACdL,aAAc,GACf,EAUD3V,EAAOD,QAAoB4K,C,GCrPvBqM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBtU,IAAjBuU,EACH,OAAOA,EAAapX,QAGrB,IAAIC,EAASgX,EAAyBE,GAAY,CAGjDnX,QAAS,CAAC,GAOX,OAHAqX,EAAoBF,GAAUlX,EAAQA,EAAOD,QAASkX,GAG/CjX,EAAOD,OACf,CCnB0BkX,CAAoB,K,MDF1CD", "sources": ["webpack://circuitBreaker/webpack/universalModuleDefinition", "webpack://circuitBreaker/./node_modules/events/events.js", "webpack://circuitBreaker/./lib/semaphore.js", "webpack://circuitBreaker/./lib/circuit.js", "webpack://circuitBreaker/./lib/cache.js", "webpack://circuitBreaker/./index.js", "webpack://circuitBreaker/./lib/status.js", "webpack://circuitBreaker/webpack/bootstrap", "webpack://circuitBreaker/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"circuitBreaker\"] = factory();\n\telse\n\t\troot[\"circuitBreaker\"] = factory();\n})(self, () => {\nreturn ", "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\nvar R = typeof Reflect === 'object' ? Reflect : null\nvar ReflectApply = R && typeof R.apply === 'function'\n  ? R.apply\n  : function ReflectApply(target, receiver, args) {\n    return Function.prototype.apply.call(target, receiver, args);\n  }\n\nvar ReflectOwnKeys\nif (R && typeof R.ownKeys === 'function') {\n  ReflectOwnKeys = R.ownKeys\n} else if (Object.getOwnPropertySymbols) {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target)\n      .concat(Object.getOwnPropertySymbols(target));\n  };\n} else {\n  ReflectOwnKeys = function ReflectOwnKeys(target) {\n    return Object.getOwnPropertyNames(target);\n  };\n}\n\nfunction ProcessEmitWarning(warning) {\n  if (console && console.warn) console.warn(warning);\n}\n\nvar NumberIsNaN = Number.isNaN || function NumberIsNaN(value) {\n  return value !== value;\n}\n\nfunction EventEmitter() {\n  EventEmitter.init.call(this);\n}\nmodule.exports = EventEmitter;\nmodule.exports.once = once;\n\n// Backwards-compat with node 0.10.x\nEventEmitter.EventEmitter = EventEmitter;\n\nEventEmitter.prototype._events = undefined;\nEventEmitter.prototype._eventsCount = 0;\nEventEmitter.prototype._maxListeners = undefined;\n\n// By default EventEmitters will print a warning if more than 10 listeners are\n// added to it. This is a useful default which helps finding memory leaks.\nvar defaultMaxListeners = 10;\n\nfunction checkListener(listener) {\n  if (typeof listener !== 'function') {\n    throw new TypeError('The \"listener\" argument must be of type Function. Received type ' + typeof listener);\n  }\n}\n\nObject.defineProperty(EventEmitter, 'defaultMaxListeners', {\n  enumerable: true,\n  get: function() {\n    return defaultMaxListeners;\n  },\n  set: function(arg) {\n    if (typeof arg !== 'number' || arg < 0 || NumberIsNaN(arg)) {\n      throw new RangeError('The value of \"defaultMaxListeners\" is out of range. It must be a non-negative number. Received ' + arg + '.');\n    }\n    defaultMaxListeners = arg;\n  }\n});\n\nEventEmitter.init = function() {\n\n  if (this._events === undefined ||\n      this._events === Object.getPrototypeOf(this)._events) {\n    this._events = Object.create(null);\n    this._eventsCount = 0;\n  }\n\n  this._maxListeners = this._maxListeners || undefined;\n};\n\n// Obviously not all Emitters should be limited to 10. This function allows\n// that to be increased. Set to zero for unlimited.\nEventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {\n  if (typeof n !== 'number' || n < 0 || NumberIsNaN(n)) {\n    throw new RangeError('The value of \"n\" is out of range. It must be a non-negative number. Received ' + n + '.');\n  }\n  this._maxListeners = n;\n  return this;\n};\n\nfunction _getMaxListeners(that) {\n  if (that._maxListeners === undefined)\n    return EventEmitter.defaultMaxListeners;\n  return that._maxListeners;\n}\n\nEventEmitter.prototype.getMaxListeners = function getMaxListeners() {\n  return _getMaxListeners(this);\n};\n\nEventEmitter.prototype.emit = function emit(type) {\n  var args = [];\n  for (var i = 1; i < arguments.length; i++) args.push(arguments[i]);\n  var doError = (type === 'error');\n\n  var events = this._events;\n  if (events !== undefined)\n    doError = (doError && events.error === undefined);\n  else if (!doError)\n    return false;\n\n  // If there is no 'error' event listener then throw.\n  if (doError) {\n    var er;\n    if (args.length > 0)\n      er = args[0];\n    if (er instanceof Error) {\n      // Note: The comments on the `throw` lines are intentional, they show\n      // up in Node's output if this results in an unhandled exception.\n      throw er; // Unhandled 'error' event\n    }\n    // At least give some kind of context to the user\n    var err = new Error('Unhandled error.' + (er ? ' (' + er.message + ')' : ''));\n    err.context = er;\n    throw err; // Unhandled 'error' event\n  }\n\n  var handler = events[type];\n\n  if (handler === undefined)\n    return false;\n\n  if (typeof handler === 'function') {\n    ReflectApply(handler, this, args);\n  } else {\n    var len = handler.length;\n    var listeners = arrayClone(handler, len);\n    for (var i = 0; i < len; ++i)\n      ReflectApply(listeners[i], this, args);\n  }\n\n  return true;\n};\n\nfunction _addListener(target, type, listener, prepend) {\n  var m;\n  var events;\n  var existing;\n\n  checkListener(listener);\n\n  events = target._events;\n  if (events === undefined) {\n    events = target._events = Object.create(null);\n    target._eventsCount = 0;\n  } else {\n    // To avoid recursion in the case that type === \"newListener\"! Before\n    // adding it to the listeners, first emit \"newListener\".\n    if (events.newListener !== undefined) {\n      target.emit('newListener', type,\n                  listener.listener ? listener.listener : listener);\n\n      // Re-assign `events` because a newListener handler could have caused the\n      // this._events to be assigned to a new object\n      events = target._events;\n    }\n    existing = events[type];\n  }\n\n  if (existing === undefined) {\n    // Optimize the case of one listener. Don't need the extra array object.\n    existing = events[type] = listener;\n    ++target._eventsCount;\n  } else {\n    if (typeof existing === 'function') {\n      // Adding the second element, need to change to array.\n      existing = events[type] =\n        prepend ? [listener, existing] : [existing, listener];\n      // If we've already got an array, just append.\n    } else if (prepend) {\n      existing.unshift(listener);\n    } else {\n      existing.push(listener);\n    }\n\n    // Check for listener leak\n    m = _getMaxListeners(target);\n    if (m > 0 && existing.length > m && !existing.warned) {\n      existing.warned = true;\n      // No error code for this since it is a Warning\n      // eslint-disable-next-line no-restricted-syntax\n      var w = new Error('Possible EventEmitter memory leak detected. ' +\n                          existing.length + ' ' + String(type) + ' listeners ' +\n                          'added. Use emitter.setMaxListeners() to ' +\n                          'increase limit');\n      w.name = 'MaxListenersExceededWarning';\n      w.emitter = target;\n      w.type = type;\n      w.count = existing.length;\n      ProcessEmitWarning(w);\n    }\n  }\n\n  return target;\n}\n\nEventEmitter.prototype.addListener = function addListener(type, listener) {\n  return _addListener(this, type, listener, false);\n};\n\nEventEmitter.prototype.on = EventEmitter.prototype.addListener;\n\nEventEmitter.prototype.prependListener =\n    function prependListener(type, listener) {\n      return _addListener(this, type, listener, true);\n    };\n\nfunction onceWrapper() {\n  if (!this.fired) {\n    this.target.removeListener(this.type, this.wrapFn);\n    this.fired = true;\n    if (arguments.length === 0)\n      return this.listener.call(this.target);\n    return this.listener.apply(this.target, arguments);\n  }\n}\n\nfunction _onceWrap(target, type, listener) {\n  var state = { fired: false, wrapFn: undefined, target: target, type: type, listener: listener };\n  var wrapped = onceWrapper.bind(state);\n  wrapped.listener = listener;\n  state.wrapFn = wrapped;\n  return wrapped;\n}\n\nEventEmitter.prototype.once = function once(type, listener) {\n  checkListener(listener);\n  this.on(type, _onceWrap(this, type, listener));\n  return this;\n};\n\nEventEmitter.prototype.prependOnceListener =\n    function prependOnceListener(type, listener) {\n      checkListener(listener);\n      this.prependListener(type, _onceWrap(this, type, listener));\n      return this;\n    };\n\n// Emits a 'removeListener' event if and only if the listener was removed.\nEventEmitter.prototype.removeListener =\n    function removeListener(type, listener) {\n      var list, events, position, i, originalListener;\n\n      checkListener(listener);\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      list = events[type];\n      if (list === undefined)\n        return this;\n\n      if (list === listener || list.listener === listener) {\n        if (--this._eventsCount === 0)\n          this._events = Object.create(null);\n        else {\n          delete events[type];\n          if (events.removeListener)\n            this.emit('removeListener', type, list.listener || listener);\n        }\n      } else if (typeof list !== 'function') {\n        position = -1;\n\n        for (i = list.length - 1; i >= 0; i--) {\n          if (list[i] === listener || list[i].listener === listener) {\n            originalListener = list[i].listener;\n            position = i;\n            break;\n          }\n        }\n\n        if (position < 0)\n          return this;\n\n        if (position === 0)\n          list.shift();\n        else {\n          spliceOne(list, position);\n        }\n\n        if (list.length === 1)\n          events[type] = list[0];\n\n        if (events.removeListener !== undefined)\n          this.emit('removeListener', type, originalListener || listener);\n      }\n\n      return this;\n    };\n\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\n\nEventEmitter.prototype.removeAllListeners =\n    function removeAllListeners(type) {\n      var listeners, events, i;\n\n      events = this._events;\n      if (events === undefined)\n        return this;\n\n      // not listening for removeListener, no need to emit\n      if (events.removeListener === undefined) {\n        if (arguments.length === 0) {\n          this._events = Object.create(null);\n          this._eventsCount = 0;\n        } else if (events[type] !== undefined) {\n          if (--this._eventsCount === 0)\n            this._events = Object.create(null);\n          else\n            delete events[type];\n        }\n        return this;\n      }\n\n      // emit removeListener for all listeners on all events\n      if (arguments.length === 0) {\n        var keys = Object.keys(events);\n        var key;\n        for (i = 0; i < keys.length; ++i) {\n          key = keys[i];\n          if (key === 'removeListener') continue;\n          this.removeAllListeners(key);\n        }\n        this.removeAllListeners('removeListener');\n        this._events = Object.create(null);\n        this._eventsCount = 0;\n        return this;\n      }\n\n      listeners = events[type];\n\n      if (typeof listeners === 'function') {\n        this.removeListener(type, listeners);\n      } else if (listeners !== undefined) {\n        // LIFO order\n        for (i = listeners.length - 1; i >= 0; i--) {\n          this.removeListener(type, listeners[i]);\n        }\n      }\n\n      return this;\n    };\n\nfunction _listeners(target, type, unwrap) {\n  var events = target._events;\n\n  if (events === undefined)\n    return [];\n\n  var evlistener = events[type];\n  if (evlistener === undefined)\n    return [];\n\n  if (typeof evlistener === 'function')\n    return unwrap ? [evlistener.listener || evlistener] : [evlistener];\n\n  return unwrap ?\n    unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);\n}\n\nEventEmitter.prototype.listeners = function listeners(type) {\n  return _listeners(this, type, true);\n};\n\nEventEmitter.prototype.rawListeners = function rawListeners(type) {\n  return _listeners(this, type, false);\n};\n\nEventEmitter.listenerCount = function(emitter, type) {\n  if (typeof emitter.listenerCount === 'function') {\n    return emitter.listenerCount(type);\n  } else {\n    return listenerCount.call(emitter, type);\n  }\n};\n\nEventEmitter.prototype.listenerCount = listenerCount;\nfunction listenerCount(type) {\n  var events = this._events;\n\n  if (events !== undefined) {\n    var evlistener = events[type];\n\n    if (typeof evlistener === 'function') {\n      return 1;\n    } else if (evlistener !== undefined) {\n      return evlistener.length;\n    }\n  }\n\n  return 0;\n}\n\nEventEmitter.prototype.eventNames = function eventNames() {\n  return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];\n};\n\nfunction arrayClone(arr, n) {\n  var copy = new Array(n);\n  for (var i = 0; i < n; ++i)\n    copy[i] = arr[i];\n  return copy;\n}\n\nfunction spliceOne(list, index) {\n  for (; index + 1 < list.length; index++)\n    list[index] = list[index + 1];\n  list.pop();\n}\n\nfunction unwrapListeners(arr) {\n  var ret = new Array(arr.length);\n  for (var i = 0; i < ret.length; ++i) {\n    ret[i] = arr[i].listener || arr[i];\n  }\n  return ret;\n}\n\nfunction once(emitter, name) {\n  return new Promise(function (resolve, reject) {\n    function errorListener(err) {\n      emitter.removeListener(name, resolver);\n      reject(err);\n    }\n\n    function resolver() {\n      if (typeof emitter.removeListener === 'function') {\n        emitter.removeListener('error', errorListener);\n      }\n      resolve([].slice.call(arguments));\n    };\n\n    eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });\n    if (name !== 'error') {\n      addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });\n    }\n  });\n}\n\nfunction addErrorHandlerIfEventEmitter(emitter, handler, flags) {\n  if (typeof emitter.on === 'function') {\n    eventTargetAgnosticAddListener(emitter, 'error', handler, flags);\n  }\n}\n\nfunction eventTargetAgnosticAddListener(emitter, name, listener, flags) {\n  if (typeof emitter.on === 'function') {\n    if (flags.once) {\n      emitter.once(name, listener);\n    } else {\n      emitter.on(name, listener);\n    }\n  } else if (typeof emitter.addEventListener === 'function') {\n    // EventTarget does not have `error` event semantics like Node\n    // EventEmitters, we do not listen for `error` events here.\n    emitter.addEventListener(name, function wrapListener(arg) {\n      // IE does not have builtin `{ once: true }` support so we\n      // have to do it manually.\n      if (flags.once) {\n        emitter.removeEventListener(name, wrapListener);\n      }\n      listener(arg);\n    });\n  } else {\n    throw new TypeError('The \"emitter\" argument must be of type EventEmitter. Received type ' + typeof emitter);\n  }\n}\n", "'use strict';\n\nmodule.exports = exports = semaphore;\n\nfunction semaphore (count) {\n  const resolvers = [];\n  let counter = count;\n\n  const sem = {\n    take,\n    release,\n    test\n  };\n\n  Object.defineProperty(sem, 'count', {\n    get: _ => counter,\n    enumerable: true\n  });\n\n  return sem;\n\n  function take (timeout) {\n    if (counter > 0) {\n      --counter;\n      return Promise.resolve(release);\n    }\n    return new Promise((resolve, reject) => {\n      resolvers.push(_ => {\n        --counter;\n        resolve(release);\n      });\n      if (timeout) {\n        setTimeout(_ => {\n          resolvers.shift();\n          const err = new Error(`Timed out after ${timeout}ms`);\n          err.code = 'ETIMEDOUT';\n          reject(err);\n        }, timeout);\n      }\n    });\n  }\n\n  function release () {\n    counter++;\n    if (resolvers.length > 0) {\n      resolvers.shift()();\n    }\n  }\n\n  function test () {\n    if (counter < 1) return false;\n    return take() && true;\n  }\n}\n", "'use strict';\n\nconst EventEmitter = require('events');\nconst Status = require('./status');\nconst Semaphore = require('./semaphore');\nconst MemoryCache = require('./cache');\n\nconst STATE = Symbol('state');\nconst OPEN = Symbol('open');\nconst CLOSED = Symbol('closed');\nconst HALF_OPEN = Symbol('half-open');\nconst PENDING_CLOSE = Symbol('pending-close');\nconst SHUTDOWN = Symbol('shutdown');\nconst FALLBACK_FUNCTION = Symbol('fallback');\nconst STATUS = Symbol('status');\nconst NAME = Symbol('name');\nconst GROUP = Symbol('group');\nconst ENABLED = Symbol('Enabled');\nconst WARMING_UP = Symbol('warming-up');\nconst VOLUME_THRESHOLD = Symbol('volume-threshold');\nconst OUR_ERROR = Symbol('our-error');\nconst RESET_TIMEOUT = Symbol('reset-timeout');\nconst WARMUP_TIMEOUT = Symbol('warmup-timeout');\nconst LAST_TIMER_AT = Symbol('last-timer-at');\nconst deprecation = `options.maxFailures is deprecated. \\\nPlease use options.errorThresholdPercentage`;\n\n/**\n * Constructs a {@link CircuitBreaker}.\n *\n * @class CircuitBreaker\n * @extends EventEmitter\n * @param {Function} action The action to fire for this {@link CircuitBreaker}\n * @param {Object} options Options for the {@link CircuitBreaker}\n * @param {Status} options.status A {@link Status} object that might\n *   have pre-prime stats\n * @param {Number} options.timeout The time in milliseconds that action should\n * be allowed to execute before timing out. Timeout can be disabled by setting\n * this to `false`. Default 10000 (10 seconds)\n * @param {Number} options.maxFailures (Deprecated) The number of times the\n * circuit can fail before opening. Default 10.\n * @param {Number} options.resetTimeout The time in milliseconds to wait before\n * setting the breaker to `halfOpen` state, and trying the action again.\n * Default: 30000 (30 seconds)\n * @param {Number} options.rollingCountTimeout Sets the duration of the\n * statistical rolling window, in milliseconds. This is how long Opossum keeps\n * metrics for the circuit breaker to use and for publishing. Default: 10000\n * @param {Number} options.rollingCountBuckets Sets the number of buckets the\n * rolling statistical window is divided into. So, if\n * options.rollingCountTimeout is 10000, and options.rollingCountBuckets is 10,\n * then the statistical window will be 1000/1 second snapshots in the\n * statistical window. Default: 10\n * @param {String} options.name the circuit name to use when reporting stats.\n * Default: the name of the function this circuit controls.\n * @param {boolean} options.rollingPercentilesEnabled This property indicates\n * whether execution latencies should be tracked and calculated as percentiles.\n * If they are disabled, all summary statistics (mean, percentiles) are\n * returned as -1. Default: true\n * @param {Number} options.capacity the number of concurrent requests allowed.\n * If the number currently executing function calls is equal to\n * options.capacity, further calls to `fire()` are rejected until at least one\n * of the current requests completes. Default: `Number.MAX_SAFE_INTEGER`.\n * @param {Number} options.errorThresholdPercentage the error percentage at\n * which to open the circuit and start short-circuiting requests to fallback.\n * Default: 50\n * @param {boolean} options.enabled whether this circuit is enabled upon\n * construction. Default: true\n * @param {boolean} options.allowWarmUp determines whether to allow failures\n * without opening the circuit during a brief warmup period (this is the\n * `rollingCountTimeout` property). Default: false\n * This can help in situations where no matter what your\n * `errorThresholdPercentage` is, if the first execution times out or fails,\n * the circuit immediately opens.\n * @param {Number} options.volumeThreshold the minimum number of requests within\n * the rolling statistical window that must exist before the circuit breaker\n * can open. This is similar to `options.allowWarmUp` in that no matter how many\n * failures there are, if the number of requests within the statistical window\n * does not exceed this threshold, the circuit will remain closed. Default: 0\n * @param {Function} options.errorFilter an optional function that will be\n * called when the circuit's function fails (returns a rejected Promise). If\n * this function returns truthy, the circuit's failPure statistics will not be\n * incremented. This is useful, for example, when you don't want HTTP 404 to\n * trip the circuit, but still want to handle it as a failure case.\n * @param {boolean} options.cache whether the return value of the first\n * successful execution of the circuit's function will be cached. Once a value\n * has been cached that value will be returned for every subsequent execution:\n * the cache can be cleared using `clearCache`. (The metrics `cacheHit` and\n * `cacheMiss` reflect cache activity.) Default: false\n * @param {Number} options.cacheTTL the time to live for the cache\n * in milliseconds. Set 0 for infinity cache. Default: 0 (no TTL)\n * @param {Number} options.cacheSize the max amount of entries in the internal\n * cache. Only used when cacheTransport is not defined.\n * Default: max size of JS map (2^24).\n * @param {Function} options.cacheGetKey function that returns the key to use\n * when caching the result of the circuit's fire.\n * Better to use custom one, because `JSON.stringify` is not good\n * from performance perspective.\n * Default: `(...args) => JSON.stringify(args)`\n * @param {CacheTransport} options.cacheTransport custom cache transport\n * should implement `get`, `set` and `flush` methods.\n * @param {boolean} options.coalesce  If true, this provides coalescing of\n * requests to this breaker, in other words: the promise will be cached.\n * Only one action (with same cache key) is executed at a time, and the other\n * pending actions wait for the result. Performance will improve when rapidly\n * firing the circuitbreaker with the same request, especially on a slower\n * action (e.g. multiple end-users fetching same data from remote).\n * Will use internal cache only. Can be used in combination with options.cache.\n * The metrics `coalesceCacheHit` and `coalesceCacheMiss` are available.\n * Default: false\n * @param {Number} options.coalesceTTL the time to live for the coalescing\n * in milliseconds. Set 0 for infinity cache. Default: same as options.timeout\n * @param {Number} options.coalesceSize the max amount of entries in the\n * coalescing cache. Default: max size of JS map (2^24).\n * @param {string[]} options.coalesceResetOn when to reset the coalesce cache.\n * Options: `error`, `success`, `timeout`. Default: not set, reset using TTL.\n * @param {AbortController} options.abortController this allows Opossum to\n * signal upon timeout and properly abort your on going requests instead of\n * leaving it in the background\n * @param {boolean} options.enableSnapshots whether to enable the rolling\n * stats snapshots that opossum emits at the bucketInterval. Disable this\n * as an optimization if you don't listen to the 'snapshot' event to reduce\n * the number of timers opossum initiates.\n * @param {EventEmitter} options.rotateBucketController if you have multiple\n * breakers in your app, the number of timers across breakers can get costly.\n * This option allows you to provide an EventEmitter that rotates the buckets\n * so you can have one global timer in your app. Make sure that you are\n * emitting a 'rotate' event from this EventEmitter\n * @param {boolean} options.autoRenewAbortController Automatically recreates\n * the instance of AbortController whenever the circuit transitions to\n * 'halfOpen' or 'closed' state. This ensures that new requests are not\n * impacted by previous signals that were triggered when the circuit was 'open'.\n * Default: false\n *\n *\n * @fires CircuitBreaker#halfOpen\n * @fires CircuitBreaker#close\n * @fires CircuitBreaker#open\n * @fires CircuitBreaker#fire\n * @fires CircuitBreaker#cacheHit\n * @fires CircuitBreaker#cacheMiss\n * @fires CircuitBreaker#coalesceCacheHit\n * @fires CircuitBreaker#coalesceCacheMiss\n * @fires CircuitBreaker#reject\n * @fires CircuitBreaker#timeout\n * @fires CircuitBreaker#success\n * @fires CircuitBreaker#semaphoreLocked\n * @fires CircuitBreaker#healthCheckFailed\n * @fires CircuitBreaker#fallback\n * @fires CircuitBreaker#failure\n */\nclass CircuitBreaker extends EventEmitter {\n  /**\n   * Returns true if the provided error was generated here. It will be false\n   * if the error came from the action itself.\n   * @param {Error} error The Error to check.\n   * @returns {Boolean} true if the error was generated here\n   */\n  static isOurError (error) {\n    return !!error[OUR_ERROR];\n  }\n\n  /**\n  * Create a new Status object,\n  * helpful when you need to prime a breaker with stats\n  * @param {Object} options -\n  * @param {Number} options.rollingCountBuckets number of buckets in the window\n  * @param {Number} options.rollingCountTimeout the duration of the window\n  * @param {Boolean} options.rollingPercentilesEnabled whether to calculate\n  * @param {Object} options.stats user supplied stats\n  * @returns {Status} a new {@link Status} object\n  */\n  static newStatus (options) {\n    return new Status(options);\n  }\n\n  constructor (action, options = {}) {\n    super();\n    this.options = options;\n    this.options.timeout = options.timeout ?? 10000;\n    this.options.resetTimeout = options.resetTimeout ?? 30000;\n    this.options.errorThresholdPercentage =\n      options.errorThresholdPercentage ?? 50;\n    this.options.rollingCountTimeout = options.rollingCountTimeout ?? 10000;\n    this.options.rollingCountBuckets = options.rollingCountBuckets ?? 10;\n    this.options.rollingPercentilesEnabled =\n      options.rollingPercentilesEnabled !== false;\n    this.options.capacity = Number.isInteger(options.capacity)\n      ? options.capacity\n      : Number.MAX_SAFE_INTEGER;\n    this.options.errorFilter = options.errorFilter || (_ => false);\n    this.options.cacheTTL = options.cacheTTL ?? 0;\n    this.options.cacheGetKey = options.cacheGetKey ??\n      ((...args) => JSON.stringify(args));\n    this.options.enableSnapshots = options.enableSnapshots !== false;\n    this.options.rotateBucketController = options.rotateBucketController;\n    this.options.coalesce = !!options.coalesce;\n    this.options.coalesceTTL = options.coalesceTTL ?? this.options.timeout;\n    this.options.coalesceResetOn = options.coalesceResetOn?.filter(o => ['error', 'success', 'timeout'].includes(o)) || [];\n\n    // Set default cache transport if not provided\n    if (this.options.cache) {\n      if (this.options.cacheTransport === undefined) {\n        this.options.cacheTransport = new MemoryCache(options.cacheSize);\n      } else if (typeof this.options.cacheTransport !== 'object' ||\n        !this.options.cacheTransport.get ||\n        !this.options.cacheTransport.set ||\n        !this.options.cacheTransport.flush\n      ) {\n        throw new TypeError(\n          'options.cacheTransport should be an object with `get`, `set` and `flush` methods'\n        );\n      }\n    }\n\n    if (this.options.coalesce) {\n      this.options.coalesceCache = new MemoryCache(options.coalesceSize);\n    }\n\n    this.semaphore = new Semaphore(this.options.capacity);\n\n    // check if action is defined\n    if (!action) {\n      throw new TypeError(\n        'No action provided. Cannot construct a CircuitBreaker without an invocable action.'\n      );\n    }\n\n    if (options.autoRenewAbortController && !options.abortController) {\n      options.abortController = new AbortController();\n    }\n\n    if (options.abortController && typeof options.abortController.abort !== 'function') {\n      throw new TypeError(\n        'AbortController does not contain `abort()` method'\n      );\n    }\n\n    this[VOLUME_THRESHOLD] = Number.isInteger(options.volumeThreshold)\n      ? options.volumeThreshold\n      : 0;\n    this[WARMING_UP] = options.allowWarmUp === true;\n\n    // The user can pass in a Status object to initialize the Status/stats\n    if (this.options.status) {\n      // Do a check that this is a Status Object,\n      if (this.options.status instanceof Status) {\n        this[STATUS] = this.options.status;\n      } else {\n        this[STATUS] = new Status({ stats: this.options.status });\n      }\n    } else {\n      this[STATUS] = new Status(this.options);\n    }\n\n    this[STATE] = CLOSED;\n\n    if (options.state) {\n      this[ENABLED] = options.state.enabled !== false;\n      this[WARMING_UP] = options.state.warmUp || this[WARMING_UP];\n      // Closed if nothing is passed in\n      this[CLOSED] = options.state.closed !== false;\n      // These should be in sync\n      this[HALF_OPEN] = this[PENDING_CLOSE] = options.state.halfOpen || false;\n      // Open should be the opposite of closed,\n      // but also the opposite of half_open\n      this[OPEN] = !this[CLOSED] && !this[HALF_OPEN];\n      this[SHUTDOWN] = options.state.shutdown || false;\n    } else {\n      this[PENDING_CLOSE] = false;\n      this[ENABLED] = options.enabled !== false;\n    }\n\n    this[FALLBACK_FUNCTION] = null;\n    this[NAME] = options.name || action.name || nextName();\n    this[GROUP] = options.group || this[NAME];\n\n    if (this[WARMING_UP]) {\n      const timer = this[WARMUP_TIMEOUT] = setTimeout(\n        _ => (this[WARMING_UP] = false),\n        this.options.rollingCountTimeout\n      );\n      if (typeof timer.unref === 'function') {\n        timer.unref();\n      }\n    }\n\n    if (typeof action !== 'function') {\n      this.action = _ => Promise.resolve(action);\n    } else this.action = action;\n\n    if (options.maxFailures) console.error(deprecation);\n\n    const increment = property =>\n      (result, runTime) => this[STATUS].increment(property, runTime);\n\n    this.on('success', increment('successes'));\n    this.on('failure', increment('failures'));\n    this.on('fallback', increment('fallbacks'));\n    this.on('timeout', increment('timeouts'));\n    this.on('fire', increment('fires'));\n    this.on('reject', increment('rejects'));\n    this.on('cacheHit', increment('cacheHits'));\n    this.on('cacheMiss', increment('cacheMisses'));\n    this.on('coalesceCacheHit', increment('coalesceCacheHits'));\n    this.on('coalesceCacheMiss', increment('coalesceCacheMisses'));\n    this.on('open', _ => this[STATUS].open());\n    this.on('close', _ => this[STATUS].close());\n    this.on('semaphoreLocked', increment('semaphoreRejections'));\n\n    /**\n     * @param {CircuitBreaker} circuit This current circuit\n     * @returns {function(): void} A bound reset callback\n     * @private\n     */\n    function _startTimer (circuit) {\n      circuit[LAST_TIMER_AT] = Date.now();\n      return _ => {\n        const timer = circuit[RESET_TIMEOUT] = setTimeout(() => {\n          _halfOpen(circuit);\n        }, circuit.options.resetTimeout);\n        if (typeof timer.unref === 'function') {\n          timer.unref();\n        }\n      };\n    }\n\n    /**\n     * Sets the circuit breaker to half open\n     * @private\n     * @param {CircuitBreaker} circuit The current circuit breaker\n     * @returns {void}\n     */\n    function _halfOpen (circuit) {\n      circuit[STATE] = HALF_OPEN;\n      circuit[PENDING_CLOSE] = true;\n      circuit._renewAbortControllerIfNeeded();\n      /**\n       * Emitted after `options.resetTimeout` has elapsed, allowing for\n       * a single attempt to call the service again. If that attempt is\n       * successful, the circuit will be closed. Otherwise it remains open.\n       *\n       * @event CircuitBreaker#halfOpen\n       * @type {Number} how long the circuit remained open\n       */\n      circuit.emit('halfOpen', circuit.options.resetTimeout);\n    }\n\n    this.on('open', _startTimer(this));\n    this.on('success', _ => {\n      if (this.halfOpen) {\n        this.close();\n      }\n    });\n\n    // Prepopulate the State of the Breaker\n    if (this[SHUTDOWN]) {\n      this[STATE] = SHUTDOWN;\n      this.shutdown();\n    } else if (this[CLOSED]) {\n      this.close();\n    } else if (this[OPEN]) {\n      // If the state being passed in is OPEN but more time has elapsed\n      // than the resetTimeout, then we should be in halfOpen state\n      if (this.options.state.lastTimerAt !== undefined &&\n        (Date.now() - this.options.state.lastTimerAt) >\n        this.options.resetTimeout) {\n        _halfOpen(this);\n      } else {\n        this.open();\n      }\n    } else if (this[HALF_OPEN]) {\n      // Not sure if anything needs to be done here\n      this[STATE] = HALF_OPEN;\n    }\n  }\n\n  /**\n   * Renews the abort controller if needed\n   * @private\n   * @returns {void}\n   */\n  _renewAbortControllerIfNeeded () {\n    if (\n      this.options.autoRenewAbortController &&\n        this.options.abortController &&\n        this.options.abortController.signal.aborted\n    ) {\n      this.options.abortController = new AbortController();\n    }\n  }\n\n  /**\n   * Closes the breaker, allowing the action to execute again\n   * @fires CircuitBreaker#close\n   * @returns {void}\n   */\n  close () {\n    if (this[STATE] !== CLOSED) {\n      if (this[RESET_TIMEOUT]) {\n        clearTimeout(this[RESET_TIMEOUT]);\n      }\n      this[STATE] = CLOSED;\n      this[PENDING_CLOSE] = false;\n      this._renewAbortControllerIfNeeded();\n      /**\n       * Emitted when the breaker is reset allowing the action to execute again\n       * @event CircuitBreaker#close\n       */\n      this.emit('close');\n    }\n  }\n\n  /**\n   * Opens the breaker. Each time the breaker is fired while the circuit is\n   * opened, a failed Promise is returned, or if any fallback function\n   * has been provided, it is invoked.\n   *\n   * If the breaker is already open this call does nothing.\n   * @fires CircuitBreaker#open\n   * @returns {void}\n   */\n  open () {\n    if (this[STATE] !== OPEN) {\n      this[STATE] = OPEN;\n      this[PENDING_CLOSE] = false;\n      /**\n       * Emitted when the breaker opens because the action has\n       * failure percentage greater than `options.errorThresholdPercentage`.\n       * @event CircuitBreaker#open\n       */\n      this.emit('open');\n    }\n  }\n\n  /**\n   * Shuts down this circuit breaker. All subsequent calls to the\n   * circuit will fail, returning a rejected promise.\n   * @returns {void}\n   */\n  shutdown () {\n    /**\n     * Emitted when the circuit breaker has been shut down.\n     * @event CircuitBreaker#shutdown\n     */\n    this.emit('shutdown');\n\n    this.disable();\n    this.removeAllListeners();\n    if (this[RESET_TIMEOUT]) {\n      clearTimeout(this[RESET_TIMEOUT]);\n    }\n    if (this[WARMUP_TIMEOUT]) {\n      clearTimeout(this[WARMUP_TIMEOUT]);\n    }\n    this.status.shutdown();\n    this[STATE] = SHUTDOWN;\n\n    // clear cache on shutdown\n    this.clearCache();\n  }\n\n  /**\n   * Determines if the circuit has been shutdown.\n   * @type {Boolean}\n   */\n  get isShutdown () {\n    return this[STATE] === SHUTDOWN;\n  }\n\n  /**\n   * Gets the name of this circuit\n   * @type {String}\n   */\n  get name () {\n    return this[NAME];\n  }\n\n  /**\n   * Gets the name of this circuit group\n   * @type {String}\n   */\n  get group () {\n    return this[GROUP];\n  }\n\n  /**\n   * Gets whether this circuit is in the `pendingClosed` state\n   * @type {Boolean}\n   */\n  get pendingClose () {\n    return this[PENDING_CLOSE];\n  }\n\n  /**\n   * True if the circuit is currently closed. False otherwise.\n   * @type {Boolean}\n   */\n  get closed () {\n    return this[STATE] === CLOSED;\n  }\n\n  /**\n   * True if the circuit is currently opened. False otherwise.\n   * @type {Boolean}\n   */\n  get opened () {\n    return this[STATE] === OPEN;\n  }\n\n  /**\n   * True if the circuit is currently half opened. False otherwise.\n   * @type {Boolean}\n   */\n  get halfOpen () {\n    return this[STATE] === HALF_OPEN;\n  }\n\n  /**\n   * The current {@link Status} of this {@link CircuitBreaker}\n   * @type {Status}\n   */\n  get status () {\n    return this[STATUS];\n  }\n\n  /**\n   * Get the current stats for the circuit.\n   * @see Status#stats\n   * @type {Object}\n   */\n  get stats () {\n    return this[STATUS].stats;\n  }\n\n  toJSON () {\n    return {\n      state: {\n        name: this.name,\n        enabled: this.enabled,\n        closed: this.closed,\n        open: this.opened,\n        halfOpen: this.halfOpen,\n        warmUp: this.warmUp,\n        shutdown: this.isShutdown,\n        lastTimerAt: this[LAST_TIMER_AT]\n      },\n      status: this.status.stats\n    };\n  }\n\n  /**\n   * Gets whether the circuit is enabled or not\n   * @type {Boolean}\n   */\n  get enabled () {\n    return this[ENABLED];\n  }\n\n  /**\n   * Gets whether the circuit is currently in warm up phase\n   * @type {Boolean}\n   */\n  get warmUp () {\n    return this[WARMING_UP];\n  }\n\n  /**\n   * Gets the volume threshold for this circuit\n   * @type {Boolean}\n   */\n  get volumeThreshold () {\n    return this[VOLUME_THRESHOLD];\n  }\n\n  /**\n   * Provide a fallback function for this {@link CircuitBreaker}. This\n   * function will be executed when the circuit is `fire`d and fails.\n   * It will always be preceded by a `failure` event, and `breaker.fire` returns\n   * a rejected Promise.\n   * @param {Function | CircuitBreaker} func the fallback function to execute\n   * when the breaker has opened or when a timeout or error occurs.\n   * @return {CircuitBreaker} this\n   */\n  fallback (func) {\n    let fb = func;\n    if (func instanceof CircuitBreaker) {\n      fb = (...args) => func.fire(...args);\n    }\n    this[FALLBACK_FUNCTION] = fb;\n    return this;\n  }\n\n  /**\n   * Execute the action for this circuit. If the action fails or times out, the\n   * returned promise will be rejected. If the action succeeds, the promise will\n   * resolve with the resolved value from action. If a fallback function was\n   * provided, it will be invoked in the event of any failure or timeout.\n   *\n   * Any parameters passed to this function will be proxied to the circuit\n   * function.\n   *\n   * @return {Promise<any>} promise resolves with the circuit function's return\n   * value on success or is rejected on failure of the action. Use isOurError()\n   * to determine if a rejection was a result of the circuit breaker or the\n   * action.\n   *\n   * @fires CircuitBreaker#failure\n   * @fires CircuitBreaker#fallback\n   * @fires CircuitBreaker#fire\n   * @fires CircuitBreaker#reject\n   * @fires CircuitBreaker#success\n   * @fires CircuitBreaker#timeout\n   * @fires CircuitBreaker#semaphoreLocked\n   */\n  fire (...args) {\n    return this.call(this.action, ...args);\n  }\n\n  /**\n   * Execute the action for this circuit using `context` as `this`.\n   * If the action fails or times out, the\n   * returned promise will be rejected. If the action succeeds, the promise will\n   * resolve with the resolved value from action. If a fallback function was\n   * provided, it will be invoked in the event of any failure or timeout.\n   *\n   * Any parameters in addition to `context will be passed to the\n   * circuit function.\n   *\n   * @param {any} context the `this` context used for function execution\n   * @param {any} rest the arguments passed to the action\n   *\n   * @return {Promise<any>} promise resolves with the circuit function's return\n   * value on success or is rejected on failure of the action.\n   *\n   * @fires CircuitBreaker#failure\n   * @fires CircuitBreaker#fallback\n   * @fires CircuitBreaker#fire\n   * @fires CircuitBreaker#reject\n   * @fires CircuitBreaker#success\n   * @fires CircuitBreaker#timeout\n   * @fires CircuitBreaker#semaphoreLocked\n   */\n  call (context, ...rest) {\n    if (this.isShutdown) {\n      const err = buildError('The circuit has been shutdown.', 'ESHUTDOWN');\n      return Promise.reject(err);\n    }\n\n    const args = rest.slice();\n\n    /**\n     * Emitted when the circuit breaker action is executed\n     * @event CircuitBreaker#fire\n     * @type {any} the arguments passed to the fired function\n     */\n    this.emit('fire', args);\n\n    // Protection, caches and coalesce disabled.\n    if (!this[ENABLED]) {\n      const result = this.action.apply(context, args);\n      return (typeof result.then === 'function')\n        ? result\n        : Promise.resolve(result);\n    }\n\n    // Generate cachekey only when cache and/or coalesce is enabled.\n    const cacheKey = this.options.cache || this.options.coalesce ? this.options.cacheGetKey.apply(this, rest) : '';\n\n    // If cache is enabled, check if we have a cached value\n    if (this.options.cache) {\n      const cached = this.options.cacheTransport.get(cacheKey);\n      if (cached) {\n        /**\n         * Emitted when the circuit breaker is using the cache\n         * and finds a value.\n         * @event CircuitBreaker#cacheHit\n         */\n        this.emit('cacheHit');\n        return cached;\n      }\n      /**\n       * Emitted when the circuit breaker does not find a value in\n       * the cache, but the cache option is enabled.\n       * @event CircuitBreaker#cacheMiss\n       */\n      this.emit('cacheMiss');\n    }\n\n    /* When coalesce is enabled, check coalesce cache and return\n     promise, if any. */\n    if (this.options.coalesce) {\n      const cachedCall = this.options.coalesceCache.get(cacheKey);\n\n      if (cachedCall) {\n        /**\n         * Emitted when the circuit breaker is using coalesce cache\n         * and finds a cached promise.\n         * @event CircuitBreaker#coalesceCacheHit\n         */\n        this.emit('coalesceCacheHit');\n        return cachedCall;\n      }\n      /**\n       * Emitted when the circuit breaker does not find a value in\n       * coalesce cache, but the coalesce option is enabled.\n       * @event CircuitBreaker#coalesceCacheMiss\n       */\n      this.emit('coalesceCacheMiss');\n    }\n\n    if (!this.closed && !this.pendingClose) {\n      /**\n       * Emitted when the circuit breaker is open and failing fast\n       * @event CircuitBreaker#reject\n       * @type {Error}\n       */\n      const error = buildError('Breaker is open', 'EOPENBREAKER');\n\n      this.emit('reject', error);\n\n      return fallback(this, error, args) ||\n        Promise.reject(error);\n    }\n    this[PENDING_CLOSE] = false;\n\n    let timeout;\n    let timeoutError = false;\n\n    const call = new Promise((resolve, reject) => {\n      const latencyStartTime = Date.now();\n      if (this.semaphore.test()) {\n        if (this.options.timeout) {\n          timeout = setTimeout(\n            () => {\n              timeoutError = true;\n              const error = buildError(\n                `Timed out after ${this.options.timeout}ms`, 'ETIMEDOUT'\n              );\n              const latency = Date.now() - latencyStartTime;\n              this.semaphore.release();\n              /**\n               * Emitted when the circuit breaker action takes longer than\n               * `options.timeout`\n               * @event CircuitBreaker#timeout\n               * @type {Error}\n               */\n              this.emit('timeout', error, latency, args);\n              handleError(error, this, timeout, args, latency, resolve, reject);\n              resetCoalesce(this, cacheKey, 'timeout');\n\n              if (this.options.abortController) {\n                this.options.abortController.abort();\n              }\n            }, this.options.timeout);\n        }\n\n        try {\n          const result = this.action.apply(context, args);\n          const promise = (typeof result.then === 'function')\n            ? result\n            : Promise.resolve(result);\n\n          promise.then(result => {\n            if (!timeoutError) {\n              clearTimeout(timeout);\n              /**\n               * Emitted when the circuit breaker action succeeds\n               * @event CircuitBreaker#success\n               * @type {any} the return value from the circuit\n               */\n              this.emit('success', result, (Date.now() - latencyStartTime));\n              resetCoalesce(this, cacheKey, 'success');\n              this.semaphore.release();\n              resolve(result);\n              if (this.options.cache) {\n                this.options.cacheTransport.set(\n                  cacheKey,\n                  promise,\n                  this.options.cacheTTL > 0\n                    ? Date.now() + this.options.cacheTTL\n                    : 0\n                );\n              }\n            }\n          })\n            .catch(error => {\n              if (!timeoutError) {\n                this.semaphore.release();\n                const latencyEndTime = Date.now() - latencyStartTime;\n                handleError(\n                  error, this, timeout, args, latencyEndTime, resolve, reject);\n                resetCoalesce(this, cacheKey, 'error');\n              }\n            });\n        } catch (error) {\n          this.semaphore.release();\n          const latency = Date.now() - latencyStartTime;\n          handleError(error, this, timeout, args, latency, resolve, reject);\n          resetCoalesce(this, cacheKey, 'error');\n        }\n      } else {\n        const latency = Date.now() - latencyStartTime;\n        const err = buildError('Semaphore locked', 'ESEMLOCKED');\n        /**\n         * Emitted when the rate limit has been reached and there\n         * are no more locks to be obtained.\n         * @event CircuitBreaker#semaphoreLocked\n         * @type {Error}\n         */\n        this.emit('semaphoreLocked', err, latency);\n        handleError(err, this, timeout, args, latency, resolve, reject);\n        resetCoalesce(this, cacheKey);\n      }\n    });\n\n    /* When coalesce is enabled, store promise in coalesceCache */\n    if (this.options.coalesce) {\n      this.options.coalesceCache.set(\n        cacheKey,\n        call,\n        this.options.coalesceTTL > 0\n          ? Date.now() + this.options.coalesceTTL\n          : 0\n      );\n    }\n\n    return call;\n  }\n\n  /**\n   * Clears the cache of this {@link CircuitBreaker}\n   * @returns {void}\n   */\n  clearCache () {\n    if (this.options.cache) {\n      this.options.cacheTransport.flush();\n    }\n\n    if (this.options.coalesceCache) {\n      this.options.coalesceCache.flush();\n    }\n  }\n\n  /**\n   * Provide a health check function to be called periodically. The function\n   * should return a Promise. If the promise is rejected the circuit will open.\n   * This is in addition to the existing circuit behavior as defined by\n   * `options.errorThresholdPercentage` in the constructor. For example, if the\n   * health check function provided here always returns a resolved promise, the\n   * circuit can still trip and open if there are failures exceeding the\n   * configured threshold. The health check function is executed within the\n   * circuit breaker's execution context, so `this` within the function is the\n   * circuit breaker itself.\n   *\n   * @param {Function} func a health check function which returns a promise.\n   * @param {Number} [interval] the amount of time between calls to the health\n   * check function. Default: 5000 (5 seconds)\n   *\n   * @returns {void}\n   *\n   * @fires CircuitBreaker#healthCheckFailed\n   * @throws {TypeError} if `interval` is supplied but not a number\n   */\n  healthCheck (func, interval) {\n    interval = interval || 5000;\n    if (typeof func !== 'function') {\n      throw new TypeError('Health check function must be a function');\n    }\n    if (isNaN(interval)) {\n      throw new TypeError('Health check interval must be a number');\n    }\n\n    const check = _ => {\n      func.apply(this).catch(e => {\n        /**\n         * Emitted with the user-supplied health check function\n         * returns a rejected promise.\n         * @event CircuitBreaker#healthCheckFailed\n         * @type {Error}\n         */\n        this.emit('healthCheckFailed', e);\n        this.open();\n      });\n    };\n\n    const timer = setInterval(check, interval);\n    if (typeof timer.unref === 'function') {\n      timer.unref();\n    }\n\n    check();\n  }\n\n  /**\n   * Enables this circuit. If the circuit is the  disabled\n   * state, it will be re-enabled. If not, this is essentially\n   * a noop.\n   * @returns {void}\n   */\n  enable () {\n    this[ENABLED] = true;\n    this.status.startListeneningForRotateEvent();\n  }\n\n  /**\n   * Disables this circuit, causing all calls to the circuit's function\n   * to be executed without circuit or fallback protection.\n   * @returns {void}\n   */\n  disable () {\n    this[ENABLED] = false;\n    this.status.removeRotateBucketControllerListener();\n  }\n\n  /**\n   * Retrieves the current AbortSignal from the abortController, if available.\n   * This signal can be used to monitor ongoing requests.\n   * @returns {AbortSignal|undefined} The AbortSignal if present,\n   * otherwise undefined.\n   */\n  getSignal () {\n    if (this.options.abortController && this.options.abortController.signal) {\n      return this.options.abortController.signal;\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Retrieves the current AbortController instance.\n   * This controller can be used to manually abort ongoing requests or create\n   * a new signal.\n   * @returns {AbortController|undefined} The AbortController if present,\n   * otherwise undefined.\n   */\n  getAbortController () {\n    return this.options.abortController;\n  }\n}\n\nfunction handleError (error, circuit, timeout, args, latency, resolve, reject) {\n  clearTimeout(timeout);\n\n  if (circuit.options.errorFilter(error, ...args)) {\n    // The error was filtered, so emit 'success'\n    circuit.emit('success', error, latency);\n  } else {\n    // Error was not filtered, so emit 'failure'\n    fail(circuit, error, args, latency);\n\n    // Only call the fallback function if errorFilter doesn't succeed\n    // If the fallback function succeeds, resolve\n    const fb = fallback(circuit, error, args);\n    if (fb) return resolve(fb);\n  }\n\n  // In all other cases, reject\n  reject(error);\n}\n\nfunction fallback (circuit, err, args) {\n  if (circuit[FALLBACK_FUNCTION]) {\n    try {\n      const result =\n      circuit[FALLBACK_FUNCTION]\n        .apply(circuit[FALLBACK_FUNCTION], [...args, err]);\n      /**\n       * Emitted when the circuit breaker executes a fallback function\n       * @event CircuitBreaker#fallback\n       * @type {any} the return value of the fallback function\n       */\n      circuit.emit('fallback', result, err);\n      if (result instanceof Promise) return result;\n      return Promise.resolve(result);\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n}\n\nfunction fail (circuit, err, args, latency) {\n  /**\n   * Emitted when the circuit breaker action fails\n   * @event CircuitBreaker#failure\n   * @type {Error}\n   */\n  circuit.emit('failure', err, latency, args);\n  if (circuit.warmUp) return;\n\n  // check stats to see if the circuit should be opened\n  const stats = circuit.stats;\n  if ((stats.fires < circuit.volumeThreshold) && !circuit.halfOpen) return;\n  const errorRate = stats.failures / stats.fires * 100;\n  if (errorRate > circuit.options.errorThresholdPercentage ||\n    circuit.halfOpen) {\n    circuit.open();\n  }\n}\n\nfunction resetCoalesce (circuit, cacheKey, event) {\n/**\n * Reset coalesce cache for this cacheKey, depending on\n * options.coalesceResetOn set.\n * @param {@link CircuitBreaker} circuit what circuit is to be cleared\n * @param {string} cacheKey cache key to clear.\n * @param {string} event optional, can be `error`, `success`, `timeout`\n * @returns {void}\n */\n  if (!event || circuit.options.coalesceResetOn.includes(event)) {\n    circuit.options.coalesceCache?.delete(cacheKey);\n  }\n}\n\nfunction buildError (msg, code) {\n  const error = new Error(msg);\n  error.code = code;\n  error[OUR_ERROR] = true;\n  return error;\n}\n\n// http://stackoverflow.com/a/2117523\nconst nextName = () =>\n  'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = Math.random() * 16 | 0;\n    const v = c === 'x' ? r : (r & 0x3 | 0x8);\n    return v.toString(16);\n  });\n\nmodule.exports = exports = CircuitBreaker;\n", "/**\n * Simple in-memory cache implementation\n * @class MemoryCache\n * @property {Map} cache Cache map\n */\nclass MemoryCache {\n  constructor (maxEntries) {\n    this.cache = new Map();\n    this.maxEntries = maxEntries ?? 2 ** 24 - 1; // Max size for Map is 2^24.\n  }\n\n  /**\n   * Get cache value by key\n   * @param {string} key Cache key\n   * @return {any} Response from cache\n   */\n  get (key) {\n    const cached = this.cache.get(key);\n    if (cached) {\n      if (cached.expiresAt > Date.now() || cached.expiresAt === 0) {\n        return cached.value;\n      }\n      this.cache.delete(key);\n    }\n    return undefined;\n  }\n\n  /**\n   * Set cache key with value and ttl\n   * @param {string} key Cache key\n   * @param {any} value Value to cache\n   * @param {number} ttl Time to live in milliseconds\n   * @return {void}\n   */\n  set (key, value, ttl) {\n    // Evict first entry when at capacity - only when it's a new key.\n    if (this.cache.size === this.maxEntries && this.get(key) === undefined) {\n      this.cache.delete(this.cache.keys().next().value);\n    }\n\n    this.cache.set(key, {\n      expiresAt: ttl,\n      value\n    });\n  }\n\n  /**\n   * Delete cache key\n   * @param {string} key Cache key\n   * @return {void}\n   */\n  delete (key) {\n    this.cache.delete(key);\n  }\n\n  /**\n   * Clear cache\n   * @returns {void}\n   */\n  flush () {\n    this.cache.clear();\n  }\n}\n\nmodule.exports = exports = MemoryCache;\n", "'use strict';\n\nmodule.exports = exports = require('./lib/circuit');\n", "'use strict';\n\nconst WINDOW = Symbol('window');\nconst BUCKETS = Symbol('buckets');\nconst TIMEOUT = Symbol('timeout');\nconst PERCENTILES = Symbol('percentiles');\nconst BUCKET_INTERVAL = Symbol('bucket-interval');\nconst SNAPSHOT_INTERVAL = Symbol('snapshot-interval');\nconst ROTATE_EVENT_NAME = Symbol('rotate-event-name');\n\nconst EventEmitter = require('events').EventEmitter;\n\n/**\n * Tracks execution status for a given {@link CircuitBreaker}.\n * A Status instance is created for every {@link CircuitBreaker}\n * and does not typically need to be created by a user.\n *\n * A Status instance will listen for all events on the {@link CircuitBreaker}\n * and track them in a rolling statistical window. The window duration is\n * determined by the `rollingCountTimeout` option provided to the\n * {@link CircuitBreaker}. The window consists of an array of Objects,\n * each representing the counts for a {@link CircuitBreaker}'s events.\n *\n * The array's length is determined by the {@link CircuitBreaker}'s\n * `rollingCountBuckets` option. The duration of each slice of the window\n * is determined by dividing the `rollingCountTimeout` by\n * `rollingCountBuckets`.\n *\n * @class Status\n * @extends EventEmitter\n * @param {Object} options for the status window\n * @param {Number} options.rollingCountBuckets number of buckets in the window\n * @param {Number} options.rollingCountTimeout the duration of the window\n * @param {Boolean} options.rollingPercentilesEnabled whether to calculate\n * percentiles\n * @param {Object} options.stats object of previous stats\n * @example\n * // Creates a 1 second window consisting of ten time slices,\n * // each 100ms long.\n * const circuit = circuitBreaker(fs.readFile,\n *  { rollingCountBuckets: 10, rollingCountTimeout: 1000});\n *\n * // get the cumulative statistics for the last second\n * circuit.status.stats;\n *\n * // get the array of 10, 1 second time slices for the last second\n * circuit.status.window;\n * @fires Status#snapshot\n * @see CircuitBreaker#status\n */\nclass Status extends EventEmitter {\n  constructor (options) {\n    super();\n\n    // Set up our statistical rolling window\n    this[BUCKETS] = options.rollingCountBuckets || 10;\n    this[TIMEOUT] = options.rollingCountTimeout || 10000;\n    this[WINDOW] = new Array(this[BUCKETS]);\n    this[PERCENTILES] = [0.0, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99, 0.995, 1];\n    this[ROTATE_EVENT_NAME] = 'rotate';\n\n    // Default this value to true\n    this.rollingPercentilesEnabled =\n    options.rollingPercentilesEnabled !== false;\n\n    // Default this value to true\n    this.enableSnapshots = options.enableSnapshots !== false;\n\n    // can be undefined\n    this.rotateBucketController = options.rotateBucketController;\n    this.rotateBucket = nextBucket(this[WINDOW]);\n\n    // prime the window with buckets\n    for (let i = 0; i < this[BUCKETS]; i++) this[WINDOW][i] = bucket();\n\n    const bucketInterval = Math.floor(this[TIMEOUT] / this[BUCKETS]);\n\n    if (this.rotateBucketController) {\n      // rotate the buckets based on an optional EventEmitter\n      this.startListeneningForRotateEvent();\n    } else {\n      // or rotate the buckets periodically\n      this[BUCKET_INTERVAL] = setInterval(this.rotateBucket, bucketInterval);\n      // No unref() in the browser\n      if (typeof this[BUCKET_INTERVAL].unref === 'function') {\n        this[BUCKET_INTERVAL].unref();\n      }\n    }\n\n    /**\n     * Emitted at each time-slice. Listeners for this\n     * event will receive a cumulative snapshot of the current status window.\n     * @event Status#snapshot\n     * @type {Object}\n     */\n    if (this.enableSnapshots) {\n      this[SNAPSHOT_INTERVAL] = setInterval(\n        _ => this.emit('snapshot', this.stats),\n        bucketInterval);\n      if (typeof this[SNAPSHOT_INTERVAL].unref === 'function') {\n        this[SNAPSHOT_INTERVAL].unref();\n      }\n    }\n\n    if (options.stats) {\n      this[WINDOW][0] = { ...bucket(), ...options.stats };\n    }\n  }\n\n  /**\n   * Get the cumulative stats for the current window\n   * @type {Object}\n   */\n  get stats () {\n    const totals = this[WINDOW].reduce((acc, val) => {\n      if (!val) { return acc; }\n      Object.keys(acc).forEach(key => {\n        if (key !== 'latencyTimes' && key !== 'percentiles') {\n          (acc[key] += val[key] || 0);\n        }\n      });\n\n      if (this.rollingPercentilesEnabled) {\n        if (val.latencyTimes) {\n          acc.latencyTimes = acc.latencyTimes.concat(val.latencyTimes);\n        }\n      }\n      return acc;\n    }, bucket());\n\n    if (this.rollingPercentilesEnabled) {\n      // Sort the latencyTimes\n      totals.latencyTimes.sort((a, b) => a - b);\n\n      // Get the mean latency\n      // Mean = sum of all values in the array/length of array\n      if (totals.latencyTimes.length) {\n        totals.latencyMean =\n          (totals\n            .latencyTimes\n            .reduce((a, b) => a + b, 0)) / totals.latencyTimes.length;\n      } else {\n        totals.latencyMean = 0;\n      }\n\n      // Calculate Percentiles\n      this[PERCENTILES].forEach(percentile => {\n        totals.percentiles[percentile] =\n          calculatePercentile(percentile, totals.latencyTimes);\n      });\n    } else {\n      totals.latencyMean = -1;\n      this[PERCENTILES].forEach(percentile => {\n        totals.percentiles[percentile] = -1;\n      });\n    }\n\n    return totals;\n  }\n\n  /**\n   * Gets the stats window as an array of time-sliced objects.\n   * @type {Array}\n   */\n  get window () {\n    return this[WINDOW].slice();\n  }\n\n  increment (property, latencyRunTime) {\n    this[WINDOW][0][property]++;\n    if (property === 'successes' ||\n        property === 'failures' ||\n        property === 'timeouts') {\n      this[WINDOW][0].latencyTimes.push(latencyRunTime || 0);\n    }\n  }\n\n  open () {\n    this[WINDOW][0].isCircuitBreakerOpen = true;\n  }\n\n  close () {\n    this[WINDOW][0].isCircuitBreakerOpen = false;\n  }\n\n  shutdown () {\n    this.removeAllListeners();\n    // interval is not set if rotateBucketController is provided\n    if (this.rotateBucketController === undefined) {\n      clearInterval(this[BUCKET_INTERVAL]);\n    } else {\n      this.removeRotateBucketControllerListener();\n    }\n    if (this.enableSnapshots) {\n      clearInterval(this[SNAPSHOT_INTERVAL]);\n    }\n  }\n\n  removeRotateBucketControllerListener () {\n    if (this.rotateBucketController) {\n      this.rotateBucketController.removeListener(this[ROTATE_EVENT_NAME],\n        this.rotateBucket);\n    }\n  }\n\n  startListeneningForRotateEvent () {\n    if (\n      this.rotateBucketController &&\n      this.rotateBucketController.listenerCount(this[ROTATE_EVENT_NAME],\n        this.rotateBucket) === 0\n    ) {\n      this.rotateBucketController.on(this[ROTATE_EVENT_NAME],\n        this.rotateBucket);\n    }\n  }\n}\n\nconst nextBucket = window => _ => {\n  window.pop();\n  window.unshift(bucket());\n};\n\nconst bucket = _ => ({\n  failures: 0,\n  fallbacks: 0,\n  successes: 0,\n  rejects: 0,\n  fires: 0,\n  timeouts: 0,\n  cacheHits: 0,\n  cacheMisses: 0,\n  coalesceCacheHits: 0,\n  coalesceCacheMisses: 0,\n  semaphoreRejections: 0,\n  percentiles: {},\n  latencyTimes: []\n});\n\nfunction calculatePercentile (percentile, arr) {\n  if (percentile === 0) {\n    return arr[0] || 0;\n  }\n  const idx = Math.ceil(percentile * arr.length);\n  return arr[idx - 1] || 0;\n}\n\nmodule.exports = exports = Status;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(660);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "ReflectOwnKeys", "R", "Reflect", "ReflectApply", "apply", "target", "receiver", "args", "Function", "prototype", "call", "ownKeys", "Object", "getOwnPropertySymbols", "getOwnPropertyNames", "concat", "NumberIsNaN", "Number", "isNaN", "value", "EventEmitter", "init", "this", "once", "emitter", "name", "Promise", "resolve", "reject", "errorListener", "err", "removeListener", "resolver", "slice", "arguments", "eventTargetAgnosticAddListener", "handler", "on", "addErrorHandlerIfEventEmitter", "_events", "undefined", "_eventsCount", "_maxListeners", "defaultMaxListeners", "checkListener", "listener", "TypeError", "_getMaxListeners", "that", "_addListener", "type", "prepend", "m", "events", "existing", "warning", "create", "newListener", "emit", "unshift", "push", "length", "warned", "w", "Error", "String", "count", "console", "warn", "onceWrapper", "fired", "wrapFn", "_onceWrap", "state", "wrapped", "bind", "_listeners", "unwrap", "evlistener", "arr", "ret", "Array", "i", "unwrapListeners", "arrayClone", "listenerCount", "n", "copy", "flags", "addEventListener", "wrapListener", "arg", "removeEventListener", "defineProperty", "enumerable", "get", "set", "RangeError", "getPrototypeOf", "setMaxListeners", "getMaxListeners", "do<PERSON><PERSON><PERSON>", "error", "er", "message", "context", "len", "listeners", "addListener", "prependListener", "prependOnceListener", "list", "position", "originalListener", "shift", "index", "pop", "spliceOne", "off", "removeAllListeners", "key", "keys", "rawListeners", "eventNames", "resolvers", "counter", "sem", "take", "release", "test", "_", "timeout", "setTimeout", "code", "_toConsumableArray", "r", "isArray", "_arrayLikeToArray", "_arrayWithoutHoles", "Symbol", "iterator", "from", "_iterableToArray", "a", "t", "toString", "constructor", "_unsupportedIterableToArray", "_nonIterableSpread", "e", "_typeof", "o", "_defineProperties", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "toPrimitive", "_toPrimitive", "_isNativeReflectConstruct", "Boolean", "valueOf", "construct", "_getPrototypeOf", "setPrototypeOf", "__proto__", "_setPrototypeOf", "require", "Status", "Semaphore", "MemoryCache", "STATE", "OPEN", "CLOSED", "HALF_OPEN", "PENDING_CLOSE", "SHUTDOWN", "FALLBACK_FUNCTION", "STATUS", "NAME", "GROUP", "ENABLED", "WARMING_UP", "VOLUME_THRESHOLD", "OUR_ERROR", "RESET_TIMEOUT", "WARMUP_TIMEOUT", "LAST_TIMER_AT", "CircuitBreaker", "_EventEmitter", "action", "_options$timeout", "_options$resetTimeout", "_options$errorThresho", "_options$rollingCount", "_options$rollingCount2", "_options$cacheTTL", "_options$cacheGetKey", "_options$coalesceTTL", "_options$coalesceRese", "_this", "options", "_classCallCheck", "ReferenceError", "_assertThisInitialized", "_possibleConstructorReturn", "_callSuper", "resetTimeout", "errorThresholdPercentage", "rollingCountTimeout", "rollingCountBuckets", "rollingPercentilesEnabled", "capacity", "isInteger", "MAX_SAFE_INTEGER", "errorFilter", "cacheTTL", "cacheGetKey", "_len", "_key", "JSON", "stringify", "enableSnapshots", "rotateBucketController", "coalesce", "coalesceTTL", "coalesceResetOn", "filter", "includes", "cache", "cacheTransport", "cacheSize", "flush", "coalesceCache", "coalesceSize", "semaphore", "autoRenewAbortController", "abortController", "AbortController", "abort", "volumeThreshold", "allowWarmUp", "status", "stats", "enabled", "warmUp", "closed", "halfOpen", "shutdown", "nextName", "group", "timer", "unref", "maxFailures", "circuit", "increment", "property", "result", "runTime", "_half<PERSON>pen", "_renewAbortControllerIfNeeded", "open", "close", "Date", "now", "lastTimerAt", "_inherits", "signal", "aborted", "clearTimeout", "disable", "clearCache", "opened", "isShutdown", "func", "fb", "fire", "_len2", "_key2", "_this2", "buildError", "_len3", "rest", "_key3", "then", "cache<PERSON>ey", "cached", "cachedCall", "pendingClose", "fallback", "timeoutError", "latencyStartTime", "latency", "handleError", "resetCoa<PERSON>ce", "promise", "latencyEndTime", "interval", "_this3", "check", "setInterval", "startListeneningForRotateEvent", "removeRotateBucketControllerListener", "_circuit$options", "fires", "failures", "fail", "event", "_circuit$options$coal", "msg", "replace", "c", "Math", "random", "maxEntries", "Map", "pow", "expiresAt", "ttl", "size", "next", "clear", "getOwnPropertyDescriptor", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "WINDOW", "BUCKETS", "TIMEOUT", "PERCENTILES", "BUCKET_INTERVAL", "SNAPSHOT_INTERVAL", "ROTATE_EVENT_NAME", "rotateBucket", "nextBucket", "bucket", "bucketInterval", "floor", "totals", "reduce", "acc", "val", "latencyTimes", "sort", "b", "latency<PERSON>ean", "percentile", "percentiles", "ceil", "calculatePercentile", "latencyRunTime", "isCircuitBreakerOpen", "clearInterval", "window", "fallbacks", "successes", "rejects", "timeouts", "cacheHits", "cacheMisses", "coalesceCacheHits", "coalesceCacheMisses", "semaphoreRejections", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}