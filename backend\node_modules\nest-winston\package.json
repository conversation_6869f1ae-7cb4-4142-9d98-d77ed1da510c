{"name": "nest-winston", "version": "1.10.2", "license": "MIT", "description": "A Nest module wrapper for winston", "keywords": ["<PERSON><PERSON><PERSON>", "winston", "logger"], "author": "<PERSON> <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["/dist"], "scripts": {"lint": "eslint src/**/*.ts --ignore-pattern src/**/*.spec.ts", "test": "jest", "prebuild": "rimraf dist/*", "build": "tsc"}, "dependencies": {"fast-safe-stringify": "^2.1.1"}, "devDependencies": {"@nestjs/common": "11.0.3", "@nestjs/core": "11.0.3", "@nestjs/platform-express": "11.0.3", "@nestjs/testing": "11.0.3", "@types/jest": "29.5.14", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "eslint": "8.57.1", "jest": "29.7.0", "reflect-metadata": "0.2.2", "rimraf": "6.0.1", "rxjs": "7.8.1", "source-map-support": "0.5.21", "ts-jest": "29.2.5", "ts-node": "10.9.2", "typescript": "4.9.5", "winston": "3.16.0"}, "peerDependencies": {"@nestjs/common": "^5.0.0 || ^6.6.0 || ^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0 || ^11.0.0", "winston": "^3.0.0"}, "homepage": "https://github.com/gremo/nest-winston#readme", "repository": {"type": "git", "url": "git+https://github.com/gremo/nest-winston.git"}, "bugs": {"url": "https://github.com/gremo/nest-winston/issues"}}