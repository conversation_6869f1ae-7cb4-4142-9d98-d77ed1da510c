import { Injectable } from '@nestjs/common';
import { Incident, IncidentStatus, IncidentPriority } from '../../domain/entities/incident/incident.entity';
import { ThreatSeverity } from '../../domain/enums/threat-severity.enum';
import { UniqueEntityId } from '../../../../shared-kernel/value-objects/unique-entity-id.value-object';
import { DomainEventPublisher } from '../../../../shared-kernel/domain/domain-event-publisher';
import { Logger } from '../../../../shared-kernel/infrastructure/logger';

/**
 * Incident Creation Request
 */
export interface CreateIncidentRequest {
  /** Incident title */
  title: string;
  /** Incident description */
  description: string;
  /** Incident severity */
  severity: ThreatSeverity;
  /** Incident category */
  category: string;
  /** Incident type */
  type: string;
  /** Detection timestamp */
  detectedAt: Date;
  /** Reporting timestamp */
  reportedAt?: Date;
  /** Related event IDs */
  relatedEventIds?: string[];
  /** Related threat IDs */
  relatedThreatIds?: string[];
  /** Related vulnerability IDs */
  relatedVulnerabilityIds?: string[];
  /** Affected assets */
  affectedAssets?: Array<{
    assetId: string;
    assetName: string;
    assetType: string;
    criticality: 'low' | 'medium' | 'high' | 'critical';
    impact: 'none' | 'low' | 'medium' | 'high' | 'critical';
  }>;
  /** Incident commander */
  incidentCommander?: string;
  /** Initial response team */
  responseTeam?: string[];
  /** Tags */
  tags?: string[];
  /** Custom attributes */
  attributes?: Record<string, any>;
}

/**
 * Incident Update Request
 */
export interface UpdateIncidentRequest {
  /** Incident ID */
  incidentId: string;
  /** Status change */
  status?: IncidentStatus;
  /** Priority change */
  priority?: IncidentPriority;
  /** Severity change */
  severity?: ThreatSeverity;
  /** Update reason */
  reason?: string;
  /** Updated by */
  updatedBy: string;
}

/**
 * Response Team Assignment
 */
export interface ResponseTeamAssignment {
  /** Incident ID */
  incidentId: string;
  /** Incident commander */
  incidentCommander?: string;
  /** Lead investigator */
  leadInvestigator?: string;
  /** Team members */
  members?: Array<{
    userId: string;
    name: string;
    role: string;
    expertise: string[];
  }>;
  /** External consultants */
  externalConsultants?: Array<{
    name: string;
    organization: string;
    role: string;
    contactInfo: string;
  }>;
}

/**
 * Incident Search Criteria
 */
export interface IncidentSearchCriteria {
  /** Status filter */
  status?: IncidentStatus[];
  /** Priority filter */
  priority?: IncidentPriority[];
  /** Severity filter */
  severity?: ThreatSeverity[];
  /** Category filter */
  category?: string[];
  /** Date range */
  dateRange?: {
    from: Date;
    to: Date;
  };
  /** Assigned to */
  assignedTo?: string;
  /** Text search */
  searchText?: string;
  /** Tags */
  tags?: string[];
  /** Pagination */
  pagination?: {
    page: number;
    limit: number;
  };
}

/**
 * Incident Statistics
 */
export interface IncidentStatistics {
  /** Total incidents */
  total: number;
  /** Status distribution */
  statusDistribution: Record<IncidentStatus, number>;
  /** Priority distribution */
  priorityDistribution: Record<IncidentPriority, number>;
  /** Severity distribution */
  severityDistribution: Record<ThreatSeverity, number>;
  /** Average resolution time */
  averageResolutionTime: number;
  /** SLA compliance rate */
  slaComplianceRate: number;
  /** Trends */
  trends: {
    daily: Array<{ date: string; count: number }>;
    weekly: Array<{ week: string; count: number }>;
    monthly: Array<{ month: string; count: number }>;
  };
}

/**
 * Incident Management Application Service
 * 
 * Manages the complete incident response lifecycle from creation
 * to closure, including team coordination and workflow management.
 * 
 * Key responsibilities:
 * - Incident creation and lifecycle management
 * - Response team coordination
 * - Workflow automation and tracking
 * - SLA monitoring and compliance
 * - Reporting and analytics
 */
@Injectable()
export class IncidentManagementService {
  private readonly logger = new Logger(IncidentManagementService.name);

  constructor(
    private readonly eventPublisher: DomainEventPublisher
  ) {}

  /**
   * Create a new incident
   */
  async createIncident(request: CreateIncidentRequest): Promise<Incident> {
    this.logger.info('Creating new incident', {
      title: request.title,
      severity: request.severity,
      category: request.category,
      type: request.type,
    });

    try {
      // Convert related IDs to UniqueEntityId objects
      const relatedEventIds = request.relatedEventIds?.map(id => UniqueEntityId.fromString(id)) || [];
      const relatedThreatIds = request.relatedThreatIds?.map(id => UniqueEntityId.fromString(id)) || [];
      const relatedVulnerabilityIds = request.relatedVulnerabilityIds?.map(id => UniqueEntityId.fromString(id)) || [];

      // Convert affected assets to incident asset format
      const affectedAssets = request.affectedAssets?.map(asset => ({
        ...asset,
        status: 'operational' as const,
        recoveryStatus: 'not_started' as const,
        affectedServices: [],
      })) || [];

      const incident = Incident.create(
        {
          title: request.title,
          description: request.description,
          severity: request.severity,
          category: request.category,
          type: request.type,
        },
        {
          detectedAt: request.detectedAt,
          reportedAt: request.reportedAt || new Date(),
        },
        {
          relatedEventIds,
          relatedThreatIds,
          relatedVulnerabilityIds,
          affectedAssets,
          incidentCommander: request.incidentCommander,
          tags: request.tags,
          attributes: request.attributes,
        }
      );

      // Assign initial response team if provided
      if (request.responseTeam && request.responseTeam.length > 0) {
        await this.assignResponseTeam({
          incidentId: incident.id.toString(),
          members: request.responseTeam.map(userId => ({
            userId,
            name: userId, // Would be resolved from user service
            role: 'analyst',
            expertise: [],
          })),
        });
      }

      // Auto-assign incident commander for critical incidents
      if (incident.isCritical() && !request.incidentCommander) {
        await this.autoAssignIncidentCommander(incident);
      }

      // Publish domain events
      await this.eventPublisher.publishAll(incident.getDomainEvents());
      incident.clearDomainEvents();

      this.logger.info('Incident created successfully', {
        incidentId: incident.id.toString(),
        priority: incident.priority,
        isCritical: incident.isCritical(),
      });

      return incident;

    } catch (error) {
      this.logger.error('Failed to create incident', {
        error: error.message,
        request,
      });
      throw error;
    }
  }

  /**
   * Update incident status
   */
  async updateIncident(request: UpdateIncidentRequest): Promise<void> {
    this.logger.info('Updating incident', {
      incidentId: request.incidentId,
      status: request.status,
      priority: request.priority,
      updatedBy: request.updatedBy,
    });

    try {
      // This would typically load the incident from repository
      // For now, we'll simulate the update process
      
      const reason = request.reason || 'Status updated';

      // Validate status transition
      if (request.status) {
        this.validateStatusTransition(request.status, reason);
      }

      // Update incident properties
      // incident.changeStatus(request.status, reason);
      
      // Publish domain events
      // await this.eventPublisher.publishAll(incident.getDomainEvents());

      this.logger.info('Incident updated successfully', {
        incidentId: request.incidentId,
        newStatus: request.status,
      });

    } catch (error) {
      this.logger.error('Failed to update incident', {
        error: error.message,
        incidentId: request.incidentId,
      });
      throw error;
    }
  }

  /**
   * Assign response team to incident
   */
  async assignResponseTeam(assignment: ResponseTeamAssignment): Promise<void> {
    this.logger.info('Assigning response team', {
      incidentId: assignment.incidentId,
      incidentCommander: assignment.incidentCommander,
      memberCount: assignment.members?.length || 0,
    });

    try {
      // This would typically load the incident and update the response team
      // For now, we'll simulate the assignment process

      // Validate team assignments
      if (assignment.incidentCommander) {
        await this.validateIncidentCommander(assignment.incidentCommander);
      }

      if (assignment.members) {
        await this.validateTeamMembers(assignment.members);
      }

      // Send notifications to assigned team members
      await this.notifyTeamAssignment(assignment);

      this.logger.info('Response team assigned successfully', {
        incidentId: assignment.incidentId,
      });

    } catch (error) {
      this.logger.error('Failed to assign response team', {
        error: error.message,
        incidentId: assignment.incidentId,
      });
      throw error;
    }
  }

  /**
   * Escalate incident
   */
  async escalateIncident(
    incidentId: string,
    reason: string,
    escalatedTo: string,
    escalatedBy: string
  ): Promise<void> {
    this.logger.info('Escalating incident', {
      incidentId,
      reason,
      escalatedTo,
      escalatedBy,
    });

    try {
      // This would typically load the incident and escalate it
      // For now, we'll simulate the escalation process

      // Validate escalation
      await this.validateEscalation(incidentId, escalatedTo);

      // Update incident priority and assign new commander
      // incident.escalate(reason, escalatedTo);

      // Notify stakeholders
      await this.notifyEscalation(incidentId, reason, escalatedTo);

      this.logger.info('Incident escalated successfully', {
        incidentId,
        escalatedTo,
      });

    } catch (error) {
      this.logger.error('Failed to escalate incident', {
        error: error.message,
        incidentId,
      });
      throw error;
    }
  }

  /**
   * Search incidents
   */
  async searchIncidents(criteria: IncidentSearchCriteria): Promise<{
    incidents: Incident[];
    total: number;
    page: number;
    limit: number;
  }> {
    this.logger.debug('Searching incidents', { criteria });

    try {
      // This would typically query the incident repository
      // For now, return empty results
      return {
        incidents: [],
        total: 0,
        page: criteria.pagination?.page || 1,
        limit: criteria.pagination?.limit || 20,
      };

    } catch (error) {
      this.logger.error('Failed to search incidents', {
        error: error.message,
        criteria,
      });
      throw error;
    }
  }

  /**
   * Get incident statistics
   */
  async getIncidentStatistics(
    dateRange?: { from: Date; to: Date }
  ): Promise<IncidentStatistics> {
    this.logger.debug('Getting incident statistics', { dateRange });

    try {
      // This would typically query the incident repository for statistics
      // For now, return mock statistics
      return {
        total: 0,
        statusDistribution: {} as Record<IncidentStatus, number>,
        priorityDistribution: {} as Record<IncidentPriority, number>,
        severityDistribution: {} as Record<ThreatSeverity, number>,
        averageResolutionTime: 0,
        slaComplianceRate: 0,
        trends: {
          daily: [],
          weekly: [],
          monthly: [],
        },
      };

    } catch (error) {
      this.logger.error('Failed to get incident statistics', {
        error: error.message,
        dateRange,
      });
      throw error;
    }
  }

  /**
   * Get incident SLA status
   */
  async getIncidentSLAStatus(incidentId: string): Promise<{
    isCompliant: boolean;
    responseTime: {
      sla: number;
      actual: number;
      remaining: number;
      isOverdue: boolean;
    };
    resolutionTime: {
      sla: number;
      actual?: number;
      remaining: number;
      isOverdue: boolean;
    };
  }> {
    this.logger.debug('Getting incident SLA status', { incidentId });

    try {
      // This would typically load the incident and calculate SLA metrics
      // For now, return mock SLA status
      return {
        isCompliant: true,
        responseTime: {
          sla: 240, // 4 hours
          actual: 120, // 2 hours
          remaining: 0,
          isOverdue: false,
        },
        resolutionTime: {
          sla: 1440, // 24 hours
          remaining: 720, // 12 hours
          isOverdue: false,
        },
      };

      return slaStatus;
    } catch (error) {
      this.logger.error('Failed to get incident SLA status', {
        error: error.message,
        incidentId,
      });
      throw error;
    }
  }

  /**
   * Auto-assign incident commander for critical incidents
   */
  private async autoAssignIncidentCommander(incident: Incident): Promise<void> {
    // This would typically query a user service to find available commanders
    // For now, we'll simulate the assignment
    const availableCommanders = await this.getAvailableIncidentCommanders();
    
    if (availableCommanders.length > 0) {
      const commander = availableCommanders[0];
      await this.assignResponseTeam({
        incidentId: incident.id.toString(),
        incidentCommander: commander.id,
      });
    }
  }

  /**
   * Get available incident commanders
   */
  private async getAvailableIncidentCommanders(): Promise<Array<{
    id: string;
    name: string;
    expertise: string[];
    currentLoad: number;
  }>> {
    // This would typically query a user service
    return [];
  }

  /**
   * Validate status transition
   */
  private validateStatusTransition(newStatus: IncidentStatus, reason: string): void {
    // Implement status transition validation logic
    if (!reason || reason.trim().length === 0) {
      throw new Error('Status change reason is required');
    }
  }

  /**
   * Validate incident commander
   */
  private async validateIncidentCommander(commanderId: string): Promise<void> {
    // This would typically validate the commander exists and has proper permissions
    if (!commanderId) {
      throw new Error('Incident commander ID is required');
    }
  }

  /**
   * Validate team members
   */
  private async validateTeamMembers(members: Array<{
    userId: string;
    name: string;
    role: string;
    expertise: string[];
  }>): Promise<void> {
    // This would typically validate team members exist and have proper permissions
    for (const member of members) {
      if (!member.userId) {
        throw new Error('Team member user ID is required');
      }
    }
  }

  /**
   * Validate escalation
   */
  private async validateEscalation(incidentId: string, escalatedTo: string): Promise<void> {
    // This would typically validate the escalation target
    if (!escalatedTo) {
      throw new Error('Escalation target is required');
    }
  }

  /**
   * Notify team assignment
   */
  private async notifyTeamAssignment(assignment: ResponseTeamAssignment): Promise<void> {
    // This would typically send notifications to assigned team members
    this.logger.info('Sending team assignment notifications', {
      incidentId: assignment.incidentId,
    });
  }

  /**
   * Notify escalation
   */
  private async notifyEscalation(
    incidentId: string,
    reason: string,
    escalatedTo: string
  ): Promise<void> {
    // This would typically send escalation notifications
    this.logger.info('Sending escalation notifications', {
      incidentId,
      escalatedTo,
    });
  }
}
