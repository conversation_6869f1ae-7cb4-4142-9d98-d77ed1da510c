# Sentinel Platform SonarQube Configuration
# Code quality and security analysis configuration

# Project identification
sonar.projectKey=sentinel-platform-backend
sonar.projectName=Sentinel Platform Backend
sonar.projectVersion=1.0.0
sonar.organization=sentinel-security

# Source code configuration
sonar.sources=src
sonar.tests=test,src/**/*.spec.ts,src/**/*.test.ts
sonar.exclusions=**/*.spec.ts,**/*.test.ts,**/node_modules/**,**/dist/**,**/coverage/**

# Test configuration
sonar.test.inclusions=**/*.spec.ts,**/*.test.ts
sonar.testExecutionReportPaths=coverage/test-reporter.xml
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/main.ts,**/*.module.ts,**/migrations/**

# Language configuration
sonar.typescript.node=node
sonar.typescript.tsconfigPath=tsconfig.json

# Code analysis rules
sonar.qualitygate.wait=true

# Security analysis
sonar.security.hotspots.inheritFromParent=true

# Duplication analysis
sonar.cpd.exclusions=**/*.spec.ts,**/*.test.ts

# Issue exclusions
sonar.issue.ignore.multicriteria=e1,e2,e3

# Exclude test files from certain rules
sonar.issue.ignore.multicriteria.e1.ruleKey=typescript:S3776
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.spec.ts

# Exclude generated files
sonar.issue.ignore.multicriteria.e2.ruleKey=*
sonar.issue.ignore.multicriteria.e2.resourceKey=**/migrations/**

# Exclude configuration files from complexity rules
sonar.issue.ignore.multicriteria.e3.ruleKey=typescript:S3776
sonar.issue.ignore.multicriteria.e3.resourceKey=**/*.config.ts

# Analysis parameters
sonar.sourceEncoding=UTF-8
sonar.scm.provider=git
sonar.scm.forceReloadAll=true

# Branch analysis
sonar.branch.name=${BRANCH_NAME}
sonar.pullrequest.key=${PULL_REQUEST_KEY}
sonar.pullrequest.branch=${PULL_REQUEST_BRANCH}
sonar.pullrequest.base=${PULL_REQUEST_BASE}
