{"version": 3, "file": "utility.js", "sourceRoot": "", "sources": ["../../src/node-saml/utility.ts"], "names": [], "mappings": ";;;AACA,+BAAgC;AAEhC,SAAgB,cAAc,CAAI,KAA2B,EAAE,KAAc;IAC3E,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;QAC9F,MAAM,IAAI,SAAS,CAAC,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,sBAAsB,CAAC,CAAC;KACtD;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAND,wCAMC;AAED,SAAgB,eAAe,CAAC,WAAmB,EAAE,OAA2B;IAC9E,MAAM,aAAa,GACjB,2FAA2F,CAAC;IAE9F,OAAO,IAAA,aAAO,EACZ,WAAW,EACX,aAAa,EACb,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,QAAQ,EAAE,EAC9C,OAAO,CACR,CAAC;AACJ,CAAC;AAVD,0CAUC", "sourcesContent": ["import { SamlSigningOptions } from \"./types\";\nimport { signXml } from \"./xml\";\n\nexport function assertRequired<T>(value: T | null | undefined, error?: string): T {\n  if (value === undefined || value === null || (typeof value === \"string\" && value.length === 0)) {\n    throw new TypeError(error ?? \"value does not exist\");\n  } else {\n    return value;\n  }\n}\n\nexport function signXmlResponse(samlMessage: string, options: SamlSigningOptions): string {\n  const responseXpath =\n    '//*[local-name(.)=\"Response\" and namespace-uri(.)=\"urn:oasis:names:tc:SAML:2.0:protocol\"]';\n\n  return signXml(\n    samlMessage,\n    responseXpath,\n    { reference: responseXpath, action: \"append\" },\n    options\n  );\n}\n"]}