/**
 * Returns `true` if the value is of type `string`.
 *
 * @param value - Any value
 * @returns A boolean value for Type Guard
 */
export declare const isString: (value: unknown) => value is string;
/**
 * Returns `true` if the value is an instance of `Error`.
 *
 * @param value - Any value
 * @returns A boolean value for Type Guard
 */
export declare const isError: (value: unknown) => value is Error;
