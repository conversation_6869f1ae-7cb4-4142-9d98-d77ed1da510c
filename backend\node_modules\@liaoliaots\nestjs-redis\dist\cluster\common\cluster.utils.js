"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createClient = void 0;
const ioredis_1 = require("ioredis");
const messages_1 = require("../../messages");
const cluster_logger_1 = require("../cluster-logger");
const utils_1 = require("../../utils");
const cluster_constants_1 = require("../cluster.constants");
const createClient = ({ namespace, nodes, onClientCreated, ...clusterOptions }, { readyLog, errorLog }) => {
    const client = new ioredis_1.Cluster(nodes, clusterOptions);
    Reflect.defineProperty(client, cluster_constants_1.NAMESPACE_KEY, {
        value: namespace ?? cluster_constants_1.DEFAULT_CLUSTER,
        writable: false,
        enumerable: false,
        configurable: false
    });
    if (readyLog) {
        client.on('ready', () => {
            cluster_logger_1.logger.log((0, messages_1.READY_LOG)((0, utils_1.parseNamespace)((0, utils_1.get)(client, cluster_constants_1.NAMESPACE_KEY))));
        });
    }
    if (errorLog) {
        client.on('error', (error) => {
            cluster_logger_1.logger.error((0, messages_1.ERROR_LOG)((0, utils_1.parseNamespace)((0, utils_1.get)(client, cluster_constants_1.NAMESPACE_KEY)), error.message), error.stack);
        });
    }
    if (onClientCreated)
        onClientCreated(client);
    return client;
};
exports.createClient = createClient;
