{"name": "@liaoliaots/nestjs-redis", "version": "10.0.0", "description": "Redis(ioredis) module for Nest framework (node.js).", "author": "LiaoLiao <<EMAIL>>", "exports": "./dist/index.js", "main": "./dist/index.js", "types": "./dist/index.d.ts", "type": "commonjs", "files": ["dist"], "license": "MIT", "keywords": ["redis", "i<PERSON>is", "sentinel", "cluster", "<PERSON><PERSON><PERSON>", "nest", "nodejs", "node", "typescript", "javascript"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc --project tsconfig.build.json && tsc-alias -p tsconfig.build.json", "lint": "concurrently \"npm:lint:es\" \"npm:lint:tsc\"", "lint:es": "eslint \"{lib,test}/**/*.ts\"", "lint:tsc": "tsc --project tsconfig.json --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json", "test:clear": "jest --clear<PERSON>ache", "make-badges": "istanbul-badges-readme --coverageDir=\"./coverage\" --readmeDir=\"./\" --style=\"flat-square\" --logo=\"jest\"", "madge": "madge --image ./dependency-graph.svg dist/index.js", "publish:alpha": "npm publish --tag alpha", "publish:next": "npm publish --tag next", "publish:rc": "npm publish --tag rc"}, "dependencies": {"tslib": "2.7.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "ioredis": "^5.0.0"}, "engines": {"node": ">=16.13.0"}, "publishConfig": {"access": "public"}, "homepage": "https://github.com/liaoliaots/nestjs-redis#readme", "repository": {"type": "git", "url": "git+https://github.com/liaoliaots/nestjs-redis.git"}, "bugs": {"url": "https://github.com/liaoliaots/nestjs-redis/issues"}}