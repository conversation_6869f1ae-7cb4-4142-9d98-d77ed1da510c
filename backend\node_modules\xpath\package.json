{"name": "xpath", "version": "0.0.32", "description": "DOM 3 XPath implemention and helper for node.js.", "engines": {"node": ">=0.6.0"}, "author": {"name": "<PERSON>"}, "contributors": [{"name": "goto100"}, {"name": "<PERSON>"}], "dependencies": {}, "devDependencies": {"xmldom": "^0.1.19"}, "typings": "./xpath.d.ts", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/goto100/xpath.git"}, "main": "./xpath.js", "keywords": ["xpath", "xml"], "license": "MIT"}