{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/passport-saml/types.ts"], "names": [], "mappings": ";;;AAsEA,MAAa,kBAAmB,SAAQ,KAAK;IAC3C,YAAY,OAAe,EAAkB,SAAiB;QAC5D,KAAK,CAAC,OAAO,CAAC,CAAC;QAD4B,cAAS,GAAT,SAAS,CAAQ;IAE9D,CAAC;CACF;AAJD,gDAIC", "sourcesContent": ["import type * as express from \"express\";\nimport * as passport from \"passport\";\nimport type { SamlOptions, MandatorySamlOptions, SamlIDPListConfig } from \"../node-saml/types\";\n\nexport interface AuthenticateOptions extends passport.AuthenticateOptions {\n  samlFallback?: \"login-request\" | \"logout-request\";\n  additionalParams?: Record<string, any>;\n}\n\nexport interface AuthorizeOptions extends AuthenticateOptions {\n  samlFallback?: \"login-request\" | \"logout-request\";\n}\n\nexport interface StrategyOptions {\n  name?: string;\n  passReqToCallback?: boolean;\n}\n\n/**\n * These options are availble for configuring a SAML strategy\n */\nexport type SamlConfig = Partial<SamlOptions> & StrategyOptions & MandatorySamlOptions;\n\nexport interface Profile {\n  issuer?: string;\n  sessionIndex?: string;\n  nameID?: string;\n  nameIDFormat?: string;\n  nameQualifier?: string;\n  spNameQualifier?: string;\n  ID?: string;\n  mail?: string; // InCommon Attribute urn:oid:0.9.2342.19200300.100.1.3\n  email?: string; // `mail` if not present in the assertion\n  [\"urn:oid:0.9.2342.19200300.100.1.3\"]?: string;\n  getAssertionXml?(): string; // get the raw assertion XML\n  getAssertion?(): Record<string, unknown>; // get the assertion XML parsed as a JavaScript object\n  getSamlResponseXml?(): string; // get the raw SAML response XML\n  [attributeName: string]: unknown; // arbitrary `AttributeValue`s\n}\n\nexport interface RequestWithUser extends express.Request {\n  samlLogoutRequest: any;\n  user?: Profile;\n}\n\nexport type VerifiedCallback = (\n  err: Error | null,\n  user?: Record<string, unknown>,\n  info?: Record<string, unknown>\n) => void;\n\nexport type VerifyWithRequest = (\n  req: express.Request,\n  profile: Profile | null | undefined,\n  done: VerifiedCallback\n) => void;\n\nexport type VerifyWithoutRequest = (\n  profile: Profile | null | undefined,\n  done: VerifiedCallback\n) => void;\n\nexport type SamlOptionsCallback = (err: Error | null, samlOptions?: SamlConfig) => void;\n\ninterface BaseMultiSamlConfig {\n  getSamlOptions(req: express.Request, callback: SamlOptionsCallback): void;\n}\n\nexport type MultiSamlConfig = Partial<SamlConfig> & StrategyOptions & BaseMultiSamlConfig;\n\nexport class ErrorWithXmlStatus extends Error {\n  constructor(message: string, public readonly xmlStatus: string) {\n    super(message);\n  }\n}\n"]}