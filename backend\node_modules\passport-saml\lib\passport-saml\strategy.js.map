{"version": 3, "file": "strategy.js", "sourceRoot": "", "sources": ["../../src/passport-saml/strategy.ts"], "names": [], "mappings": ";;;AAAA,yDAAiE;AACjE,4CAAoC;AACpC,2BAA2B;AAU3B,MAAsB,gBAAiB,SAAQ,4BAAgB;IAU7D,YAAY,OAAmB,EAAE,MAAa;QAC5C,KAAK,EAAE,CAAC;QACR,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC5E;QAED,+FAA+F;QAC/F,uEAAuE;QACvE,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;SACpB;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAK,IAAI,CAAC,WAA+B,CAAC,0BAA0B,EAAE;YACpE,IAAI,CAAC,KAAK,GAAG,IAAI,gBAAI,CAAC,OAAO,CAAC,CAAC;SAChC;QACD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;IACxD,CAAC;IAED,YAAY,CAAC,GAAoB,EAAE,OAA4B;QAC7D,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;SAC5E;QAED,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,eAAe,CAAC;QAC/D,MAAM,gBAAgB,GAAG,CAAC,EACxB,OAAO,EACP,SAAS,GAIV,EAAE,EAAE;YACH,IAAI,SAAS,EAAE;gBACb,GAAG,CAAC,MAAM,EAAE,CAAC;gBACb,IAAI,OAAO,EAAE;oBACX,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;wBACtB,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;qBACnF;oBAED,MAAM,UAAU,GACd,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC3E,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;iBACzF;gBACD,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;aACpB;YAED,MAAM,QAAQ,GAAG,CACf,GAAiB,EACjB,IAA8B,EAC9B,IAA8B,EAC9B,EAAE;gBACF,IAAI,GAAG,EAAE;oBACP,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACxB;gBAED,IAAI,CAAC,IAAI,EAAE;oBACT,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;iBAC7B;gBAED,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3B,CAAC,CAAC;YAEF,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC1B,IAAI,CAAC,OAA6B,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC7D;iBAAM;gBACJ,IAAI,CAAC,OAAgC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC3D;QACH,CAAC,CAAC;QAEF,MAAM,iBAAiB,GAAG,CAAC,GAAiB,EAAE,GAAmB,EAAE,EAAE;YACnE,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACjB;iBAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,GAAI,CAAC,CAAC;aACrB;QACH,CAAC,CAAC;QAEF,IAAI,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAClE,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;YAC/C,IAAI,CAAC,KAAK;iBACP,qBAAqB,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC;iBAC/C,IAAI,CAAC,gBAAgB,CAAC;iBACtB,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACpC;aAAM,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,KAAK;iBACP,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC;iBACnC,IAAI,CAAC,gBAAgB,CAAC;iBACtB,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACpC;aAAM,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;YAC3C,IAAI,CAAC,KAAK;iBACP,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC;iBAClC,IAAI,CAAC,gBAAgB,CAAC;iBACtB,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;SACpC;aAAM;YACL,MAAM,cAAc,GAAG;gBACrB,eAAe,EAAE,KAAK,IAAI,EAAE;oBAC1B,IAAI;wBACF,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;4BACtB,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;yBACjF;wBAED,MAAM,UAAU,GACd,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC3E,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC;wBAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,KAAK,WAAW,EAAE;4BAC1D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;4BACtE,MAAM,GAAG,GAAG,GAAG,CAAC,GAAI,CAAC;4BACrB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAChB;6BAAM;4BACL,4BAA4B;4BAC5B,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;yBACjF;qBACF;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,CAAC,KAAK,CAAC,GAAY,CAAC,CAAC;qBAC1B;gBACH,CAAC;gBACD,gBAAgB,EAAE,KAAK,IAAI,EAAE;oBAC3B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;wBACtB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;qBAClF;oBAED,IAAI;wBACF,MAAM,UAAU,GACd,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC3E,IAAI,CAAC,QAAQ,CACX,MAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAe,EAAE,UAAU,EAAE,OAAO,CAAC,CAC7E,CAAC;qBACH;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,CAAC,KAAK,CAAC,GAAY,CAAC,CAAC;qBAC1B;gBACH,CAAC;aACF,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAExB,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;gBACxC,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACvB;YAED,cAAc,EAAE,CAAC;SAClB;IACH,CAAC;IAED,MAAM,CAAC,GAAoB,EAAE,QAA0D;QACrF,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;SAClE;QACD,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5F,IAAI,CAAC,KAAK;aACP,iBAAiB,CAAC,GAAG,CAAC,IAAe,EAAE,UAAU,EAAE,EAAE,CAAC;aACtD,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aAClC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IACnC,CAAC;IAES,gCAAgC,CACxC,cAA6B,EAC7B,WAA2B;QAE3B,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;SAC9F;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC;IAED,4CAA4C;IAC5C,KAAK,CAAC,GAAU;QACd,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;CACF;AAvLD,4CAuLC;AAED,MAAa,QAAS,SAAQ,gBAAgB;IAG5C,+BAA+B,CAC7B,cAA6B,EAC7B,WAA2B;QAE3B,OAAO,IAAI,CAAC,gCAAgC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IAC5E,CAAC;;AARH,4BASC;AARiB,mCAA0B,GAAG,IAAI,CAAC", "sourcesContent": ["import { Strategy as PassportStrategy } from \"passport-strategy\";\nimport { SAML } from \"../node-saml\";\nimport * as url from \"url\";\nimport {\n  AuthenticateOptions,\n  RequestWithUser,\n  SamlConfig,\n  VerifyWithoutRequest,\n  VerifyWithRequest,\n} from \"./types\";\nimport { Profile } from \"./types\";\n\nexport abstract class AbstractStrategy extends PassportStrategy {\n  static readonly newSamlProviderOnConstruct: boolean;\n\n  name: string;\n  _verify: VerifyWithRequest | VerifyWithoutRequest;\n  _saml: SAML | undefined;\n  _passReqToCallback?: boolean;\n\n  constructor(options: SamlConfig, verify: VerifyWithRequest);\n  constructor(options: SamlConfig, verify: VerifyWithoutRequest);\n  constructor(options: SamlConfig, verify: never) {\n    super();\n    if (typeof options === \"function\") {\n      throw new Error(\"Mandatory SAML options missing\");\n    }\n\n    if (!verify) {\n      throw new Error(\"SAML authentication strategy requires a verify function\");\n    }\n\n    // Customizing the name can be useful to support multiple SAML configurations at the same time.\n    // Unlike other options, this one gets deleted instead of passed along.\n    if (options.name) {\n      this.name = options.name;\n    } else {\n      this.name = \"saml\";\n    }\n\n    this._verify = verify;\n    if ((this.constructor as typeof Strategy).newSamlProviderOnConstruct) {\n      this._saml = new SAML(options);\n    }\n    this._passReqToCallback = !!options.passReqToCallback;\n  }\n\n  authenticate(req: RequestWithUser, options: AuthenticateOptions): void {\n    if (this._saml == null) {\n      throw new Error(\"Can't get authenticate without a SAML provider defined.\");\n    }\n\n    options.samlFallback = options.samlFallback || \"login-request\";\n    const validateCallback = ({\n      profile,\n      loggedOut,\n    }: {\n      profile?: Profile | null;\n      loggedOut?: boolean;\n    }) => {\n      if (loggedOut) {\n        req.logout();\n        if (profile) {\n          if (this._saml == null) {\n            throw new Error(\"Can't get logout response URL without a SAML provider defined.\");\n          }\n\n          const RelayState =\n            (req.query && req.query.RelayState) || (req.body && req.body.RelayState);\n          return this._saml.getLogoutResponseUrl(profile, RelayState, options, redirectIfSuccess);\n        }\n        return this.pass();\n      }\n\n      const verified = (\n        err: Error | null,\n        user?: Record<string, unknown>,\n        info?: Record<string, unknown>\n      ) => {\n        if (err) {\n          return this.error(err);\n        }\n\n        if (!user) {\n          return this.fail(info, 401);\n        }\n\n        this.success(user, info);\n      };\n\n      if (this._passReqToCallback) {\n        (this._verify as VerifyWithRequest)(req, profile, verified);\n      } else {\n        (this._verify as VerifyWithoutRequest)(profile, verified);\n      }\n    };\n\n    const redirectIfSuccess = (err: Error | null, url?: string | null) => {\n      if (err) {\n        this.error(err);\n      } else {\n        this.redirect(url!);\n      }\n    };\n\n    if (req.query && (req.query.SAMLResponse || req.query.SAMLRequest)) {\n      const originalQuery = url.parse(req.url).query;\n      this._saml\n        .validateRedirectAsync(req.query, originalQuery)\n        .then(validateCallback)\n        .catch((err) => this.error(err));\n    } else if (req.body && req.body.SAMLResponse) {\n      this._saml\n        .validatePostResponseAsync(req.body)\n        .then(validateCallback)\n        .catch((err) => this.error(err));\n    } else if (req.body && req.body.SAMLRequest) {\n      this._saml\n        .validatePostRequestAsync(req.body)\n        .then(validateCallback)\n        .catch((err) => this.error(err));\n    } else {\n      const requestHandler = {\n        \"login-request\": async () => {\n          try {\n            if (this._saml == null) {\n              throw new Error(\"Can't process login request without a SAML provider defined.\");\n            }\n\n            const RelayState =\n              (req.query && req.query.RelayState) || (req.body && req.body.RelayState);\n            const host = req.headers && req.headers.host;\n            if (this._saml.options.authnRequestBinding === \"HTTP-POST\") {\n              const data = await this._saml.getAuthorizeFormAsync(RelayState, host);\n              const res = req.res!;\n              res.send(data);\n            } else {\n              // Defaults to HTTP-Redirect\n              this.redirect(await this._saml.getAuthorizeUrlAsync(RelayState, host, options));\n            }\n          } catch (err) {\n            this.error(err as Error);\n          }\n        },\n        \"logout-request\": async () => {\n          if (this._saml == null) {\n            throw new Error(\"Can't process logout request without a SAML provider defined.\");\n          }\n\n          try {\n            const RelayState =\n              (req.query && req.query.RelayState) || (req.body && req.body.RelayState);\n            this.redirect(\n              await this._saml.getLogoutUrlAsync(req.user as Profile, RelayState, options)\n            );\n          } catch (err) {\n            this.error(err as Error);\n          }\n        },\n      }[options.samlFallback];\n\n      if (typeof requestHandler !== \"function\") {\n        return this.fail(401);\n      }\n\n      requestHandler();\n    }\n  }\n\n  logout(req: RequestWithUser, callback: (err: Error | null, url?: string | null) => void): void {\n    if (this._saml == null) {\n      throw new Error(\"Can't logout without a SAML provider defined.\");\n    }\n    const RelayState = (req.query && req.query.RelayState) || (req.body && req.body.RelayState);\n    this._saml\n      .getLogoutUrlAsync(req.user as Profile, RelayState, {})\n      .then((url) => callback(null, url))\n      .catch((err) => callback(err));\n  }\n\n  protected _generateServiceProviderMetadata(\n    decryptionCert: string | null,\n    signingCert?: string | null\n  ): string {\n    if (this._saml == null) {\n      throw new Error(\"Can't generate service provider metadata without a SAML provider defined.\");\n    }\n\n    return this._saml.generateServiceProviderMetadata(decryptionCert, signingCert);\n  }\n\n  // This is reduntant, but helps with testing\n  error(err: Error): void {\n    super.error(err);\n  }\n}\n\nexport class Strategy extends AbstractStrategy {\n  static readonly newSamlProviderOnConstruct = true;\n\n  generateServiceProviderMetadata(\n    decryptionCert: string | null,\n    signingCert?: string | null\n  ): string {\n    return this._generateServiceProviderMetadata(decryptionCert, signingCert);\n  }\n}\n"]}