{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/node-saml/types.ts"], "names": [], "mappings": ";;;AAWO,MAAM,yBAAyB,GAAG,CACvC,OAAoC,EACL,EAAE;IACjC,OAAO,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC;AACpC,CAAC,CAAC;AAJW,QAAA,yBAAyB,6BAIpC", "sourcesContent": ["import type { CacheProvider } from \"./inmemory-cache-provider\";\n\nexport type SignatureAlgorithm = \"sha1\" | \"sha256\" | \"sha512\";\n\nexport interface SamlSigningOptions {\n  privateKey: string | Buffer;\n  signatureAlgorithm?: SignatureAlgorithm;\n  xmlSignatureTransforms?: string[];\n  digestAlgorithm?: string;\n}\n\nexport const isValidSamlSigningOptions = (\n  options: Partial<SamlSigningOptions>\n): options is SamlSigningOptions => {\n  return options.privateKey != null;\n};\n\nexport interface AudienceRestrictionXML {\n  Audience?: XMLObject[];\n}\n\nexport type XMLValue = string | number | boolean | null | XMLObject | XMLValue[];\n\nexport type XMLObject = {\n  [key: string]: XMLValue;\n};\n\nexport type XMLInput = XMLObject;\n\nexport type XMLOutput = Record<string, any>;\n\nexport interface AuthorizeRequestXML {\n  \"samlp:AuthnRequest\": XMLInput;\n}\n\nexport type CertCallback = (\n  callback: (err: Error | null, cert?: string | string[]) => void\n) => void;\n\n/**\n * These are SAML options that must be provided to construct a new SAML Strategy\n */\nexport interface MandatorySamlOptions {\n  cert: string | string[] | CertCallback;\n}\n\nexport interface SamlIDPListConfig {\n  entries: SamlIDPEntryConfig[];\n  getComplete?: string;\n}\n\nexport interface SamlIDPEntryConfig {\n  providerId: string;\n  name?: string;\n  loc?: string;\n}\n\nexport interface LogoutRequestXML {\n  \"samlp:LogoutRequest\": {\n    \"saml:NameID\": XMLInput;\n    [key: string]: XMLValue;\n  };\n}\n\nexport interface ServiceMetadataXML {\n  EntityDescriptor: {\n    [key: string]: XMLValue;\n    SPSSODescriptor: XMLObject;\n  };\n}\n\nexport type RacComparision = \"exact\" | \"minimum\" | \"maximum\" | \"better\";\n\ninterface SamlScopingConfig {\n  idpList?: SamlIDPListConfig[];\n  proxyCount?: number;\n  requesterId?: string[] | string;\n}\n\n/**\n * The options required to use a SAML strategy\n * These may be provided by means of defaults specified in the constructor\n */\nexport interface SamlOptions extends Partial<SamlSigningOptions>, MandatorySamlOptions {\n  // Core\n  callbackUrl?: string;\n  path: string;\n  protocol?: string;\n  host: string;\n  entryPoint?: string;\n  issuer: string;\n  decryptionPvk?: string | Buffer;\n\n  // Additional SAML behaviors\n  additionalParams: Record<string, string>;\n  additionalAuthorizeParams: Record<string, string>;\n  identifierFormat?: string | null;\n  acceptedClockSkewMs: number;\n  attributeConsumingServiceIndex?: string;\n  disableRequestedAuthnContext: boolean;\n  authnContext: string[];\n  forceAuthn: boolean;\n  skipRequestCompression: boolean;\n  authnRequestBinding?: string;\n  racComparison: RacComparision;\n  providerName?: string;\n  passive: boolean;\n  idpIssuer?: string;\n  audience?: string;\n  scoping?: SamlScopingConfig;\n  wantAssertionsSigned?: boolean;\n  maxAssertionAgeMs: number;\n\n  // InResponseTo Validation\n  validateInResponseTo: boolean;\n  requestIdExpirationPeriodMs: number;\n  cacheProvider: CacheProvider;\n\n  // Logout\n  logoutUrl: string;\n  additionalLogoutParams: Record<string, string>;\n  logoutCallbackUrl?: string;\n\n  // extras\n  disableRequestAcsUrl: boolean;\n}\n"]}