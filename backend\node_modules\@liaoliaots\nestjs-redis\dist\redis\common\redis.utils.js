"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createClient = void 0;
const ioredis_1 = require("ioredis");
const messages_1 = require("../../messages");
const redis_logger_1 = require("../redis-logger");
const utils_1 = require("../../utils");
const redis_constants_1 = require("../redis.constants");
const createClient = ({ namespace, url, path, onClientCreated, ...redisOptions }, { readyLog, errorLog }) => {
    let client;
    if (url)
        client = new ioredis_1.Redis(url, redisOptions);
    else if (path)
        client = new ioredis_1.Redis(path, redisOptions);
    else
        client = new ioredis_1.Redis(redisOptions);
    Reflect.defineProperty(client, redis_constants_1.NAMESPACE_KEY, {
        value: namespace ?? redis_constants_1.DEFAULT_REDIS,
        writable: false,
        enumerable: false,
        configurable: false
    });
    if (readyLog) {
        client.on('ready', () => {
            redis_logger_1.logger.log((0, messages_1.READY_LOG)((0, utils_1.parseNamespace)((0, utils_1.get)(client, redis_constants_1.NAMESPACE_KEY))));
        });
    }
    if (errorLog) {
        client.on('error', (error) => {
            redis_logger_1.logger.error((0, messages_1.ERROR_LOG)((0, utils_1.parseNamespace)((0, utils_1.get)(client, redis_constants_1.NAMESPACE_KEY)), error.message), error.stack);
        });
    }
    if (onClientCreated)
        onClientCreated(client);
    return client;
};
exports.createClient = createClient;
