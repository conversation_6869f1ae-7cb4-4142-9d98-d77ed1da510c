import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { IncidentRepository } from '../../../domain/interfaces/repositories/incident.repository.interface';
import { Incident, IncidentStatus, IncidentPriority } from '../../../domain/entities/incident/incident.entity';
import { ThreatSeverity } from '../../../domain/enums/threat-severity.enum';

/**
 * Get Incidents Query
 */
export class GetIncidentsQuery {
  constructor(
    public readonly criteria: {
      incidentIds?: string[];
      statuses?: IncidentStatus[];
      priorities?: IncidentPriority[];
      severities?: ThreatSeverity[];
      categories?: string[];
      types?: string[];
      detectionDateRange?: {
        from: Date;
        to: Date;
      };
      resolutionDateRange?: {
        from: Date;
        to: Date;
      };
      incidentCommander?: string;
      leadInvestigator?: string;
      teamMember?: string;
      affectedAssetIds?: string[];
      relatedEventIds?: string[];
      relatedThreatIds?: string[];
      relatedVulnerabilityIds?: string[];
      tags?: string[];
      searchText?: string;
      slaStatus?: 'compliant' | 'at_risk' | 'breached';
      hasEvidence?: boolean;
      complianceRequirements?: string[];
      businessImpactLevel?: ('low' | 'medium' | 'high' | 'critical')[];
      pagination?: {
        page: number;
        limit: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
      };
    }
  ) {}
}

/**
 * Get Incident By ID Query
 */
export class GetIncidentByIdQuery {
  constructor(public readonly incidentId: string) {}
}

/**
 * Get Active Incidents Query
 */
export class GetActiveIncidentsQuery {
  constructor(
    public readonly assignedTo?: string,
    public readonly limit: number = 50
  ) {}
}

/**
 * Get Incident Statistics Query
 */
export class GetIncidentStatisticsQuery {
  constructor(
    public readonly dateRange?: {
      from: Date;
      to: Date;
    }
  ) {}
}

/**
 * Get Incident Performance Metrics Query
 */
export class GetIncidentPerformanceMetricsQuery {
  constructor(
    public readonly dateRange?: {
      from: Date;
      to: Date;
    }
  ) {}
}

/**
 * Get Incident Timeline Query
 */
export class GetIncidentTimelineQuery {
  constructor(public readonly incidentId: string) {}
}

/**
 * Get Team Performance Query
 */
export class GetTeamPerformanceQuery {
  constructor(
    public readonly teamMemberId?: string,
    public readonly dateRange?: {
      from: Date;
      to: Date;
    }
  ) {}
}

/**
 * Get Incidents Query Handler
 */
@QueryHandler(GetIncidentsQuery)
export class GetIncidentsQueryHandler implements IQueryHandler<GetIncidentsQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetIncidentsQuery): Promise<{
    incidents: Incident[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }> {
    try {
      const result = await this.incidentRepository.findByCriteria(query.criteria);

      return {
        incidents: result.incidents,
        total: result.total,
        page: result.page,
        limit: result.limit,
        hasMore: result.page * result.limit < result.total,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve incidents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Get Incident By ID Query Handler
 */
@QueryHandler(GetIncidentByIdQuery)
export class GetIncidentByIdQueryHandler implements IQueryHandler<GetIncidentByIdQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetIncidentByIdQuery): Promise<Incident | null> {
    try {
      const result = await this.incidentRepository.findByCriteria({
        pagination: { page: 1, limit: 1 }
      });
      return result.incidents.find(incident => incident.id.toString() === query.incidentId) || null;
    } catch (error) {
      throw new Error(`Failed to retrieve incident ${query.incidentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Get Active Incidents Query Handler
 */
@QueryHandler(GetActiveIncidentsQuery)
export class GetActiveIncidentsQueryHandler implements IQueryHandler<GetActiveIncidentsQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetActiveIncidentsQuery): Promise<{
    incidents: Incident[];
    summary: {
      totalActive: number;
      critical: number;
      high: number;
      slaAtRisk: number;
      overdue: number;
      unassigned: number;
    };
  }> {
    try {
      let incidents = await this.incidentRepository.findActiveIncidents();

      if (query.assignedTo) {
        incidents = incidents.filter(incident =>
          incident.responseTeam?.incidentCommander === query.assignedTo ||
          incident.responseTeam?.leadInvestigator === query.assignedTo ||
          incident.responseTeam?.members?.some(member => member.userId === query.assignedTo)
        );
      }

      const limitedIncidents = incidents.slice(0, query.limit);

      const summary = {
        totalActive: incidents.length,
        critical: incidents.filter(i => i.priority === IncidentPriority.CRITICAL || i.priority === IncidentPriority.EMERGENCY).length,
        high: incidents.filter(i => i.priority === IncidentPriority.HIGH).length,
        slaAtRisk: incidents.filter(i => this.isSLAAtRisk(i)).length,
        overdue: incidents.filter(i => this.isOverdue(i)).length,
        unassigned: incidents.filter(i => !i.responseTeam.incidentCommander).length,
      };

      return {
        incidents: limitedIncidents,
        summary,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve active incidents: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private isSLAAtRisk(incident: Incident): boolean {
    // Simple SLA check - would be more sophisticated in real implementation
    const age = incident.getAge();
    const slaThreshold = this.getSLAThreshold(incident.priority);
    return age > slaThreshold * 0.8; // 80% of SLA time
  }

  private isOverdue(incident: Incident): boolean {
    const age = incident.getAge();
    const slaThreshold = this.getSLAThreshold(incident.priority);
    return age > slaThreshold;
  }

  private getSLAThreshold(priority: IncidentPriority): number {
    switch (priority) {
      case IncidentPriority.EMERGENCY: return 1; // 1 hour
      case IncidentPriority.CRITICAL: return 4; // 4 hours
      case IncidentPriority.HIGH: return 24; // 24 hours
      case IncidentPriority.MEDIUM: return 72; // 72 hours
      case IncidentPriority.LOW: return 168; // 1 week
      default: return 72;
    }
  }
}

/**
 * Get Incident Statistics Query Handler
 */
@QueryHandler(GetIncidentStatisticsQuery)
export class GetIncidentStatisticsQueryHandler implements IQueryHandler<GetIncidentStatisticsQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetIncidentStatisticsQuery): Promise<{
    total: number;
    statusDistribution: Record<IncidentStatus, number>;
    priorityDistribution: Record<IncidentPriority, number>;
    severityDistribution: Record<ThreatSeverity, number>;
    responseMetrics: {
      averageResponseTime: number;
      averageContainmentTime: number;
      averageResolutionTime: number;
      slaComplianceRate: number;
    };
    businessImpact: {
      totalFinancialImpact: number;
      averageDowntime: number;
      customersAffected: number;
      reputationImpact: number;
    };
    teamPerformance: {
      averageTeamSize: number;
      commanderAssignmentRate: number;
      escalationRate: number;
      lessonsLearnedRate: number;
    };
    trends: {
      daily: Array<{ date: string; count: number; avgSeverity: number }>;
      weekly: Array<{ week: string; count: number; avgSeverity: number }>;
      monthly: Array<{ month: string; count: number; avgSeverity: number }>;
    };
  }> {
    try {
      return await this.incidentRepository.getStatistics(query.dateRange);
    } catch (error) {
      throw new Error(`Failed to retrieve incident statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Get Incident Performance Metrics Query Handler
 */
@QueryHandler(GetIncidentPerformanceMetricsQuery)
export class GetIncidentPerformanceMetricsQueryHandler implements IQueryHandler<GetIncidentPerformanceMetricsQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetIncidentPerformanceMetricsQuery): Promise<{
    responseTime: {
      average: number;
      median: number;
      p95: number;
      p99: number;
      byPriority: Record<IncidentPriority, number>;
      bySeverity: Record<ThreatSeverity, number>;
    };
    containmentTime: {
      average: number;
      median: number;
      p95: number;
      p99: number;
      byPriority: Record<IncidentPriority, number>;
      bySeverity: Record<ThreatSeverity, number>;
    };
    resolutionTime: {
      average: number;
      median: number;
      p95: number;
      p99: number;
      byPriority: Record<IncidentPriority, number>;
      bySeverity: Record<ThreatSeverity, number>;
    };
    slaCompliance: {
      overall: number;
      byPriority: Record<IncidentPriority, number>;
      bySeverity: Record<ThreatSeverity, number>;
      breaches: number;
      atRisk: number;
    };
  }> {
    try {
      return await this.incidentRepository.getPerformanceMetrics(query.dateRange);
    } catch (error) {
      throw new Error(`Failed to retrieve incident performance metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Get Incident Timeline Query Handler
 */
@QueryHandler(GetIncidentTimelineQuery)
export class GetIncidentTimelineQueryHandler implements IQueryHandler<GetIncidentTimelineQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetIncidentTimelineQuery): Promise<Array<{
    timestamp: Date;
    event: string;
    description: string;
    performedBy: string;
    category: 'status_change' | 'assignment' | 'evidence' | 'communication' | 'escalation';
    metadata?: Record<string, any>;
  }>> {
    try {
      return await this.incidentRepository.getIncidentTimeline(query.incidentId);
    } catch (error) {
      throw new Error(`Failed to retrieve incident timeline: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

/**
 * Get Team Performance Query Handler
 */
@QueryHandler(GetTeamPerformanceQuery)
export class GetTeamPerformanceQueryHandler implements IQueryHandler<GetTeamPerformanceQuery> {
  constructor(
    private readonly incidentRepository: IncidentRepository
  ) {}

  async execute(query: GetTeamPerformanceQuery): Promise<{
    incidentsHandled: number;
    averageResponseTime: number;
    averageResolutionTime: number;
    slaComplianceRate: number;
    escalationRate: number;
    commanderAssignments: number;
    lessonsLearnedCompleted: number;
    performanceRating: 'excellent' | 'good' | 'average' | 'needs_improvement';
    recommendations: string[];
  }> {
    try {
      const metrics = await this.incidentRepository.getTeamPerformanceMetrics(
        query.teamMemberId,
        query.dateRange
      );

      const recommendations = this.generatePerformanceRecommendations(metrics);

      return {
        ...metrics,
        recommendations,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve team performance: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generatePerformanceRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.slaComplianceRate < 0.8) {
      recommendations.push('Focus on improving SLA compliance through better time management');
    }

    if (metrics.escalationRate > 0.3) {
      recommendations.push('Review escalation patterns and consider additional training');
    }

    if (metrics.averageResponseTime > 2) {
      recommendations.push('Work on reducing initial response time');
    }

    if (metrics.lessonsLearnedCompleted < metrics.incidentsHandled * 0.5) {
      recommendations.push('Increase participation in lessons learned sessions');
    }

    if (recommendations.length === 0) {
      recommendations.push('Continue excellent performance and mentor junior team members');
    }

    return recommendations;
  }
}

/**
 * Incident Query Handlers Export
 */
export const IncidentQueryHandlers = [
  GetIncidentsQueryHandler,
  GetIncidentByIdQueryHandler,
  GetActiveIncidentsQueryHandler,
  GetIncidentStatisticsQueryHandler,
  GetIncidentPerformanceMetricsQueryHandler,
  GetIncidentTimelineQueryHandler,
  GetTeamPerformanceQueryHandler,
];

/**
 * Incident Queries Export
 */
export const IncidentQueries = [
  GetIncidentsQuery,
  GetIncidentByIdQuery,
  GetActiveIncidentsQuery,
  GetIncidentStatisticsQuery,
  GetIncidentPerformanceMetricsQuery,
  GetIncidentTimelineQuery,
  GetTeamPerformanceQuery,
];
